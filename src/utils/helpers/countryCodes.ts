export interface ICountryCode {
    country: string;
    value: string;
    iso: string;
    label: string;
    phoneLength: number;
}

// Deduplicated countryCodesOptions array (no duplicate 'value' entries)
export const countryCodes: ICountryCode[] = [
    { country: 'Afghanistan', value: '+93', iso: 'AF', label: '+93-AF (Afghanistan)', phoneLength: 9 },
    { country: 'Albania', value: '+355', iso: 'AL', label: '+355-AL (Albania)', phoneLength: 9 },
    { country: 'Algeria', value: '+213', iso: 'DZ', label: '+213-DZ (Algeria)', phoneLength: 9 },
    { country: 'American Samoa', value: '******', iso: 'AS', label: '******-AS (American Samoa)', phoneLength: 7 },
    { country: 'Andorra', value: '+376', iso: 'AD', label: '+376-AD (Andorra)', phoneLength: 6 },
    { country: 'Angola', value: '+244', iso: 'AO', label: '+244-AO (Angola)', phoneLength: 9 },
    { country: 'Anguilla', value: '******', iso: 'AI', label: '******-AI (Anguilla)', phoneLength: 10 },
    { country: 'Antarctica', value: '+672', iso: 'AQ', label: '+672-AQ (Antarctica)', phoneLength: 10 },
    {
        country: 'Antigua and Barbuda',
        value: '******',
        iso: 'AG',
        label: '******-AG (Antigua and Barbuda)',
        phoneLength: 10,
    },
    { country: 'Argentina', value: '+54', iso: 'AR', label: '+54-AR (Argentina)', phoneLength: 10 },
    { country: 'Armenia', value: '+374', iso: 'AM', label: '+374-AM (Armenia)', phoneLength: 8 },
    { country: 'Aruba', value: '+297', iso: 'AW', label: '+297-AW (Aruba)', phoneLength: 7 },
    { country: 'Australia', value: '+61', iso: 'AU', label: '+61-AU (Australia)', phoneLength: 9 },
    { country: 'Austria', value: '+43', iso: 'AT', label: '+43-AT (Austria)', phoneLength: 10 },
    { country: 'Azerbaijan', value: '+994', iso: 'AZ', label: '+994-AZ (Azerbaijan)', phoneLength: 9 },
    { country: 'Bahamas', value: '******', iso: 'BS', label: '******-BS (Bahamas)', phoneLength: 10 },
    { country: 'Bahrain', value: '+973', iso: 'BH', label: '+973-BH (Bahrain)', phoneLength: 8 },
    { country: 'Bangladesh', value: '+880', iso: 'BD', label: '+880-BD (Bangladesh)', phoneLength: 10 },
    { country: 'Barbados', value: '******', iso: 'BB', label: '******-BB (Barbados)', phoneLength: 10 },
    { country: 'Belarus', value: '+375', iso: 'BY', label: '+375-BY (Belarus)', phoneLength: 9 },
    { country: 'Belgium', value: '+32', iso: 'BE', label: '+32-BE (Belgium)', phoneLength: 9 },
    { country: 'Belize', value: '+501', iso: 'BZ', label: '+501-BZ (Belize)', phoneLength: 7 },
    { country: 'Benin', value: '+229', iso: 'BJ', label: '+229-BJ (Benin)', phoneLength: 8 },
    { country: 'Bermuda', value: '******', iso: 'BM', label: '******-BM (Bermuda)', phoneLength: 10 },
    { country: 'Bhutan', value: '+975', iso: 'BT', label: '+975-BT (Bhutan)', phoneLength: 8 },
    { country: 'Bolivia', value: '+591', iso: 'BO', label: '+591-BO (Bolivia)', phoneLength: 8 },
    {
        country: 'Bosnia and Herzegovina',
        value: '+387',
        iso: 'BA',
        label: '+387-BA (Bosnia and Herzegovina)',
        phoneLength: 8,
    },
    { country: 'Botswana', value: '+267', iso: 'BW', label: '+267-BW (Botswana)', phoneLength: 8 },
    { country: 'Brazil', value: '+55', iso: 'BR', label: '+55-BR (Brazil)', phoneLength: 10 },
    {
        country: 'British Indian Ocean Territory',
        value: '+246',
        iso: 'IO',
        label: '+246-IO (British Indian Ocean Territory)',
        phoneLength: 7,
    },
    {
        country: 'British Virgin Islands',
        value: '******',
        iso: 'VG',
        label: '******-VG (British Virgin Islands)',
        phoneLength: 10,
    },
    { country: 'Brunei', value: '+673', iso: 'BN', label: '+673-BN (Brunei)', phoneLength: 7 },
    { country: 'Bulgaria', value: '+359', iso: 'BG', label: '+359-BG (Bulgaria)', phoneLength: 8 },
    { country: 'Burkina Faso', value: '+226', iso: 'BF', label: '+226-BF (Burkina Faso)', phoneLength: 8 },
    { country: 'Burundi', value: '+257', iso: 'BI', label: '+257-BI (Burundi)', phoneLength: 8 },
    { country: 'Cambodia', value: '+855', iso: 'KH', label: '+855-KH (Cambodia)', phoneLength: 9 },
    { country: 'Cameroon', value: '+237', iso: 'CM', label: '+237-CM (Cameroon)', phoneLength: 9 },
    { country: 'Canada', value: '+1', iso: 'CA', label: '+1-CA (Canada)', phoneLength: 10 },
    { country: 'Cape Verde', value: '+238', iso: 'CV', label: '+238-CV (Cape Verde)', phoneLength: 7 },
    { country: 'Cayman Islands', value: '******', iso: 'KY', label: '******-KY (Cayman Islands)', phoneLength: 7 },
    {
        country: 'Central African Republic',
        value: '+236',
        iso: 'CF',
        label: '+236-CF (Central African Republic)',
        phoneLength: 9,
    },
    { country: 'Chad', value: '+235', iso: 'TD', label: '+235-TD (Chad)', phoneLength: 9 },
    { country: 'Chile', value: '+56', iso: 'CL', label: '+56-CL (Chile)', phoneLength: 9 },
    { country: 'China', value: '+86', iso: 'CN', label: '+86-CN (China)', phoneLength: 11 },
    { country: 'Christmas Island', value: '+61', iso: 'CX', label: '+61-CX (Christmas Island)', phoneLength: 10 },
    { country: 'Cocos Islands', value: '+61', iso: 'CC', label: '+61-CC (Cocos Islands)', phoneLength: 10 },
    { country: 'Colombia', value: '+57', iso: 'CO', label: '+57-CO (Colombia)', phoneLength: 9 },
    { country: 'Comoros', value: '+269', iso: 'KM', label: '+269-KM (Comoros)', phoneLength: 7 },
    { country: 'Cook Islands', value: '+682', iso: 'CK', label: '+682-CK (Cook Islands)', phoneLength: 7 },
    { country: 'Costa Rica', value: '+506', iso: 'CR', label: '+506-CR (Costa Rica)', phoneLength: 8 },
    { country: 'Croatia', value: '+385', iso: 'HR', label: '+385-HR (Croatia)', phoneLength: 9 },
    { country: 'Cuba', value: '+53', iso: 'CU', label: '+53-CU (Cuba)', phoneLength: 7 },
    { country: 'Curacao', value: '+599', iso: 'CW', label: '+599-CW (Curacao)', phoneLength: 7 },
    { country: 'Cyprus', value: '+357', iso: 'CY', label: '+357-CY (Cyprus)', phoneLength: 8 },
    { country: 'Czech Republic', value: '+420', iso: 'CZ', label: '+420-CZ (Czech Republic)', phoneLength: 9 },
    {
        country: 'Democratic Republic of the Congo',
        value: '+243',
        iso: 'CD',
        label: '+243-CD (Democratic Republic of the Congo)',
        phoneLength: 9,
    },
    { country: 'Denmark', value: '+45', iso: 'DK', label: '+45-DK (Denmark)', phoneLength: 9 },
    { country: 'Djibouti', value: '+253', iso: 'DJ', label: '+253-DJ (Djibouti)', phoneLength: 8 },
    { country: 'Dominica', value: '******', iso: 'DM', label: '******-DM (Dominica)', phoneLength: 9 },
    {
        country: 'Dominican Republic',
        value: '******, 1-829, 1-849',
        iso: 'DO',
        label: '******-DO (Dominican Republic)',
        phoneLength: 10,
    },
    { country: 'East Timor', value: '+670', iso: 'TL', label: '+670-TL (East Timor)', phoneLength: 9 },
    { country: 'Ecuador', value: '+593', iso: 'EC', label: '+593-EC (Ecuador)', phoneLength: 9 },
    { country: 'Egypt', value: '+20', iso: 'EG', label: '+20-EG (Egypt)', phoneLength: 9 },
    { country: 'El Salvador', value: '+503', iso: 'SV', label: '+503-SV (El Salvador)', phoneLength: 9 },
    { country: 'Equatorial Guinea', value: '+240', iso: 'GQ', label: '+240-GQ (Equatorial Guinea)', phoneLength: 9 },
    { country: 'Eritrea', value: '+291', iso: 'ER', label: '+291-ER (Eritrea)', phoneLength: 8 },
    { country: 'Estonia', value: '+372', iso: 'EE', label: '+372-EE (Estonia)', phoneLength: 9 },
    { country: 'Ethiopia', value: '+251', iso: 'ET', label: '+251-ET (Ethiopia)', phoneLength: 9 },
    { country: 'Falkland Islands', value: '+500', iso: 'FK', label: '+500-FK (Falkland Islands)', phoneLength: 7 },
    { country: 'Faroe Islands', value: '+298', iso: 'FO', label: '+298-FO (Faroe Islands)', phoneLength: 7 },
    { country: 'Fiji', value: '+679', iso: 'FJ', label: '+679-FJ (Fiji)', phoneLength: 8 },
    { country: 'Finland', value: '+358', iso: 'FI', label: '+358-FI (Finland)', phoneLength: 9 },
    { country: 'France', value: '+33', iso: 'FR', label: '+33-FR (France)', phoneLength: 9 },
    { country: 'French Polynesia', value: '+689', iso: 'PF', label: '+689-PF (French Polynesia)', phoneLength: 7 },
    { country: 'Gabon', value: '+241', iso: 'GA', label: '+241-GA (Gabon)', phoneLength: 8 },
    { country: 'Gambia', value: '+220', iso: 'GM', label: '+220-GM (Gambia)', phoneLength: 9 },
    { country: 'Georgia', value: '+995', iso: 'GE', label: '+995-GE (Georgia)', phoneLength: 9 },
    { country: 'Germany', value: '+49', iso: 'DE', label: '+49-DE (Germany)', phoneLength: 11 },
    { country: 'Ghana', value: '+233', iso: 'GH', label: '+233-GH (Ghana)', phoneLength: 9 },
    { country: 'Gibraltar', value: '+350', iso: 'GI', label: '+350-GI (Gibraltar)', phoneLength: 7 },
    { country: 'Greece', value: '+30', iso: 'GR', label: '+30-GR (Greece)', phoneLength: 9 },
    { country: 'Greenland', value: '+299', iso: 'GL', label: '+299-GL (Greenland)', phoneLength: 7 },
    { country: 'Grenada', value: '******', iso: 'GD', label: '******-GD (Grenada)', phoneLength: 9 },
    { country: 'Guam', value: '******', iso: 'GU', label: '******-GU (Guam)', phoneLength: 7 },
    { country: 'Guatemala', value: '+502', iso: 'GT', label: '+502-GT (Guatemala)', phoneLength: 9 },
    { country: 'Guernsey', value: '+44-1481', iso: 'GG', label: '+44-1481-GG (Guernsey)', phoneLength: 10 },
    { country: 'Guinea', value: '+224', iso: 'GN', label: '+224-GN (Guinea)', phoneLength: 9 },
    { country: 'Guinea-Bissau', value: '+245', iso: 'GW', label: '+245-GW (Guinea-Bissau)', phoneLength: 9 },
    { country: 'Guyana', value: '+592', iso: 'GY', label: '+592-GY (Guyana)', phoneLength: 9 },
    { country: 'Haiti', value: '+509', iso: 'HT', label: '+509-HT (Haiti)', phoneLength: 9 },
    { country: 'Honduras', value: '+504', iso: 'HN', label: '+504-HN (Honduras)', phoneLength: 9 },
    { country: 'Hong Kong', value: '+852', iso: 'HK', label: '+852-HK (Hong Kong)', phoneLength: 9 },
    { country: 'Hungary', value: '+36', iso: 'HU', label: '+36-HU (Hungary)', phoneLength: 9 },
    { country: 'Iceland', value: '+354', iso: 'IS', label: '+354-IS (Iceland)', phoneLength: 8 },
    { country: 'India', value: '+91', iso: 'IN', label: '+91-IN (India)', phoneLength: 10 },
    { country: 'Indonesia', value: '+62', iso: 'ID', label: '+62-ID (Indonesia)', phoneLength: 9 },
    { country: 'Iran', value: '+98', iso: 'IR', label: '+98-IR (Iran)', phoneLength: 9 },
    { country: 'Iraq', value: '+964', iso: 'IQ', label: '+964-IQ (Iraq)', phoneLength: 9 },
    { country: 'Ireland', value: '+353', iso: 'IE', label: '+353-IE (Ireland)', phoneLength: 9 },
    { country: 'Isle of Man', value: '+44-1624', iso: 'IM', label: '+44-1624-IM (Isle of Man)', phoneLength: 10 },
    { country: 'Israel', value: '+972', iso: 'IL', label: '+972-IL (Israel)', phoneLength: 9 },
    { country: 'Italy', value: '+39', iso: 'IT', label: '+39-IT (Italy)', phoneLength: 9 },
    { country: 'Ivory Coast', value: '+225', iso: 'CI', label: '+225-CI (Ivory Coast)', phoneLength: 9 },
    { country: 'Jamaica', value: '******', iso: 'JM', label: '******-JM (Jamaica)', phoneLength: 9 },
    { country: 'Japan', value: '+81', iso: 'JP', label: '+81-JP (Japan)', phoneLength: 10 },
    { country: 'Jersey', value: '+44-1534', iso: 'JE', label: '+44-1534-JE (Jersey)', phoneLength: 10 },
    { country: 'Jordan', value: '+962', iso: 'JO', label: '+962-JO (Jordan)', phoneLength: 9 },
    { country: 'Kazakhstan', value: '+7', iso: 'KZ', label: '+7-KZ (Kazakhstan)', phoneLength: 9 },
    { country: 'Kenya', value: '+254', iso: 'KE', label: '+254-KE (Kenya)', phoneLength: 9 },
    { country: 'Kiribati', value: '+686', iso: 'KI', label: '+686-KI (Kiribati)', phoneLength: 7 },
    { country: 'Kosovo', value: '+383', iso: 'XK', label: '+383-XK (Kosovo)', phoneLength: 9 },
    { country: 'Kuwait', value: '+965', iso: 'KW', label: '+965-KW (Kuwait)', phoneLength: 8 },
    { country: 'Kyrgyzstan', value: '+996', iso: 'KG', label: '+996-KG (Kyrgyzstan)', phoneLength: 9 },
    { country: 'Laos', value: '+856', iso: 'LA', label: '+856-LA (Laos)', phoneLength: 9 },
    { country: 'Latvia', value: '+371', iso: 'LV', label: '+371-LV (Latvia)', phoneLength: 9 },
    { country: 'Lebanon', value: '+961', iso: 'LB', label: '+961-LB (Lebanon)', phoneLength: 9 },
    { country: 'Lesotho', value: '+266', iso: 'LS', label: '+266-LS (Lesotho)', phoneLength: 9 },
    { country: 'Liberia', value: '+231', iso: 'LR', label: '+231-LR (Liberia)', phoneLength: 9 },
    { country: 'Libya', value: '+218', iso: 'LY', label: '+218-LY (Libya)', phoneLength: 9 },
    { country: 'Liechtenstein', value: '+423', iso: 'LI', label: '+423-LI (Liechtenstein)', phoneLength: 8 },
    { country: 'Lithuania', value: '+370', iso: 'LT', label: '+370-LT (Lithuania)', phoneLength: 9 },
    { country: 'Luxembourg', value: '+352', iso: 'LU', label: '+352-LU (Luxembourg)', phoneLength: 8 },
    { country: 'Macao', value: '+853', iso: 'MO', label: '+853-MO (Macao)', phoneLength: 8 },
    { country: 'Macedonia', value: '+389', iso: 'MK', label: '+389-MK (Macedonia)', phoneLength: 9 },
    { country: 'Madagascar', value: '+261', iso: 'MG', label: '+261-MG (Madagascar)', phoneLength: 9 },
    { country: 'Malawi', value: '+265', iso: 'MW', label: '+265-MW (Malawi)', phoneLength: 9 },
    { country: 'Malaysia', value: '+60', iso: 'MY', label: '+60-MY (Malaysia)', phoneLength: 9 },
    { country: 'Maldives', value: '+960', iso: 'MV', label: '+960-MV (Maldives)', phoneLength: 9 },
    { country: 'Mali', value: '+223', iso: 'ML', label: '+223-ML (Mali)', phoneLength: 9 },
    { country: 'Malta', value: '+356', iso: 'MT', label: '+356-MT (Malta)', phoneLength: 8 },
    { country: 'Marshall Islands', value: '+692', iso: 'MH', label: '+692-MH (Marshall Islands)', phoneLength: 7 },
    { country: 'Mauritania', value: '+222', iso: 'MR', label: '+222-MR (Mauritania)', phoneLength: 9 },
    { country: 'Mauritius', value: '+230', iso: 'MU', label: '+230-MU (Mauritius)', phoneLength: 9 },
    { country: 'Mayotte', value: '+262', iso: 'YT', label: '+262-YT (Mayotte)', phoneLength: 9 },
    { country: 'Mexico', value: '+52', iso: 'MX', label: '+52-MX (Mexico)', phoneLength: 9 },
    { country: 'Micronesia', value: '+691', iso: 'FM', label: '+691-FM (Micronesia)', phoneLength: 7 },
    { country: 'Moldova', value: '+373', iso: 'MD', label: '+373-MD (Moldova)', phoneLength: 9 },
    { country: 'Monaco', value: '+377', iso: 'MC', label: '+377-MC (Monaco)', phoneLength: 8 },
    { country: 'Mongolia', value: '+976', iso: 'MN', label: '+976-MN (Mongolia)', phoneLength: 9 },
    { country: 'Montenegro', value: '+382', iso: 'ME', label: '+382-ME (Montenegro)', phoneLength: 9 },
    { country: 'Montserrat', value: '******', iso: 'MS', label: '******-MS (Montserrat)', phoneLength: 9 },
    { country: 'Morocco', value: '+212', iso: 'MA', label: '+212-MA (Morocco)', phoneLength: 9 },
    { country: 'Mozambique', value: '+258', iso: 'MZ', label: '+258-MZ (Mozambique)', phoneLength: 9 },
    { country: 'Myanmar', value: '+95', iso: 'MM', label: '+95-MM (Myanmar)', phoneLength: 9 },
    { country: 'Namibia', value: '+264', iso: 'NA', label: '+264-NA (Namibia)', phoneLength: 9 },
    { country: 'Nauru', value: '+674', iso: 'NR', label: '+674-NR (Nauru)', phoneLength: 7 },
    { country: 'Nepal', value: '+977', iso: 'NP', label: '+977-NP (Nepal)', phoneLength: 9 },
    { country: 'Netherlands', value: '+31', iso: 'NL', label: '+31-NL (Netherlands)', phoneLength: 9 },
    {
        country: 'Netherlands Antilles',
        value: '+599',
        iso: 'AN',
        label: '+599-AN (Netherlands Antilles)',
        phoneLength: 7,
    },
    { country: 'New Caledonia', value: '+687', iso: 'NC', label: '+687-NC (New Caledonia)', phoneLength: 7 },
    { country: 'New Zealand', value: '+64', iso: 'NZ', label: '+64-NZ (New Zealand)', phoneLength: 9 },
    { country: 'Nicaragua', value: '+505', iso: 'NI', label: '+505-NI (Nicaragua)', phoneLength: 9 },
    { country: 'Niger', value: '+227', iso: 'NE', label: '+227-NE (Niger)', phoneLength: 9 },
    { country: 'Nigeria', value: '+234', iso: 'NG', label: '+234-NG (Nigeria)', phoneLength: 10 },
    { country: 'Niue', value: '+683', iso: 'NU', label: '+683-NU (Niue)', phoneLength: 7 },
    { country: 'North Korea', value: '+850', iso: 'KP', label: '+850-KP (North Korea)', phoneLength: 9 },
    {
        country: 'Northern Mariana Islands',
        value: '******',
        iso: 'MP',
        label: '******-MP (Northern Mariana Islands)',
        phoneLength: 9,
    },
    { country: 'Norway', value: '+47', iso: 'NO', label: '+47-NO (Norway)', phoneLength: 9 },
    { country: 'Oman', value: '+968', iso: 'OM', label: '+968-OM (Oman)', phoneLength: 9 },
    { country: 'Pakistan', value: '+92', iso: 'PK', label: '+92-PK (Pakistan)', phoneLength: 9 },
    { country: 'Palau', value: '+680', iso: 'PW', label: '+680-PW (Palau)', phoneLength: 7 },
    { country: 'Palestine', value: '+970', iso: 'PS', label: '+970-PS (Palestine)', phoneLength: 9 },
    { country: 'Panama', value: '+507', iso: 'PA', label: '+507-PA (Panama)', phoneLength: 9 },
    { country: 'Papua New Guinea', value: '+675', iso: 'PG', label: '+675-PG (Papua New Guinea)', phoneLength: 9 },
    { country: 'Paraguay', value: '+595', iso: 'PY', label: '+595-PY (Paraguay)', phoneLength: 9 },
    { country: 'Peru', value: '+51', iso: 'PE', label: '+51-PE (Peru)', phoneLength: 9 },
    { country: 'Philippines', value: '+63', iso: 'PH', label: '+63-PH (Philippines)', phoneLength: 9 },
    { country: 'Pitcairn', value: '+64', iso: 'PN', label: '+64-PN (Pitcairn)', phoneLength: 7 },
    { country: 'Poland', value: '+48', iso: 'PL', label: '+48-PL (Poland)', phoneLength: 9 },
    { country: 'Portugal', value: '+351', iso: 'PT', label: '+351-PT (Portugal)', phoneLength: 9 },
    { country: 'Puerto Rico', value: '******, 1-939', iso: 'PR', label: '******-PR (Puerto Rico)', phoneLength: 10 },
    { country: 'Qatar', value: '+974', iso: 'QA', label: '+974-QA (Qatar)', phoneLength: 9 },
    {
        country: 'Republic of the Congo',
        value: '+242',
        iso: 'CG',
        label: '+242-CG (Republic of the Congo)',
        phoneLength: 9,
    },
    { country: 'Reunion', value: '+262', iso: 'RE', label: '+262-RE (Reunion)', phoneLength: 9 },
    { country: 'Romania', value: '+40', iso: 'RO', label: '+40-RO (Romania)', phoneLength: 9 },
    { country: 'Russia', value: '+7', iso: 'RU', label: '+7-RU (Russia)', phoneLength: 9 },
    { country: 'Rwanda', value: '+250', iso: 'RW', label: '+250-RW (Rwanda)', phoneLength: 9 },
    { country: 'Saint Barthelemy', value: '+590', iso: 'BL', label: '+590-BL (Saint Barthelemy)', phoneLength: 9 },
    { country: 'Saint Helena', value: '+290', iso: 'SH', label: '+290-SH (Saint Helena)', phoneLength: 9 },
    {
        country: 'Saint Kitts and Nevis',
        value: '******',
        iso: 'KN',
        label: '******-KN (Saint Kitts and Nevis)',
        phoneLength: 9,
    },
    { country: 'Saint Lucia', value: '******', iso: 'LC', label: '******-LC (Saint Lucia)', phoneLength: 9 },
    {
        country: 'Saint Pierre and Miquelon',
        value: '+508',
        iso: 'PM',
        label: '+508-PM (Saint Pierre and Miquelon)',
        phoneLength: 9,
    },
    {
        country: 'Saint Vincent and the Grenadines',
        value: '******',
        iso: 'VC',
        label: '******-VC (Saint Vincent and the Grenadines)',
        phoneLength: 9,
    },
    { country: 'Samoa', value: '+685', iso: 'WS', label: '+685-WS (Samoa)', phoneLength: 7 },
    { country: 'San Marino', value: '+378', iso: 'SM', label: '+378-SM (San Marino)', phoneLength: 8 },
    {
        country: 'Sao Tome and Principe',
        value: '+239',
        iso: 'ST',
        label: '+239-ST (Sao Tome and Principe)',
        phoneLength: 9,
    },
    { country: 'Saudi Arabia', value: '+966', iso: 'SA', label: '+966-SA (Saudi Arabia)', phoneLength: 9 },
    { country: 'Senegal', value: '+221', iso: 'SN', label: '+221-SN (Senegal)', phoneLength: 9 },
    { country: 'Serbia', value: '+381', iso: 'RS', label: '+381-RS (Serbia)', phoneLength: 9 },
    { country: 'Seychelles', value: '+248', iso: 'SC', label: '+248-SC (Seychelles)', phoneLength: 9 },
    { country: 'Sierra Leone', value: '+232', iso: 'SL', label: '+232-SL (Sierra Leone)', phoneLength: 9 },
    { country: 'Singapore', value: '+65', iso: 'SG', label: '+65-SG (Singapore)', phoneLength: 9 },
    { country: 'Sint Maarten', value: '******', iso: 'SX', label: '******-SX (Sint Maarten)', phoneLength: 9 },
    { country: 'Slovakia', value: '+421', iso: 'SK', label: '+421-SK (Slovakia)', phoneLength: 9 },
    { country: 'Slovenia', value: '+386', iso: 'SI', label: '+386-SI (Slovenia)', phoneLength: 9 },
    { country: 'Solomon Islands', value: '+677', iso: 'SB', label: '+677-SB (Solomon Islands)', phoneLength: 9 },
    { country: 'Somalia', value: '+252', iso: 'SO', label: '+252-SO (Somalia)', phoneLength: 9 },
    { country: 'South Africa', value: '+27', iso: 'ZA', label: '+27-ZA (South Africa)', phoneLength: 9 },
    { country: 'South Korea', value: '+82', iso: 'KR', label: '+82-KR (South Korea)', phoneLength: 9 },
    { country: 'South Sudan', value: '+211', iso: 'SS', label: '+211-SS (South Sudan)', phoneLength: 9 },
    { country: 'Spain', value: '+34', iso: 'ES', label: '+34-ES (Spain)', phoneLength: 9 },
    { country: 'Sri Lanka', value: '+94', iso: 'LK', label: '+94-LK (Sri Lanka)', phoneLength: 9 },
    { country: 'Sudan', value: '+249', iso: 'SD', label: '+249-SD (Sudan)', phoneLength: 9 },
    { country: 'Suriname', value: '+597', iso: 'SR', label: '+597-SR (Suriname)', phoneLength: 9 },
    {
        country: 'Svalbard and Jan Mayen',
        value: '+47',
        iso: 'SJ',
        label: '+47-SJ (Svalbard and Jan Mayen)',
        phoneLength: 7,
    },
    { country: 'Swaziland', value: '+268', iso: 'SZ', label: '+268-SZ (Swaziland)', phoneLength: 9 },
    { country: 'Sweden', value: '+46', iso: 'SE', label: '+46-SE (Sweden)', phoneLength: 9 },
    { country: 'Switzerland', value: '+41', iso: 'CH', label: '+41-CH (Switzerland)', phoneLength: 9 },
    { country: 'Syria', value: '+963', iso: 'SY', label: '+963-SY (Syria)', phoneLength: 9 },
    { country: 'Taiwan', value: '+886', iso: 'TW', label: '+886-TW (Taiwan)', phoneLength: 9 },
    { country: 'Tajikistan', value: '+992', iso: 'TJ', label: '+992-TJ (Tajikistan)', phoneLength: 9 },
    { country: 'Tanzania', value: '+255', iso: 'TZ', label: '+255-TZ (Tanzania)', phoneLength: 9 },
    { country: 'Thailand', value: '+66', iso: 'TH', label: '+66-TH (Thailand)', phoneLength: 9 },
    { country: 'Togo', value: '+228', iso: 'TG', label: '+228-TG (Togo)', phoneLength: 9 },
    { country: 'Tokelau', value: '+690', iso: 'TK', label: '+690-TK (Tokelau)', phoneLength: 7 },
    { country: 'Tonga', value: '+676', iso: 'TO', label: '+676-TO (Tonga)', phoneLength: 7 },
    {
        country: 'Trinidad and Tobago',
        value: '******',
        iso: 'TT',
        label: '******-TT (Trinidad and Tobago)',
        phoneLength: 9,
    },
    { country: 'Tunisia', value: '+216', iso: 'TN', label: '+216-TN (Tunisia)', phoneLength: 9 },
    { country: 'Turkey', value: '+90', iso: 'TR', label: '+90-TR (Turkey)', phoneLength: 9 },
    { country: 'Turkmenistan', value: '+993', iso: 'TM', label: '+993-TM (Turkmenistan)', phoneLength: 9 },
    {
        country: 'Turks and Caicos Islands',
        value: '******',
        iso: 'TC',
        label: '******-TC (Turks and Caicos Islands)',
        phoneLength: 9,
    },
    { country: 'Tuvalu', value: '+688', iso: 'TV', label: '+688-TV (Tuvalu)', phoneLength: 7 },
    {
        country: 'U.S. Virgin Islands',
        value: '******',
        iso: 'VI',
        label: '******-VI (U.S. Virgin Islands)',
        phoneLength: 9,
    },
    { country: 'Uganda', value: '+256', iso: 'UG', label: '+256-UG (Uganda)', phoneLength: 9 },
    { country: 'Ukraine', value: '+380', iso: 'UA', label: '+380-UA (Ukraine)', phoneLength: 9 },
    {
        country: 'United Arab Emirates',
        value: '+971',
        iso: 'AE',
        label: '+971-AE (United Arab Emirates)',
        phoneLength: 9,
    },
    { country: 'United Kingdom', value: '+44', iso: 'GB', label: '+44-GB (United Kingdom)', phoneLength: 10 },
    { country: 'United States', value: '+1', iso: 'US', label: '+1-US (United States)', phoneLength: 10 },
    { country: 'Uruguay', value: '+598', iso: 'UY', label: '+598-UY (Uruguay)', phoneLength: 9 },
    { country: 'Uzbekistan', value: '+998', iso: 'UZ', label: '+998-UZ (Uzbekistan)', phoneLength: 9 },
    { country: 'Vanuatu', value: '+678', iso: 'VU', label: '+678-VU (Vanuatu)', phoneLength: 7 },
    { country: 'Vatican', value: '+379', iso: 'VA', label: '+379-VA (Vatican)', phoneLength: 8 },
    { country: 'Venezuela', value: '+58', iso: 'VE', label: '+58-VE (Venezuela)', phoneLength: 9 },
    { country: 'Vietnam', value: '+84', iso: 'VN', label: '+84-VN (Vietnam)', phoneLength: 9 },
    { country: 'Wallis and Futuna', value: '+681', iso: 'WF', label: '+681-WF (Wallis and Futuna)', phoneLength: 7 },
    { country: 'Western Sahara', value: '+212', iso: 'EH', label: '+212-EH (Western Sahara)', phoneLength: 9 },
    { country: 'Yemen', value: '+967', iso: 'YE', label: '+967-YE (Yemen)', phoneLength: 9 },
    { country: 'Zambia', value: '+260', iso: 'ZM', label: '+260-ZM (Zambia)', phoneLength: 9 },
    { country: 'Zimbabwe', value: '+263', iso: 'ZW', label: '+263-Z (Zimbabwe)W', phoneLength: 9 },
];

function deduplicateCountryCodes(options: ICountryCode[]): ICountryCode[] {
    const seen = new Set<string>();
    return options.filter(entry => {
        if (seen.has(entry.value)) return false;
        seen.add(entry.value);
        return true;
    });
}

export const countryCodesOptions = deduplicateCountryCodes(countryCodes);
