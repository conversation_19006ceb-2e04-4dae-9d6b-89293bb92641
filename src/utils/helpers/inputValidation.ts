import * as React from 'react';

export function allowOnlyNumbers(e: React.KeyboardEvent<HTMLInputElement>) {
    if (['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'Home', 'End'].includes(e.key)) {
        return;
    }
    if (!/^[0-9.]$/.test(e.key)) {
        e.preventDefault();
    }
}

export function allowOnlyAlphabets(e: React.KeyboardEvent<HTMLInputElement>) {
    if (
        ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'Home', 'End', ' '].includes(e.key)
    ) {
        return;
    }
    if (!/^[a-zA-Z]$/.test(e.key)) {
        e.preventDefault();
    }
}

export function allowOnlyAlphabetsAndNumbers(e: React.KeyboardEvent<HTMLInputElement>) {
    const allowedControlKeys = new Set([
        'Backspace',
        'Delete',
        'Tab',
        'Escape',
        'Enter',
        '<PERSON>Left',
        'ArrowRight',
        'Home',
        'End',
    ]);

    // Allow control keys
    if (allowedControlKeys.has(e.key)) {
        return;
    }

    // Allow only a-z, A-Z, 0-9
    if (!/^[a-zA-Z0-9]$/.test(e.key)) {
        e.preventDefault();
    }
}
