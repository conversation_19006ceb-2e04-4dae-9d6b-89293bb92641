import { format } from 'date-fns';

export function formatDate(date: Date) {
    return format(date, 'dd-MM-yyyy');
}

export function formatDateTime(date: Date) {
    return date ? format(date, 'dd-MM-yyyy , HH:mm') : '';
}

export function formatApiDate(date: Date) {
    return format(date, 'yyyy-MM-dd');
}

export function formatApiDateTime(date: Date) {
    return format(date, 'yyyy-MM-dd HH:mm');
}
