function getNextIntervalDateTime(interval: number = 30): Date {
    const now = new Date();
    const minutes = now.getMinutes();
    const nextMinutes = Math.ceil(minutes / interval) * interval;
    if (nextMinutes === 60) {
        now.setHours(now.getHours() + 1, 0, 0, 0);
    } else {
        now.setMinutes(nextMinutes, 0, 0);
    }
    return now;
}

function addMinutesToDateTime(date: Date, minutesToAdd: number): Date {
    const newDate = new Date(date);
    newDate.setMinutes(newDate.getMinutes() + minutesToAdd);
    return newDate;
}

function formatTime(date: Date): string {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
}

const checkInDateTime = getNextIntervalDateTime(30);
const checkOutDateTime = addMinutesToDateTime(checkInDateTime, 180);

export const SearchFormInitialValues = {
    airport: [],
    checkInDate: checkInDateTime,
    checkOutDate: checkOutDateTime,
    checkInTime: formatTime(checkInDateTime),
    checkOutTime: formatTime(checkOutDateTime),
    flightNumber: '',
    area: [],
    noOfAdults: 1,
    noOfChildren: 0,
    rooms: 1,
};
