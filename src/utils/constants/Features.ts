import * as React from 'react';
import { CalendarDaysIcon, ClockIcon, TagIcon, GlobeAltIcon } from '@heroicons/react/24/outline';

export interface StayBenefit {
    title: string;
    description: string;
    icon: React.ElementType;
}

export const stayBenefits: StayBenefit[] = [
    {
        icon: CalendarDaysIcon,
        title: 'Unlock a New Revenue Stream',
        description:
            'Tap into untapped potential by offering hourly stays and short-term experiences to airside and landside travelers — a segment often ignored.',
    },
    {
        icon: ClockIcon,
        title: 'Maximize Off-Peak Utilization',
        description:
            "Monetize otherwise idle inventory during low-demand hours. Whether it's a hotel room, spa bed, or workspace — every hour counts.",
    },
    {
        icon: TagIcon,
        title: 'Real-Time Reach. Seamless Management. Smarter Growth.',
        description:
            'StayTransit empowers your business to grow in real time. Get discovered by travelers instantly, manage your bookings seamlessly, and watch your revenue rise every day with live data, smart tools, and a network built for hospitality success.',
    },
    {
        icon: GlobeAltIcon,
        title: 'Connect with Transit Travelers at Global Hubs',
        description:
            'Get discovered by transit passengers across international hubs who are actively looking for comfort, convenience, or productivity between flights.',
    },
];

export const stayBenefitsGuest: StayBenefit[] = [
    {
        icon: CalendarDaysIcon,
        title: 'Flexible Bookings',
        description: 'Book your way — anytime, anywhere.',
    },
    {
        icon: ClockIcon,
        title: 'Hourly Bookings',
        description: 'Hourly freedom for a non-stop world.',
    },
    {
        icon: TagIcon,
        title: 'Book Direct Benefits',
        description: 'The best rates live here — book direct.',
    },
    {
        icon: GlobeAltIcon,
        title: 'Eco-conscious & Sustainability',
        description: 'Together for a greener tomorrow.',
    },
];
