import { lazy } from 'react';
import { Navigate, type RouteObject } from 'react-router';
import GuestLayout from '../layouts/GuestLayout.tsx';
import OpenLayout from '../layouts/OpenLayout.tsx';
import ListYourProperty from '../pages/guest/list-your-property';

const GuestPage = lazy(() => import('../pages/guest'));
const Profile = lazy(() => import('../pages/guest/profile'));
const BookingDetailsPage = lazy(() => import('../pages/guest/booking-details'));
const ServiceDetails = lazy(() => import('../pages/onboarding'));
const ReservationVerify = lazy(() => import('../pages/guest/reservation/verify'));
const GuestDetailsPage = lazy(() => import('../pages/guest/reservation/details'));
const CancelPage = lazy(() => import('../pages/guest/reservation/cancel'));
const PaymentSuccess = lazy(() => import('../pages/guest/payment-success'));
const PaymentFailed = lazy(() => import('../pages/guest/payment-failed'));

export const userRoutes: RouteObject[] = [
    {
        path: '/',
        element: <Navigate to={'/guest'} />,
    },
    {
        path: '/guest',
        element: <GuestLayout />,
        children: [
            {
                index: true,
                element: <GuestPage />,
            },
            {
                path: 'profile',
                element: <Profile />,
            },
            {
                path: 'list-your-property',
                element: <ListYourProperty />,
            },
            {
                path: 'booking/:id',
                element: <BookingDetailsPage />,
            },
        ],
    },
    {
        path: '/reservation',
        element: <OpenLayout />,
        children: [
            {
                path: 'success',
                element: <PaymentSuccess />,
            },
            {
                path: 'failed',
                element: <PaymentFailed />,
            },
            {
                path: 'verify',
                element: <ReservationVerify />,
            },
            {
                path: ':bookingId/update',
                element: <GuestDetailsPage />,
            },
            {
                path: ':bookingId/cancel',
                element: <CancelPage />,
            },
        ],
    },

    {
        path: 'onboarding',
        element: <ServiceDetails />,
    },
];
