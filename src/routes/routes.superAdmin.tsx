import { lazy } from 'react';
import type { RouteObject } from 'react-router';
import SuperAdminLayout from '../layouts/SuperAdminLayout.tsx';
import OnboardingLayout from '../layouts/OnboardingLayout.tsx';
const LiveTracking = lazy(() => import('../pages/dashboard/super-admin/live-tracking'));
const MerchantManagement = lazy(() => import('../pages/dashboard/super-admin/merchant-management'));
const SuperAdminDomainValues = lazy(() => import('../pages/dashboard/super-admin/domain-values'));
const Locations = lazy(() => import('../pages/dashboard/super-admin/locations'));
const PolicyList = lazy(() => import('../pages/dashboard/super-admin/policy-management'));
const PolicySetup = lazy(() => import('../pages/dashboard/super-admin/policy-management/setup'));
const PropertyInformation = lazy(() => import('../pages/dashboard/merchant/property-information'));
const PropertyAmenities = lazy(() => import('../pages/dashboard/merchant/amenities'));
const Rooms = lazy(() => import('../pages/dashboard/merchant/rooms'));
const RoomSetup = lazy(() => import('../pages/dashboard/merchant/rooms/setup'));
const PropertyPolicies = lazy(() => import('../pages/dashboard/merchant/property-policies'));
const BusinessDetailsPage = lazy(() => import('../components/onboarding/BusinessDetails.tsx'));

export const superAdminRoutes: RouteObject[] = [
    {
        path: '/admin',
        element: <SuperAdminLayout />,
        children: [
            {
                path: 'live-tracking',
                element: <LiveTracking />,
            },
            {
                path: 'merchant-management',
                element: <MerchantManagement />,
            },
            {
                path: 'domain-values',
                element: <SuperAdminDomainValues />,
            },
            {
                path: 'locations',
                element: <Locations />,
            },
            {
                path: 'policy',
                children: [
                    {
                        index: true,
                        element: <PolicyList />,
                    },
                    {
                        path: 'policy-setup',
                        element: <PolicySetup />,
                    },
                ],
            },
        ],
    },
    {
        path: 'onboarding/:propertyId',
        element: <OnboardingLayout />,
        children: [
            {
                path: 'property-information',
                element: <PropertyInformation />,
            },
            {
                path: 'amenities',
                element: <PropertyAmenities />,
            },
            {
                path: 'room-types',
                children: [
                    {
                        index: true,
                        element: <Rooms />,
                    },
                    {
                        path: 'setup',
                        element: <RoomSetup />,
                    },
                ],
            },
            {
                path: 'policies',
                element: <PropertyPolicies />,
            },
            {
                path: 'business-details',
                element: <BusinessDetailsPage />,
            },
        ],
    },
];
