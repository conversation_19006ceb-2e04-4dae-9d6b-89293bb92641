import { createBrowserRouter, type RouteObject } from 'react-router';
import { userRoutes } from './routes.user';
import { merchantRoutes } from './routes.merchant.tsx';
import { superAdminRoutes } from './routes.superAdmin.tsx';
import NotFound from '../pages/error/NotFound.tsx';
import ErrorPage from '../pages/error/Error.tsx';

const routes: RouteObject[] = [
    {
        path: '/',
        errorElement: <ErrorPage />,
        hasErrorBoundary: true,
        children: [...userRoutes, ...merchantRoutes, ...superAdminRoutes],
    },
    {
        path: '*',
        element: <NotFound />,
    },
];

export const router = createBrowserRouter(routes);
