import { lazy } from 'react';
import type { RouteObject } from 'react-router';
import MerchantLayout from '../layouts/MerchantLayout.tsx';
import MerchantProfile from '../pages/dashboard/merchant/profile/index.tsx';

const LiveTracking = lazy(() => import('../pages/dashboard/merchant/live-tracking'));
const PropertyAmenities = lazy(() => import('../pages/dashboard/merchant/amenities'));
const PropertyInformation = lazy(() => import('../pages/dashboard/merchant/property-information'));
const PropertyPolicies = lazy(() => import('../pages/dashboard/merchant/property-policies'));
const MerchantDomainValues = lazy(() => import('../pages/dashboard/merchant/domain-values'));
const Rooms = lazy(() => import('../pages/dashboard/merchant/rooms'));
const RoomSetup = lazy(() => import('../pages/dashboard/merchant/rooms/setup'));
const Packages = lazy(() => import('../pages/dashboard/merchant/package'));
const PackagesSetup = lazy(() => import('../pages/dashboard/merchant/package/setup'));
const RateCard = lazy(() => import('../pages/dashboard/merchant/rate-card'));
const RoomAvailability = lazy(() => import('../pages/dashboard/merchant/room-availability'));

export const merchantRoutes: RouteObject[] = [
    {
        path: '/:propertyId/merchant',
        element: <MerchantLayout />,
        children: [
            {
                path: 'live-tracking',
                element: <LiveTracking />,
            },
            {
                path: 'property-information',
                element: <PropertyInformation />,
            },
            {
                path: 'amenities',
                element: <PropertyAmenities />,
            },
            {
                path: 'property-policies',
                element: <PropertyPolicies />,
            },
            {
                path: 'rooms',
                children: [
                    {
                        index: true,
                        element: <Rooms />,
                    },
                    {
                        path: 'setup',
                        element: <RoomSetup />,
                    },
                ],
            },
            {
                path: 'package',
                children: [
                    {
                        index: true,
                        element: <Packages />,
                    },
                    {
                        path: 'setup',
                        element: <PackagesSetup />,
                    },
                ],
            },
            {
                path: 'rate-card',
                element: <RateCard />,
            },
            {
                path: 'room-availability',
                element: <RoomAvailability />,
            },
            {
                path: 'domain-values',
                element: <MerchantDomainValues />,
            },
            {
                path: 'profile',
                element: <MerchantProfile />,
            },
        ],
    },
];
