import { LayoutProvider } from '../contexts/LayoutContext.tsx';
import Sidebar from '../components/merchant/Sidebar.tsx';
import Topbar from '../components/merchant/Topbar.tsx';
import { Navigate, Outlet } from 'react-router';
import { merchantMenu } from '../constants/menu.tsx';
import useBoundStore from '../store/useBoundStore.ts';

function Dashboard() {
    const { loggedUser } = useBoundStore();

    if (!loggedUser) {
        return <Navigate to="/guest" />;
    }

    const userRoles = Array.isArray(loggedUser.user.role) ? loggedUser.user.role : [loggedUser.user.role];

    if (!userRoles.includes('admin') && !userRoles.includes('merchant') && !userRoles.includes('superadmin')) {
        return <Navigate to="/guest" />;
    }

    return (
        <LayoutProvider>
            <div className="flex">
                <Sidebar menu={merchantMenu} />
                <div className="w-[calc(100vw-var(--sidebar-width))] ml-auto mt-[var(--topbar-height)] transition-all duration-300 ease-in-out">
                    <Topbar />
                    <div className="p-3 w-full">
                        <Outlet />
                    </div>
                </div>
            </div>
        </LayoutProvider>
    );
}

export default Dashboard;
