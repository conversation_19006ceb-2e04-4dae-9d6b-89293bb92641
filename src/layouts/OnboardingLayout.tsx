import { useEffect, useState } from 'react';
import { BriefcaseIcon, CheckIcon } from '@heroicons/react/24/outline';
import { Outlet, useLocation, useNavigate, useParams } from 'react-router';
import { useApiHook } from '../hooks/useApi.ts';
import type { ICompany } from '../interfaces/ICompany.ts';
import type { IRoomType } from '../interfaces/IRoomType.ts';
import OnboardingHeader from '../components/onboarding/OnboardingHeader.tsx';

type OnboardingStep = {
    label: string;
    name: string;
    link: string;
    active: boolean;
    completed: boolean | undefined;
};

function OnboardingLayout() {
    const location = useLocation();
    const currentRoute = location.pathname;
    const navigate = useNavigate();
    const { propertyId } = useParams();
    const { get } = useApiHook();
    const [property, setProperty] = useState<ICompany | null>(null);
    const [roomTypes, setRoomTypes] = useState<IRoomType[]>([]);
    const fetchProperty = async () => {
        if (propertyId) {
            try {
                const response = await get<ICompany>(`/properties/${propertyId}`);
                setProperty(response);
            } catch (error) {
                console.error('Failed to fetch property:', error);
            }
        }
    };

    const fetchRoomTypes = async () => {
        const response = await get<IRoomType[]>(`/properties/${propertyId}/room-types`);
        setRoomTypes(response);
    };

    useEffect(() => {
        if (propertyId) {
            fetchProperty();
            fetchRoomTypes();
        }
    }, [propertyId, currentRoute]);

    const onboardingSteps: OnboardingStep[] = [
        // {
        //     label: "Service Type",
        //     name: "Service Type",
        //     link: "/onboarding/${propertyId}/service-list",
        //     active: currentRoute.includes('onboarding/${propertyId}/service-list'),
        //     completed: false,
        // },
        {
            label: 'Property Information',
            name: 'Property Information',
            link: `/onboarding/${propertyId}/property-information`,
            active: currentRoute.includes(`onboarding/${propertyId}/property-information`),
            completed: !!property?.customFields.propertyDetails,
        },
        {
            label: 'Amenities',
            name: 'Amenities',
            link: `/onboarding/${propertyId}/amenities`,
            active: currentRoute.includes(`onboarding/${propertyId}/amenities`),
            completed: !!property?.customFields.amenities,
        },
        {
            label: 'Rooms',
            name: 'Rooms',
            link: `/onboarding/${propertyId}/room-types`,
            active: currentRoute.includes(`onboarding/${propertyId}/room-types`),
            completed: roomTypes.length > 0,
        },
        {
            label: 'Policies',
            name: 'Policies',
            link: `/onboarding/${propertyId}/policies`,
            active: currentRoute.includes(`onboarding/${propertyId}/policies`),
            completed: !!property?.customFields?.policies,
        },
        {
            label: 'Business Details',
            name: 'Business Details',
            link: `/onboarding/${propertyId}/business-details`,
            active: currentRoute.includes(`onboarding/${propertyId}/business-details`),
            completed: !!property?.customFields.businessDetails,
        },
    ];

    const handleStepClick = (step: OnboardingStep) => {
        if (step.completed || step.active) {
            navigate(step.link);
        }
    };

    return (
        <>
            <OnboardingHeader />
            <div className="flex justify-center bg-sky-50">
                <div className="grid grid-cols-12 gap-4 p-2 w-full max-w-7xl mx-auto">
                    <div className="col-span-12 bg-white">
                        <div className="w-full pr-2 bg-white p-3 rounded shadow">
                            <ul className="text-blue-600 space-y-2 flex gap-3">
                                {onboardingSteps.map((step, i) => (
                                    <li
                                        key={i}
                                        className={`mb-2 text-black p-2 rounded cursor-pointer flex items-center transition-colors duration-300 
                                                ${
                                                    step.active
                                                        ? 'bg-red-100 text-red-700'
                                                        : step.completed
                                                          ? 'bg-green-50 text-green-700 hover:bg-green-100'
                                                          : 'hover:bg-red-50 hover:text-red-600'
                                                }`}
                                        onClick={() => handleStepClick(step)}
                                    >
                                        {step.completed ? (
                                            <CheckIcon className="h-5 w-5 mr-2 text-green-600" />
                                        ) : (
                                            <BriefcaseIcon className="h-5 w-5 mr-2" />
                                        )}
                                        {step.name}
                                    </li>
                                ))}
                            </ul>
                        </div>
                        <Outlet />
                    </div>
                </div>
            </div>
        </>
    );
}

export default OnboardingLayout;
