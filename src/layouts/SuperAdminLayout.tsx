import { LayoutProvider } from '../contexts/LayoutContext';
import Sidebar from '../components/merchant/Sidebar';
import Topbar from '../components/merchant/Topbar';
import { Navigate, Outlet } from 'react-router';
import { superAdminMenu } from '../constants/menu';
import useBoundStore from '../store/useBoundStore';

function SuperAdminLayout() {
    const { loggedUser } = useBoundStore();

    if (!loggedUser || !loggedUser.user) {
        return <Navigate to="/guest" />;
    }

    const userRoles = Array.isArray(loggedUser.user.role) ? loggedUser.user.role : [loggedUser.user.role];

    if (!userRoles.includes('superadmin')) {
        return <Navigate to="/guest" />;
    }

    return (
        <LayoutProvider>
            <div className="flex">
                <Sidebar menu={superAdminMenu} isSuperAdmin={true} />
                <div className="w-[calc(100vw-var(--sidebar-width))] ml-auto mt-[var(--topbar-height)] transition-all duration-300 ease-in-out">
                    <Topbar />
                    <div className="p-3 w-full">
                        <Outlet />
                    </div>
                </div>
            </div>
        </LayoutProvider>
    );
}

export default SuperAdminLayout;
