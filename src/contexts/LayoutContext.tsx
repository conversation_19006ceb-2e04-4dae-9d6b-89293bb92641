import * as React from 'react';
import { createContext, useEffect, useState } from 'react';

interface LayoutContextType {
    isSidebarSm: boolean;
    setIsSidebarSm: (isSidebarSm: boolean) => void;
}

export const LayoutContext = createContext<LayoutContextType | undefined>(undefined);
export function LayoutProvider({ children }: { children: React.ReactNode }) {
    const [isSidebarSm, setIsSidebarSm] = useState(false);

    useEffect(() => {
        if (isSidebarSm) {
            document.documentElement.classList.add('sidebar-sm');
        } else {
            document.documentElement.classList.remove('sidebar-sm');
        }
    }, [isSidebarSm]);

    return <LayoutContext.Provider value={{ isSidebarSm, setIsSidebarSm }}> {children} </LayoutContext.Provider>;
}
