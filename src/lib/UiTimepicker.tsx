import React, { forwardRef } from 'react';
import { useField } from 'formik';
import { ClockIcon } from '@heroicons/react/24/outline';

interface Props {
    name: string;
    label?: string;
    className?: string;
    inputClassName?: string;
    interval?: number;
    onChange?: (value: string) => void;
    disabled?: boolean;
}

function generateTimeOptions(interval: number = 30): string[] {
    const options: string[] = [];
    for (let h = 0; h < 24; h++) {
        for (let m = 0; m < 60; m += interval) {
            const hour = h.toString().padStart(2, '0');
            const minute = m.toString().padStart(2, '0');
            options.push(`${hour}:${minute}`);
        }
    }
    return options;
}

const TimePicker = forwardRef<HTMLSelectElement, Props>(
    (
        {
            name,
            label,
            className = '',
            inputClassName = '',
            interval = 30,
            disabled = false,
            onChange, // <-- Added
        },
        ref
    ) => {
        const [field, meta, helpers] = useField(name);
        const timeOptions = React.useMemo(() => generateTimeOptions(interval), [interval]);

        const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
            field.onChange(e); // update Formik
            helpers.setTouched(true);
            if (onChange) {
                onChange(e.target.value); // notify parent
            }
        };

        return (
            <div className={className}>
                {label && (
                    <label htmlFor={name} className="block text-base text-gray-800 mb-2 tracking-wide">
                        {label}
                    </label>
                )}
                <div className="relative w-full">
                    <ClockIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4" />
                    <select
                        {...field}
                        id={name}
                        ref={ref}
                        disabled={disabled}
                        className={`w-full h-[40px] pl-9 pr-3 text-sm text-gray-800 bg-transparent focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none border border-gray-300 rounded-md ${inputClassName} ${disabled ? 'bg-gray-100 cursor-not-allowed border-gray-200' : 'hover:border-gray-400'}`}
                        onChange={handleChange} // <-- Added
                    >
                        {timeOptions.map(time => (
                            <option key={time} value={time}>
                                {time}
                            </option>
                        ))}
                    </select>
                    {meta.touched && meta.error && <p className="text-red-500 text-xs mt-1 ">{meta.error}</p>}
                </div>
            </div>
        );
    }
);

TimePicker.displayName = 'TimePicker';

export default TimePicker;
