import React from 'react';
import Select from 'react-dropdown-select';
import { useField, useFormikContext } from 'formik';

interface UiSelectProps {
    name: string;
    label?: string;
    options: { value: string; label: string }[];
    multiple?: boolean;
    logo?: string;
    disabled?: boolean;
    required?: boolean;
    bottomSpacing?: boolean;
    error?: string;
    inputClassName?: string;
    containerClassName?: string;
    onChange?: (values: string[]) => void;
}

const UiSelect: React.FC<UiSelectProps> = ({
    name,
    label,
    options,
    multiple,
    inputClassName = '',
    logo,
    disabled,
    required = false,
    bottomSpacing = true,
    error,
    onChange,
    containerClassName = '',
}) => {
    const { setFieldValue } = useFormikContext();
    const [field, meta] = useField(name);

    const handleChange = (selected: { value: string; label: string }[]) => {
        const selectedValues = selected.map(item => item.value);
        const value = multiple ? selectedValues : selectedValues[0] || '';
        setFieldValue(name, value);
        if (onChange) {
            onChange(selectedValues);
        }
    };

    // Get the selected option(s) based on field value
    const getSelectedOptions = () => {
        if (!field.value) return [];

        if (multiple) {
            return options.filter(option => Array.isArray(field.value) && field.value.includes(option.value));
        } else {
            return options.filter(option => option.value === field.value);
        }
    };

    return (
        <div className={`${containerClassName} ${bottomSpacing ? 'mb-3' : ''}`}>
            <label htmlFor="">
                {label} <span className="text-red-700">{required && '*'}</span>
            </label>
            <Select
                className={`${inputClassName} border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 px-2 py-1 min-h-[40px] ${field.value ? 'select-placeholder-hide' : ''}`}
                name={name}
                options={options}
                values={getSelectedOptions()}
                onChange={handleChange}
                disabled={disabled}
                dropdownPosition="auto"
                multi={multiple}
                placeholder={label}
                dropdownHandle={false}
            />
            {logo && <img src={logo} alt="Logo" className="mt-2 w-6 h-6" />}
            {meta.touched && meta.error ? <div className="text-red-500 mt-1 text-sm">{meta.error}</div> : null}
            {error && <div className="text-red-500 mt-1 text-sm">{error}</div>}
        </div>
    );
};

export default UiSelect;
