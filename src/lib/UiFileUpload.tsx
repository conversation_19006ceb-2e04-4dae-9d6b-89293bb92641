import { useRef, useState, useEffect, type ChangeEvent } from 'react';
import { DocumentArrowUpIcon, XMarkIcon, DocumentTextIcon } from '@heroicons/react/24/solid';
import { useApiHook } from '../hooks/useApi.ts';
import { useToastHook } from '../hooks/useToaster.ts';
import type { IFileUploadResponse, IFileURLResponse } from '../interfaces/IFileUpload.ts';

interface FileUploaderProps {
    label?: string;
    onFileUpload: (urls: string[]) => void;
    name?: string;
    value?: string[];
    allowedTypes?: 'images' | 'documents' | 'all';
    multiple?: boolean;
}

function UiFileUpload({
    label = 'Upload File',
    onFileUpload,
    name = 'fileUpload',
    value = [],
    allowedTypes = 'all',
    multiple = true,
}: FileUploaderProps) {
    const [filePreviews, setFilePreviews] = useState<string[]>([]);
    const { get, post } = useApiHook();
    const fileInputRef = useRef<HTMLInputElement | null>(null);
    const { showError, showSuccess } = useToastHook();

    const isImage = (url: string) => url.includes('.jpg') || url.includes('.jpeg') || url.includes('.png');

    const handleFileUploadAndPreview = async (selectedFiles: File[]) => {
        const uploadedUrls: string[] = [];

        for (const file of selectedFiles) {
            const isImage = /\.(jpg|jpeg|png)$/i.test(file.name);
            const isDocument = /\.(pdf|doc|docx)$/i.test(file.name);

            if ((allowedTypes === 'images' && !isImage) || (allowedTypes === 'documents' && !isDocument)) {
                showError(`"${file.name}" is not a supported file type.`);
                continue;
            }
            if (file.size > 2 * 1024 * 1024) {
                showError(`"${file.name}" exceeds 2MB limit and was not uploaded.`);
                continue;
            }

            const formData = new FormData();
            formData.append('image', file);

            try {
                const uploadResponse = await post<IFileUploadResponse>('images/upload', formData, {
                    headers: { 'Content-Type': 'multipart/form-data' },
                });

                const URLRes = await get<IFileURLResponse>(`/images/preview/${uploadResponse.key}`);
                uploadedUrls.push(URLRes.url);
                showSuccess(`"${file.name}" uploaded successfully.`);
            } catch (error) {
                console.error('Upload failed for file:', file.name, error);
                showError(`Failed to upload "${file.name}".`);
            }
        }

        setFilePreviews(prev => (multiple ? [...prev, ...uploadedUrls] : uploadedUrls));

        onFileUpload(multiple ? [...filePreviews, ...uploadedUrls] : uploadedUrls);
    };

    const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
        const selectedFiles = Array.from(event.target.files || []);
        if (selectedFiles.length === 0) return;
        handleFileUploadAndPreview(selectedFiles);
    };

    const handleRemoveFile = (index: number) => {
        const updatedPreviews = [...filePreviews];
        updatedPreviews.splice(index, 1);
        setFilePreviews(updatedPreviews);
        if (fileInputRef.current) fileInputRef.current.value = '';
        onFileUpload(updatedPreviews);
    };

    useEffect(() => {
        if (value && value.length > 0) {
            setFilePreviews(value);
        }
    }, [value]);

    return (
        <div className="md:col-span-2">
            <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-1">
                {label}*
            </label>

            <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                <div className="space-y-2 text-center">
                    <DocumentArrowUpIcon className="mx-auto h-8 w-8 text-gray-400" />
                    <div className="flex text-sm text-gray-600 justify-center">
                        <label
                            htmlFor={name}
                            className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500"
                        >
                            {multiple ? <span>Upload files</span> : <span>Upload file</span>}
                            <input
                                id={name}
                                name={name}
                                type="file"
                                className="sr-only"
                                ref={fileInputRef}
                                onChange={handleFileChange}
                                accept={
                                    allowedTypes === 'images'
                                        ? '.jpg,.jpeg,.png'
                                        : allowedTypes === 'documents'
                                          ? '.pdf,.doc,.docx'
                                          : '.pdf,.doc,.docx,.jpg,.jpeg,.png'
                                }
                                multiple={multiple}
                            />
                        </label>
                        <span className="pl-1">or drag and drop</span>
                    </div>
                    {allowedTypes === 'images' ? (
                        <p className="text-xs text-gray-500">JPG, PNG up to 2MB each</p>
                    ) : allowedTypes === 'documents' ? (
                        <p className="text-xs text-gray-500">PDF up to 2MB each</p>
                    ) : (
                        <p className="text-xs text-gray-500">PDF, DOC, JPG, PNG up to 2MB each</p>
                    )}

                    {/* Preview */}
                    <div className="mt-4 flex flex-wrap gap-4 justify-center">
                        {filePreviews.map((url, index) => (
                            <div key={index} className="relative w-24 rounded overflow-hidden border shadow-sm">
                                {isImage(url) ? (
                                    <img src={url} alt={`File ${index}`} className="object-cover" />
                                ) : (
                                    <div className="flex items-center justify-center w-full h-full bg-gray-100 text-red-600">
                                        <DocumentTextIcon className="w-8 h-8" />
                                    </div>
                                )}
                                <button
                                    type="button"
                                    onClick={() => handleRemoveFile(index)}
                                    className="absolute top-1 right-1 bg-white rounded-full p-0.5 text-red-500 hover:text-red-700 shadow-sm"
                                    title="Remove"
                                >
                                    <XMarkIcon className="h-4 w-4" />
                                </button>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
}

export default UiFileUpload;
