import React from 'react';
import { useField } from 'formik';
import { EyeSlashIcon, EyeIcon } from '@heroicons/react/24/outline';

interface UiInputProps {
    label?: string;
    name: string;
    type?: 'text' | 'number' | 'email' | 'password' | 'date' | 'time' | 'checkbox';
    placeholder?: string;
    required?: boolean;
    readonly?: boolean;
    disabled?: boolean;
    bottomSpacing?: boolean;
    suffix?: React.ReactNode;
    onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
    countryCodeFieldName?: string;
    widthClassname?: string;
    inlineText?: string;
    onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
}

const UiInput: React.FC<UiInputProps> = ({
    label,
    placeholder,
    readonly = false,
    required = false,
    bottomSpacing = true,
    suffix,
    inlineText,
    widthClassname,
    type = 'text',
    onChange,
    disabled = false,
    onKeyDown,
    ...props
}) => {
    const [field, meta, helpers] = useField(props.name);

    const [isPasswordVisible, setIsPasswordVisible] = React.useState(false);

    const togglePasswordVisibility = React.useCallback(() => {
        setIsPasswordVisible(prev => !prev);
    }, []);

    return (
        <div className={`flex flex-col w-full ${bottomSpacing ? 'mb-3' : ''}`}>
            <div className="relative">
                {label && (
                    <>
                        <label>
                            {label} <span className="text-red-700">{required && '*'}</span>
                        </label>
                        <br />
                    </>
                )}

                <input
                    {...field}
                    {...props}
                    type={type === 'password' && isPasswordVisible ? 'text' : type}
                    placeholder={placeholder}
                    autoComplete="off"
                    disabled={disabled}
                    readOnly={readonly}
                    className={`${widthClassname || 'w-full'} px-3 py-2 border h-10 rounded-md ${
                        type === 'password' ? 'pr-10' : ''
                    } ${
                        meta.touched && meta.error ? 'border-red-500' : 'border-gray-300'
                    } ${readonly ? 'bg-gray-100' : ''}`}
                    onChange={e => {
                        // const value =
                        //   type === "text"
                        //     ? e.target.value.replace(/[^A-Za-z\s]/g, "")
                        //     : e.target.value;
                        helpers.setValue(e.target.value);
                        if (onChange) onChange(e);
                    }}
                    onKeyDown={onKeyDown}
                />
                {inlineText && <span className="ml-2 text-gray-900  text-md whitespace-nowrap">{inlineText}</span>}

                {type === 'password' ? (
                    <button
                        type="button"
                        className="absolute right-3 top-[35px] flex items-center justify-center cursor-pointer z-10"
                        onClick={togglePasswordVisibility}
                        aria-label={isPasswordVisible ? 'Hide password' : 'Show password'}
                    >
                        {isPasswordVisible ? (
                            <EyeSlashIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                        ) : (
                            <EyeIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                        )}
                    </button>
                ) : (
                    suffix && (
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center">
                            {suffix}
                        </div>
                    )
                )}
                {meta.touched && meta.error && <div className="text-red-500 text-sm mt-1">{meta.error}</div>}
            </div>
        </div>
    );
};

export default UiInput;
