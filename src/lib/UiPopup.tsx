import React from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';

interface PopupProps {
    isOpen: boolean;
    onClose: () => void;
    title?: string;
    children: React.ReactNode;
    width?: string; // e.g. 'max-w-md', 'w-[400px]'
    height?: string; // e.g. 'max-h-md', 'h-[400px]'
    showCloseButton?: boolean;
    closeOnOverlayClick?: boolean;
    className?: string;
}

const UiPopup: React.FC<PopupProps> = ({
    isOpen,
    onClose,
    title,
    children,
    width = 'max-w-lg',
    height = 'max-h-[90vh]',
    showCloseButton = true,
    closeOnOverlayClick = false,
    className = '',
}) => {
    if (!isOpen) return null;

    const handleOverlayClick = (e: React.MouseEvent) => {
        if (closeOnOverlayClick && e.target === e.currentTarget) {
            onClose();
        }
    };

    return (
        <div
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-[100] p-4"
            onClick={handleOverlayClick}
        >
            <div
                className={`bg-white rounded-lg shadow-xl ${width} ${height} ${className}`}
                onClick={e => e.stopPropagation()}
            >
                {/* Header */}
                {(title || showCloseButton) && (
                    <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
                        {title && <h2 className="text-lg font-semibold text-gray-900">{title}</h2>}
                        {showCloseButton && (
                            <button
                                onClick={onClose}
                                className="text-gray-500 hover:text-gray-800 transition-colors"
                                aria-label="Close popup"
                            >
                                <XMarkIcon className="w-6 h-6" />
                            </button>
                        )}
                    </div>
                )}

                {/* Content */}
                <div
                    className="overflow-y-auto"
                    style={{
                        maxHeight: title || showCloseButton ? 'calc(100vh - 50px)' : '100vh ',
                    }}
                >
                    {children}
                </div>
            </div>
        </div>
    );
};

export default UiPopup;
