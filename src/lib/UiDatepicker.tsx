import React from 'react';
import { useField, useFormikContext } from 'formik';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

interface UiDatePickerProps {
    label: string;
    name: string;
    placeholder?: string;
    readonly?: boolean;
    bottomSpacing?: boolean;
    minDate?: Date;
    required?: boolean;
}

const UiDatePicker: React.FC<UiDatePickerProps> = ({
    label,
    name,
    placeholder,
    readonly = false,
    bottomSpacing = true,
    minDate,
    required = false,
}) => {
    const { setFieldValue } = useFormikContext();
    const [field, meta] = useField(name);

    const value = field.value ? new Date(field.value) : null;

    const handleDateChange = (date: Date | null) => {
        setFieldValue(name, date);
    };

    return (
        <div className={`flex flex-col w-full ${bottomSpacing ? 'mb-3' : ''}`}>
            {label && (
                <label htmlFor={name} className="text-sm mb-1">
                    {label} {required && <span className="text-red-700">*</span>}{' '}
                </label>
            )}
            <div className="relative">
                <DatePicker
                    {...(minDate ? { minDate } : {})}
                    id={name}
                    selected={value}
                    onChange={handleDateChange}
                    dateFormat="yyyy-MM-dd"
                    placeholderText={placeholder || 'Select date'}
                    disabled={readonly}
                    className={`w-full px-3 py-2 border h-10 rounded-md focus:outline-none ${
                        meta.touched && meta.error ? 'border-red-500' : 'border-gray-300'
                    }`}
                />
            </div>

            {meta.touched && meta.error && <div className="text-red-500 text-sm mt-1">{meta.error as string}</div>}
        </div>
    );
};

export default UiDatePicker;
