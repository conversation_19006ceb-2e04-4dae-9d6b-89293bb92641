import React from 'react';
import { useField } from 'formik';

interface UiRadioGroupProps {
    name: string;
    label?: string;
    options: { label: string; value: string | boolean }[];
    required?: boolean;
    bottomSpacing?: boolean;
    direction?: 'row' | 'column';
}

const UiRadioGroupField: React.FC<UiRadioGroupProps> = ({
    name,
    label,
    options,
    required = false,
    bottomSpacing = true,
    direction = 'row',
}) => {
    const [field, meta, helpers] = useField(name);

    return (
        <div className={`flex flex-col w-full ${bottomSpacing ? 'mb-3' : ''}`}>
            {label && (
                <label className="text-md text-gray-700 mb-1">
                    {label} {required && <span className="text-red-700">*</span>}
                </label>
            )}

            <div className={`flex ${direction === 'row' ? 'flex-row gap-6' : 'flex-col gap-2'}`}>
                {options.map(opt => (
                    <label key={String(opt.value)} className="inline-flex items-center">
                        <input
                            type="radio"
                            name={name}
                            value={String(opt.value)}
                            required={required}
                            checked={field.value === opt.value}
                            onChange={() => {
                                helpers.setValue(opt.value, true);
                            }}
                            onBlur={field.onBlur}
                            className="w-5 h-5 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                        />
                        <span className="ml-2 text-sm text-gray-700">{opt.label}</span>
                    </label>
                ))}
            </div>

            {meta.touched && meta.error && <div className="text-red-500 text-sm mt-1">{meta.error}</div>}
        </div>
    );
};

export default UiRadioGroupField;
