function UiLoader({ label }: { label: string }) {
    return (
        <div className="fixed inset-0 bg-[#00000080] flex items-center justify-center z-50">
            <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
                <p className="text-gray-600">{label}</p>
            </div>
        </div>
    );
}

export default UiLoader;
