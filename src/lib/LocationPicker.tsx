import React, { useState, useEffect, useCallback } from 'react';
import { GoogleMap, Marker } from '../services/map-service.ts';

interface GoogleMapUiProps {
    placeId?: string;
    width?: string;
    height?: string;
    onPointSelect?: (location: {
        lat: number;
        lng: number;
        address: string;
        city?: string;
        state?: string;
        country?: string;
        zipcode?: string;
        placeId?: string;
    }) => void;
}

const GoogleMapUi: React.FC<GoogleMapUiProps> = ({ placeId, width = '100%', height = '400px', onPointSelect }) => {
    const [selectedLocation, setSelectedLocation] = useState<google.maps.LatLngLiteral | null>(null);

    // 🔁 Load location from placeId (initial load)
    const fetchPlaceLocation = useCallback(() => {
        if (!placeId || !window.google) return;

        const service = new google.maps.places.PlacesService(document.createElement('div'));
        service.getDetails(
            { placeId, fields: ['geometry', 'address_components', 'formatted_address'] },
            (place, status) => {
                if (status === google.maps.places.PlacesServiceStatus.OK && place?.geometry?.location) {
                    const location = place.geometry.location;
                    if (location) {
                        const lat = location.lat();
                        const lng = location.lng();
                        const locationData = extractAddressData(
                            lat,
                            lng,
                            place.address_components || [],
                            place.formatted_address || '',
                            place.place_id
                        );
                        setSelectedLocation({ lat, lng });
                        onPointSelect?.(locationData);
                    }
                } else {
                    console.warn('Place details fetch failed:', status);
                }
            }
        );
    }, [placeId, onPointSelect]);

    useEffect(() => {
        fetchPlaceLocation();
    }, [fetchPlaceLocation]);

    // 🔁 Handle clicks with reverse geocoding
    const handleMapClick = (event: google.maps.MapMouseEvent) => {
        if (event.latLng) {
            const lat = event.latLng.lat();
            const lng = event.latLng.lng();

            const geocoder = new google.maps.Geocoder();
            geocoder.geocode({ location: { lat, lng } }, (results, status) => {
                if (status === 'OK' && results && results.length > 0) {
                    const result = results[0];
                    const locationData = extractAddressData(
                        lat,
                        lng,
                        result.address_components,
                        result.formatted_address,
                        result.place_id
                    );
                    setSelectedLocation({ lat, lng });
                    onPointSelect?.(locationData);
                } else {
                    console.warn('Reverse geocoding failed:', status);
                }
            });
        }
    };

    // 🔁 Extract useful fields from address_components
    const extractAddressData = (
        lat: number,
        lng: number,
        components: google.maps.GeocoderAddressComponent[],
        fullAddress: string,
        placeId?: string
    ) => {
        const getComponent = (type: string) => components.find(c => c.types.includes(type))?.long_name || '';

        return {
            lat,
            lng,
            address: fullAddress,
            city: getComponent('locality'),
            state: getComponent('administrative_area_level_1'),
            zipcode: getComponent('postal_code'),
            country: getComponent('country'), // ✅ Add this line
            placeId,
        };
    };

    const defaultCenter = selectedLocation || { lat: 20, lng: 0 };

    return (
        <div style={{ width, height }}>
            <GoogleMap
                mapContainerStyle={{ width: '100%', height: '100%' }}
                center={defaultCenter}
                zoom={selectedLocation ? 15 : 2}
                onClick={handleMapClick}
            >
                {selectedLocation && <Marker position={selectedLocation} />}
            </GoogleMap>
        </div>
    );
};

export default GoogleMapUi;
