import React from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';

interface DrawerProps {
    isOpen: boolean;
    onClose: () => void;
    title?: string;
    children: React.ReactNode;
    width?: string; // e.g. 'max-w-md', 'w-[400px]'
    position?: 'left' | 'right' | 'top' | 'bottom';
    showCloseButton?: boolean;
    closeOnOverlayClick?: boolean;
    className?: string;
    overlayClassName?: string;
    headerClassName?: string;
    contentClassName?: string;
}

const UiDrawer: React.FC<DrawerProps> = ({
    isOpen,
    onClose,
    title,
    children,
    width = 'max-w-lg',
    position = 'right',
    showCloseButton = true,
    closeOnOverlayClick = true,
    className = '',
    overlayClassName = '',
    headerClassName = '',
    contentClassName = '',
}) => {
    if (!isOpen) return null;

    const handleOverlayClick = (e: React.MouseEvent) => {
        if (closeOnOverlayClick && e.target === e.currentTarget) {
            onClose();
        }
    };

    // Position classes
    const getPositionClasses = () => {
        switch (position) {
            case 'left':
                return {
                    container: 'left-0 h-full',
                    transform: isOpen ? 'translate-x-0' : '-translate-x-full',
                    overlay: 'left-0',
                };
            case 'right':
                return {
                    container: 'right-0 h-full',
                    transform: isOpen ? 'translate-x-0' : 'translate-x-full',
                    overlay: 'right-0',
                };
            case 'top':
                return {
                    container: 'top-0 w-full',
                    transform: isOpen ? 'translate-y-0' : '-translate-y-full',
                    overlay: 'top-0',
                };
            case 'bottom':
                return {
                    container: 'bottom-0 w-full',
                    transform: isOpen ? 'translate-y-0' : 'translate-y-full',
                    overlay: 'bottom-0',
                };
            default:
                return {
                    container: 'right-0 h-full',
                    transform: isOpen ? 'translate-x-0' : 'translate-x-full',
                    overlay: 'right-0',
                };
        }
    };

    const positionClasses = getPositionClasses();

    return (
        <>
            {/* Overlay */}
            <div className={`fixed inset-0 z-[100] bg-black/30 ${overlayClassName}`} onClick={handleOverlayClick} />

            {/* Drawer */}
            <div
                className={`fixed ${positionClasses.container} ${width} bg-white shadow-xl z-[100] transform transition-transform duration-300 ease-in-out ${positionClasses.transform} ${className}`}
            >
                {/* Header */}
                {(title || showCloseButton) && (
                    <div className={`flex items-center justify-between px-6 py-4 border-b ${headerClassName}`}>
                        {title && <h2 className="text-lg font-semibold text-gray-900">{title}</h2>}
                        {showCloseButton && (
                            <button
                                onClick={onClose}
                                className="text-gray-500 hover:text-gray-800 transition-colors"
                                aria-label="Close drawer"
                            >
                                <XMarkIcon className="w-6 h-6" />
                            </button>
                        )}
                    </div>
                )}

                {/* Content */}
                <div
                    className={`overflow-y-auto ${contentClassName}`}
                    style={{
                        height: title || showCloseButton ? 'calc(100% - 64px)' : '100%',
                    }}
                >
                    {children}
                </div>
            </div>
        </>
    );
};

export default UiDrawer;
