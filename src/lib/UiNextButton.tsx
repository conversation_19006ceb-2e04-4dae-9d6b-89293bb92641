import React from 'react';
import { useFormikContext } from 'formik';
import { Link } from 'react-router';

interface UiNextButtonProps {
    label: string;
    loadingLabel?: string;
    className?: string;
    isFormik?: boolean;
    to?: string;
}

const UiNextButton: React.FC<UiNextButtonProps> = ({
    label,
    loadingLabel = 'Submitting...',
    className = '',
    isFormik = true,
    to,
}) => {
    // TODO: Fix the type issues.
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const formik = isFormik ? useFormikContext() : null;
    const isSubmitting = formik ? formik.isSubmitting : false;

    // Common button JSX to avoid duplication
    const button = (
        <button
            type="submit"
            className={`w-full sm:w-auto py-3 px-8 text-white  theme-background rounded-md shadow-md hover:bg-red-700 focus:ring-2 focus:ring-indigo-500 focus:outline-none transition-all duration-300 font-medium disabled:opacity-60 mt-4 ${className}`}
            disabled={isSubmitting}
        >
            {isSubmitting ? loadingLabel : label}
        </button>
    );

    return (
        <div className="flex justify-end">
            {to ? <Link to={to}>{button}</Link> : button}
            {/*{isSubmitting && (*/}
            {/*    <div className="w-full text-blue-700 bg-blue-50 border border-blue-200 rounded p-3 text-sm mt-2">*/}
            {/*        Submitting, please wait...*/}
            {/*    </div>*/}
            {/*)}*/}
        </div>
    );
};

export default UiNextButton;
