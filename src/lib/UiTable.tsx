import { type ReactNode, useMemo, useState } from 'react';
import {
    useReactTable,
    getCoreRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    getFilteredRowModel,
    type ColumnDef,
    flexRender,
    type SortingState,
    type ColumnFiltersState,
    type Row,
} from '@tanstack/react-table';

// Define the props for the UiTable component
interface UiTableProps<T> {
    columns: ColumnDef<T, unknown>[];
    data: T[];
    enableSorting?: boolean;
    enableFiltering?: boolean;
    enablePagination?: boolean;
    enableGlobalFilter?: boolean;
    pageSizeOptions?: number[];
    defaultPageSize?: number;
    className?: string;
    children?: ReactNode;
}

function UiTable<T>({
    columns,
    data,
    enableSorting = true,
    enableFiltering = false,
    enablePagination = true,
    enableGlobalFilter = true,
    pageSizeOptions = [10, 20, 30, 40, 50],
    defaultPageSize = 10,
    className = '',
    children,
}: UiTableProps<T>) {
    // State for sorting and filtering
    const [sorting, setSorting] = useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    const [globalFilter, setGlobalFilter] = useState('');

    // Memoize data and columns to prevent unnecessary re-renders
    const memoizedData = useMemo(() => data, [data]);
    const memoizedColumns = useMemo(() => columns, [columns]);

    // Custom global filter function that searches across all columns
    const globalFilterFn = useMemo(() => {
        return (row: Row<T>, _columnId: string, filterValue: string) => {
            const searchValue = filterValue.toLowerCase();

            // Get all values from the row
            const rowValues = Object.values(row.original as Record<string, unknown>);

            // Check if any value contains the search term
            return rowValues.some(value => {
                if (value === null || value === undefined) return false;
                return String(value).toLowerCase().includes(searchValue);
            });
        };
    }, []);

    // Initialize the table instance
    const table = useReactTable({
        data: memoizedData,
        columns: memoizedColumns,
        state: {
            sorting,
            columnFilters,
            globalFilter,
        },
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onGlobalFilterChange: setGlobalFilter,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: enableSorting ? getSortedRowModel() : undefined,
        getFilteredRowModel: enableFiltering || enableGlobalFilter ? getFilteredRowModel() : undefined,
        getPaginationRowModel: enablePagination ? getPaginationRowModel() : undefined,
        globalFilterFn: globalFilterFn,
        initialState: {
            pagination: {
                pageSize: defaultPageSize,
            },
        },
    });

    return (
        <div className={`space-y-4 ${className}`}>
            {/* Table */}
            <div className="flex items-center justify-between w-full">{children}</div>
            {enableGlobalFilter && (
                <div className="flex items-center space-x-2">
                    <div className="relative">
                        <input
                            type="text"
                            placeholder="Search..."
                            value={globalFilter ?? ''}
                            onChange={e => setGlobalFilter(e.target.value)}
                            className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64 pr-8"
                        />
                        {globalFilter && (
                            <button
                                onClick={() => setGlobalFilter('')}
                                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                            >
                                ✕
                            </button>
                        )}
                    </div>
                    {globalFilter && (
                        <span className="text-sm text-gray-500">
                            {table.getFilteredRowModel().rows.length} of {table.getPreFilteredRowModel().rows.length}{' '}
                            results
                        </span>
                    )}
                </div>
            )}
            <div className="w-full overflow-x-auto border border-gray-200 rounded-lg">
                <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                        {table.getHeaderGroups().map(headerGroup => (
                            <tr key={headerGroup.id}>
                                {headerGroup.headers.map(header => (
                                    <th
                                        key={header.id}
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap"
                                    >
                                        <div className="flex items-center space-x-1">
                                            <span>
                                                {header.isPlaceholder
                                                    ? null
                                                    : flexRender(header.column.columnDef.header, header.getContext())}
                                            </span>
                                            {enableSorting && header.column.getCanSort() && (
                                                <button
                                                    onClick={header.column.getToggleSortingHandler()}
                                                    className="focus:outline-none"
                                                >
                                                    {header.column.getIsSorted() === 'asc' ? (
                                                        <svg
                                                            xmlns="http://www.w3.org/2000/svg"
                                                            fill="none"
                                                            viewBox="0 0 24 24"
                                                            strokeWidth={1.5}
                                                            stroke="currentColor"
                                                            className="w-4 h-4"
                                                        >
                                                            <path
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                                d="M8.25 15l3.75-3.75L15.75 15"
                                                            />
                                                        </svg>
                                                    ) : header.column.getIsSorted() === 'desc' ? (
                                                        <svg
                                                            xmlns="http://www.w3.org/2000/svg"
                                                            fill="none"
                                                            viewBox="0 0 24 24"
                                                            strokeWidth={1.5}
                                                            stroke="currentColor"
                                                            className="w-4 h-4"
                                                        >
                                                            <path
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                                d="M15.75 9l-3.75 3.75L8.25 9"
                                                            />
                                                        </svg>
                                                    ) : (
                                                        <svg
                                                            xmlns="http://www.w3.org/2000/svg"
                                                            fill="none"
                                                            viewBox="0 0 24 24"
                                                            strokeWidth={1.5}
                                                            stroke="currentColor"
                                                            className="w-4 h-4 opacity-50"
                                                        >
                                                            <path
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                                d="M15.75 9l-3.75 3.75L8.25 9"
                                                            />
                                                        </svg>
                                                    )}
                                                </button>
                                            )}
                                        </div>
                                        {enableFiltering && header.column.getCanFilter() && (
                                            <div className="mt-2">
                                                <input
                                                    type="text"
                                                    value={(header.column.getFilterValue() as string) ?? ''}
                                                    onChange={e => header.column.setFilterValue(e.target.value)}
                                                    placeholder={`Filter ${header.column.columnDef.header}`}
                                                    className="w-full px-2 py-1 text-sm border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                                                />
                                            </div>
                                        )}
                                    </th>
                                ))}
                            </tr>
                        ))}
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        {table.getRowModel().rows.map(row => (
                            <tr key={row.id} className="hover:bg-gray-50">
                                {row.getVisibleCells().map(cell => (
                                    <td key={cell.id} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                    </td>
                                ))}
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>

            {/* Pagination Controls */}
            {enablePagination && (
                <div className="flex items-center justify-between p-2">
                    <div className="flex items-center space-x-2">
                        <button
                            onClick={() => table.previousPage()}
                            disabled={!table.getCanPreviousPage()}
                            className="px-3 py-1 border rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            Previous
                        </button>
                        <button
                            onClick={() => table.nextPage()}
                            disabled={!table.getCanNextPage()}
                            className="px-3 py-1 border rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            Next
                        </button>
                        <span className="text-sm text-gray-700">
                            Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
                        </span>
                    </div>
                    <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-700">Rows per page:</span>
                        <select
                            value={table.getState().pagination.pageSize}
                            onChange={e => table.setPageSize(Number(e.target.value))}
                            className="px-2 py-1 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                        >
                            {pageSizeOptions.map(size => (
                                <option key={size} value={size}>
                                    {size}
                                </option>
                            ))}
                        </select>
                    </div>
                </div>
            )}
        </div>
    );
}

export default UiTable;
