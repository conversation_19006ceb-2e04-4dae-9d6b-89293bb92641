import React from 'react';
import { useNavigate } from 'react-router';

interface UiBackButtonProps {
    label?: string;
    className?: string;
    goBack?: boolean;
    redirectTo?: string;
}

const UiBackButton: React.FC<UiBackButtonProps> = ({ label = 'Back', className = '', goBack = true, redirectTo }) => {
    const navigate = useNavigate();

    const handleClick = () => {
        if (goBack) {
            navigate(-1);
        } else if (redirectTo) {
            navigate(redirectTo);
        }
    };

    return (
        <button
            type="button"
            onClick={handleClick}
            className={`w-full sm:w-auto py-3 px-8 text-white bg-gray-400 rounded-md shadow-md hover:bg-gray-500 focus:ring-2 focus:ring-gray-300 focus:outline-none transition-all duration-300 font-medium disabled:opacity-60 mt-4 ${className}`}
        >
            {/* <ArrowLeftIcon className="h-5 w-5" /> */}
            {label}
        </button>
    );
};

export default UiBackButton;
