import React from 'react';

const UiButton = ({
    children,
    className,
    onClick,
    disabled,
    type = 'button',
    isTheme = true,
    loading = false,
}: {
    children: React.ReactNode;
    className?: string;
    onClick?: () => void;
    disabled?: boolean;
    type?: 'button' | 'submit' | 'reset' | undefined;
    isTheme?: boolean;
    loading?: boolean;
}) => {
    return (
        <button
            className={`${isTheme ? 'theme-background' : ''} text-white p-2 px-4 rounded-full ${className} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            onClick={onClick}
            disabled={disabled}
            type={type}
        >
            {loading ? (
                <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                </div>
            ) : (
                children
            )}
        </button>
    );
};

export default UiButton;
