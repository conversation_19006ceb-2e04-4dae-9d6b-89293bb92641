import React, { useEffect, useRef, useState } from 'react';
import { Autocomplete } from '../services/map-service.ts';

interface LocationData {
    address: string;
    city?: string;
    state?: string;
    country?: string;
    zipcode?: string;
    placeId?: string;
    latitude?: number;
    longitude?: number;
}

interface UiMapInputProps {
    onLocationSelect: (location: LocationData) => void;
    label?: string;
    placeholder?: string;
    defaultValue?: string;
    required?: boolean;
}

const UiMapInput: React.FC<UiMapInputProps> = ({
    onLocationSelect,
    label,
    placeholder = 'Search for a location',
    defaultValue = '',
    required = false,
}) => {
    const [inputValue, setInputValue] = useState(defaultValue);
    const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);

    useEffect(() => {
        setInputValue(defaultValue);
    }, [defaultValue]);

    const handlePlaceSelect = () => {
        const place = autocompleteRef.current?.getPlace();
        if (!place || !place.address_components) return;

        const location = place.geometry?.location;
        const addressData: LocationData = {
            address: place.formatted_address!,
            city: place.address_components.find(c => c.types.includes('locality'))?.long_name,
            state: place.address_components.find(c => c.types.includes('administrative_area_level_1'))?.long_name,
            country: place.address_components.find(c => c.types.includes('country'))?.long_name,
            zipcode: place.address_components.find(c => c.types.includes('postal_code'))?.long_name,
            latitude: location ? location.lat() : undefined,
            longitude: location ? location.lng() : undefined,
            placeId: place.place_id,
        };

        setInputValue(addressData.address);
        onLocationSelect(addressData);
    };

    return (
        <div className="mb-4">
            {label && (
                <label className="block text-sm font-medium text-gray-700 mb-1">
                    {label} {required && <span className={'text-red-500'}>*</span>}
                </label>
            )}
            <Autocomplete
                onLoad={autocomplete => (autocompleteRef.current = autocomplete)}
                onPlaceChanged={handlePlaceSelect}
            >
                <input
                    type="text"
                    className="w-full border p-2 rounded border-gray-300"
                    placeholder={placeholder}
                    value={inputValue}
                    onChange={e => setInputValue(e.target.value)}
                />
            </Autocomplete>
        </div>
    );
};

export default UiMapInput;
