import toast from 'react-hot-toast';

interface ToastOptions {
    duration?: number;
    position?: 'top-right' | 'top-center' | 'top-left' | 'bottom-right' | 'bottom-center' | 'bottom-left';
}

export const useToastHook = () => {
    const showSuccess = (message: string, options?: ToastOptions) => {
        toast.success(message, {
            ...options,
            iconTheme: {
                primary: 'var(--primary-color)',
                secondary: 'var(--white-color)',
            },
        });
    };

    const showError = (message: string, options?: ToastOptions) => {
        toast.error(message, {
            ...options,
            iconTheme: {
                primary: '#ef4444',
                secondary: 'var(--white-color)',
            },
        });
    };

    const showLoading = (message: string, options?: ToastOptions): string => {
        return toast.loading(message, {
            ...options,
        });
    };

    const dismiss = (toastId?: string) => {
        if (toastId) {
            toast.dismiss(toastId);
        } else {
            toast.dismiss();
        }
    };

    return {
        showSuccess,
        showError,
        showLoading,
        dismiss,
    };
};
