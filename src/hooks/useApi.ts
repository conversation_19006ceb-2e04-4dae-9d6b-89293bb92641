import { useState, useCallback } from 'react';
import { type AxiosResponse, AxiosError, type AxiosRequestConfig } from 'axios';
import api from '../services/api.ts';

interface ErrorResponse {
    error?: string;
}
interface UseApiHookReturn {
    loading: boolean;
    error: string | null;
    api: typeof api;
    get: <T>(url: string, config?: AxiosRequestConfig) => Promise<T>;
    post: <T>(url: string, data?: unknown, config?: AxiosRequestConfig) => Promise<T>;
    put: <T>(url: string, data?: unknown, config?: AxiosRequestConfig) => Promise<T>;
    patch: <T>(url: string, data?: unknown, config?: AxiosRequestConfig) => Promise<T>;
    delete: <T>(url: string, config?: AxiosRequestConfig) => Promise<T>;
    clearError: () => void;
}

export const useApiHook = (): UseApiHookReturn => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const clearError = useCallback(() => {
        setError(null);
    }, []);

    const handleRequest = useCallback(
        //
        async <T>(request: Promise<AxiosResponse<T>>, _config?: AxiosRequestConfig): Promise<T> => {
            try {
                setLoading(true);
                setError(null);
                const response = await request;

                return response.data;
            } catch (err) {
                const axiosErr = err as AxiosError<ErrorResponse>;

                const message = axiosErr.response?.data?.error || axiosErr.message || 'Unexpected error';

                setError(message);
                throw axiosErr;
            } finally {
                setLoading(false);
            }
        },
        []
    );

    const get = useCallback(
        <T>(url: string, config?: AxiosRequestConfig) => {
            return handleRequest<T>(api.get<T>(url, config), { method: 'get', url, ...config });
        },
        [handleRequest]
    );

    const post = useCallback(
        <T>(url: string, data?: unknown, config?: AxiosRequestConfig) => {
            return handleRequest<T>(api.post<T>(url, data, config), { method: 'post', url, data, ...config });
        },
        [handleRequest]
    );

    const put = useCallback(
        <T>(url: string, data?: unknown, config?: AxiosRequestConfig) => {
            return handleRequest<T>(api.put<T>(url, data, config), { method: 'put', url, data, ...config });
        },
        [handleRequest]
    );

    const patch = useCallback(
        <T>(url: string, data?: unknown, config?: AxiosRequestConfig) => {
            return handleRequest<T>(api.patch<T>(url, data, config), { method: 'patch', url, data, ...config });
        },
        [handleRequest]
    );

    const del = useCallback(
        <T>(url: string, config?: AxiosRequestConfig) => {
            return handleRequest<T>(api.delete<T>(url, config), { method: 'delete', url, ...config });
        },
        [handleRequest]
    );

    return {
        loading,
        error,
        api,
        get,
        post,
        put,
        patch,
        delete: del,
        clearError,
    };
};
