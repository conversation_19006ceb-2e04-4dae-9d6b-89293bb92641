import { useContext } from 'react';
import { LayoutContext } from '../../contexts/LayoutContext.tsx';

export function SidebarToggle() {
    const layout = useContext(LayoutContext);
    const isSidebarSm = layout?.isSidebarSm;
    const setIsSidebarSm = layout?.setIsSidebarSm;

    function toggleSidebar() {
        if (setIsSidebarSm) {
            setIsSidebarSm(!isSidebarSm || false);
        }
    }

    return (
        <button
            onClick={() => toggleSidebar()}
            className="border  rounded p-2 shadow-md bg-[var(--grey-color)]/50 hover:bg-[var(--grey-color)]/10"
            aria-label="Toggle sidebar"
        >
            <svg className="w-5 h-5 text-[var(--black-color)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
        </button>
    );
}
