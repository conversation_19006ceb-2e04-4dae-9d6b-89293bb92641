import { useContext } from 'react';
import { LayoutContext } from '../../contexts/LayoutContext.tsx';
import Logo from '../common/Logo.tsx';
import clsx from 'clsx';
import { Link, useLocation, useParams } from 'react-router';
import { type IMenuOption } from '../../constants/menu.tsx';

function Sidebar({ menu, isSuperAdmin = false }: { menu: IMenuOption[]; isSuperAdmin?: boolean }) {
    const { propertyId } = useParams();
    const layout = useContext(LayoutContext);
    const isSidebarSm = layout?.isSidebarSm;
    const location = useLocation();
    const currentRoute = location.pathname;

    const isActive = (link: string) => currentRoute.includes(link);

    return (
        <nav
            aria-label="Sidebar Navigation"
            className="sidebar-main fixed top-0 left-0 h-full shadow-lg transition-all duration-300 ease-in-out w-[var(--sidebar-width)] bg-white z-20"
        >
            <div
                className={clsx(
                    'h-[var(--topbar-height)] w-full flex items-center',
                    isSidebarSm ? 'justify-center' : 'ml-4 justify-start'
                )}
            >
                <Logo size={isSidebarSm ? 'sm' : 'lg'} />
            </div>

            <ul className="text-blue-600 space-y-2 p-3">
                {menu.map((step, i) => {
                    const active = isActive(step.link);
                    return (
                        <li key={i}>
                            <Link
                                to={isSuperAdmin ? `${step.link}` : `/${propertyId}${step.link}`}
                                className={clsx(
                                    'flex items-center p-2 rounded transition-all duration-300 ease-in-out cursor-pointer',
                                    isSidebarSm ? 'justify-center' : '',
                                    active ? 'bg-red-200 text-red-700' : 'hover:bg-red-100 text-black'
                                )}
                            >
                                {step.icon}
                                <span
                                    className={clsx(
                                        'ml-2 transition-all duration-300 ease-in-out',
                                        isSidebarSm && 'w-0 overflow-hidden opacity-0'
                                    )}
                                >
                                    {step.name}
                                </span>
                            </Link>
                        </li>
                    );
                })}
            </ul>
        </nav>
    );
}

export default Sidebar;
