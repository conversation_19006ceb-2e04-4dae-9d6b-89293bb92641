import { SidebarToggle } from './SidebarToggle';
import { useNavigate } from 'react-router';
import useBoundStore from '../../store/useBoundStore.ts';
import UserMenu from '../common/UserMenu.tsx';

function Topbar() {
    const { loggedUser, clearLoggedUser, selectedProperty } = useBoundStore();

    const navigate = useNavigate();
    const logOut = () => {
        clearLoggedUser();
        navigate('/guest', { replace: true });
    };

    return (
        <div className="h-[var(--topbar-height)] w-full fixed pl-[var(--sidebar-width)] shadow-md top-0 right-0 transition-all duration-300 ease-in-out z-10 bg-white">
            <div className="h-full px-4 md:px-6 flex items-center gap-3">
                <SidebarToggle />
                <div className=" flex items-center gap-3 justify-between w-full">
                    {selectedProperty && <h3 className="text-sm ">{selectedProperty.name}</h3>}
                    <UserMenu user={loggedUser.user} onSignOut={logOut} isDashboard={true} />
                </div>
            </div>
        </div>
    );
}

export default Topbar;
