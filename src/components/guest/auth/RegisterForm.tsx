import React from 'react';
import { Formik, Form, type FormikHelpers } from 'formik';
import * as Yup from 'yup';
import { countryCodesOptions } from '../../../utils/helpers/countryCodes.ts';
import { useApiHook } from '../../../hooks/useApi.ts';
import useBoundStore from '../../../store/useBoundStore.ts';
import { useToastHook } from '../../../hooks/useToaster.ts';
import { useNavigate } from 'react-router';
import type { ILoginResponse } from '../../../interfaces/ILoginResponse.ts';
import storageService from '../../../services/storage-service.ts';
import UiLoader from '../../../lib/UiLoader.tsx';
import UiInput from '../../../lib/UiInput.tsx';
import UiSelect from '../../../lib/UiSelect.tsx';
import { allowOnlyNumbers } from '../../../utils/helpers/inputValidation.ts';
import UiButton from '../../../lib/UiButton.tsx';

interface FormValues {
    email: string;
    password: string;
    confirmPassword: string;
    firstName: string;
    lastName: string;
    phone: {
        countryCode: string;
        phoneNumber: string;
    };
}

interface RegisterFormProps {
    redirect?: string | null;
}

const RegisterForm: React.FC<RegisterFormProps> = ({ redirect = null }) => {
    const validationSchema = Yup.object({
        firstName: Yup.string().required('First name is required'),
        lastName: Yup.string().required('Last name is required'),
        email: Yup.string().email('Invalid email address').required('Email is required'),
        phone: Yup.object({
            countryCode: Yup.string().required('Country code is required'),
            phoneNumber: Yup.string()
                .required('Phone Number is Required')
                .test('min-length-by-country', function (value) {
                    const { parent } = this;
                    const selectedCode = countryCodesOptions.find(code => code.value === parent.countryCode);
                    const minLength = selectedCode?.phoneLength || 10;
                    if (!value) return this.createError({ message: 'Phone Number is Required' });
                    if (value.length !== minLength) {
                        return this.createError({
                            message: `Phone number must be exactly ${minLength} digits`,
                        });
                    }
                    return true;
                }),
        }),
        password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
        confirmPassword: Yup.string()
            .oneOf([Yup.ref('password')], 'Passwords must match')
            .required('Confirm password is required'),
    });

    const initialValues = {
        firstName: '',
        lastName: '',
        email: '',
        phone: {
            countryCode: '+91',
            phoneNumber: '',
        },
        password: '',
        confirmPassword: '',
    };

    const { error, post } = useApiHook();
    const { showError, showSuccess } = useToastHook();
    const { updateLoggedUser, updateLoginPopup, clearRegisterPopup } = useBoundStore();
    const navigate = useNavigate();

    const handleSubmit = async (values: FormValues, { setSubmitting }: FormikHelpers<FormValues>) => {
        const body = {
            firstName: values.firstName,
            lastName: values.lastName,
            email: values.email,
            password: values.password,
            phone: {
                countryCode: values.phone.countryCode,
                phoneNumber: values.phone.phoneNumber,
            },
            type: redirect?.includes('onboarding') ? 'merchant' : 'user',
        };

        try {
            const registerResponse = await post<{ message: string }>('/authentication/register', body);

            if (registerResponse) {
                showSuccess('Registration successful.');
                const loginBody = {
                    email: values.email,
                    password: values.password,
                };

                const loginResponse = await post<ILoginResponse>('/authentication/login', loginBody);

                if (loginResponse) {
                    const { token, user } = loginResponse;
                    storageService.setItem('currentUser', loginResponse);
                    updateLoggedUser({ token, user });
                    // const properties = user.properties;
                    // const activeProperties = properties?.filter(
                    //     (property: ICompany) => property.active
                    // );

                    if (redirect) {
                        navigate('/' + redirect);
                    } else {
                        navigate('/');
                    }
                } else {
                    showError('Auto-login failed. Please login manually.');
                    navigate('/guest');
                }
                clearRegisterPopup();
            } else {
                showError('Registration failed');
            }
            setSubmitting(false);
        } catch (err) {
            const e = err as { response?: { data?: { error: string } } };
            showError(error || e?.response?.data?.error || 'Registration failed');
            setSubmitting(false);
        }
    };

    return (
        <>
            <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={handleSubmit}>
                {({ isSubmitting, setFieldValue }) => (
                    <>
                        {isSubmitting && <UiLoader label="Loading..." />}
                        <Form className="space-y-5">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                                <UiInput label="First Name" name="firstName" type="text" placeholder="John" />
                                <UiInput label="Last Name" name="lastName" placeholder="Doe" />
                            </div>

                            <UiInput label="Email Address" name="email" type="email" placeholder="<EMAIL>" />

                            <div className="flex flex-col w-full">
                                <label className="mr-2">Phone Number</label>
                                <div className="flex items-start">
                                    <UiSelect
                                        name="phone.countryCode"
                                        options={countryCodesOptions.map(code => ({
                                            label: code.label,
                                            value: code.value,
                                        }))}
                                        onChange={value => {
                                            setFieldValue('phone.countryCode', value[0] || '');
                                        }}
                                        containerClassName="min-w-[150px]"
                                        inputClassName={'h-10 rounded-lg border-gray-300'}
                                    />
                                    <UiInput
                                        label=""
                                        name="phone.phoneNumber"
                                        placeholder="Enter phone number"
                                        onKeyDown={allowOnlyNumbers}
                                        widthClassname={'w-full border-l-0 rounded-l-none'}
                                    />
                                </div>
                            </div>

                            <UiInput label="Password" name="password" type="password" placeholder="••••••••" />

                            <UiInput
                                label="Confirm Password"
                                name="confirmPassword"
                                type="password"
                                placeholder="••••••••"
                            />

                            <UiButton
                                type="submit"
                                disabled={isSubmitting}
                                className="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center"
                            >
                                {isSubmitting ? (
                                    <>
                                        <svg
                                            className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                        >
                                            <circle
                                                className="opacity-25"
                                                cx="12"
                                                cy="12"
                                                r="10"
                                                stroke="currentColor"
                                                strokeWidth="4"
                                            ></circle>
                                            <path
                                                className="opacity-75"
                                                fill="currentColor"
                                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                            ></path>
                                        </svg>
                                        Processing...
                                    </>
                                ) : (
                                    'Create Account'
                                )}
                            </UiButton>

                            <div className="text-center text-sm text-gray-600 mt-6">
                                Already have an account?{' '}
                                <span
                                    onClick={() => {
                                        clearRegisterPopup();
                                        updateLoginPopup(true);
                                    }}
                                    className="font-medium theme-text transition duration-200 cursor-pointer"
                                >
                                    Sign in
                                </span>
                            </div>
                        </Form>
                    </>
                )}
            </Formik>
        </>
    );
};

export default RegisterForm;
