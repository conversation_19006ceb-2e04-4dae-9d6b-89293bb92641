import { Formik, Form, type FormikHelpers } from 'formik';
import * as Yup from 'yup';
import { useToastHook } from '../../../hooks/useToaster.ts';
import { useApiHook } from '../../../hooks/useApi.ts';
import useBoundStore from '../../../store/useBoundStore.ts';
import UiLoader from '../../../lib/UiLoader.tsx';
import UiInput from '../../../lib/UiInput.tsx';
import UiButton from '../../../lib/UiButton.tsx';

interface FormValues {
    email: string;
}

const ForgotPassword = () => {
    const initialValues: FormValues = {
        email: '',
    };

    const { showSuccess, showError } = useToastHook();
    const { post } = useApiHook();
    const { updateLoginPopup, clearForgotPasswordPopup } = useBoundStore();

    const validationSchema = Yup.object({
        email: Yup.string().email('Invalid email address').required('Email is required'),
    });

    const handleSubmit = async (values: FormValues, { setSubmitting }: FormikHelpers<FormValues>) => {
        try {
            post<string>('/authentication/forgot-password', values);
            showSuccess('Sent Password to your email');
            clearForgotPasswordPopup();
        } catch (_err) {
            showError('Failed to forgot password');
            setSubmitting(false);
        }
    };
    return (
        <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={handleSubmit}>
            {({ isSubmitting }) => (
                <>
                    {isSubmitting && <UiLoader label="Loading..." />}
                    <Form className="space-y-5">
                        <UiInput name="email" type="email" placeholder="Enter your email address" />
                        <UiButton
                            type="submit"
                            disabled={isSubmitting}
                            className="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center"
                        >
                            Send
                        </UiButton>

                        <div className="text-center text-sm text-gray-600 mt-6">
                            <span
                                onClick={() => {
                                    clearForgotPasswordPopup();
                                    updateLoginPopup(true);
                                }}
                                className="font-medium theme-text cursor-pointer"
                            >
                                Login
                            </span>
                        </div>
                    </Form>
                </>
            )}
        </Formik>
    );
};

export default ForgotPassword;
