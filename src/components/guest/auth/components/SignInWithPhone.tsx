import React from 'react';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import UiSelect from '../../../../lib/UiSelect.tsx';
import { countryCodesOptions } from '../../../../utils/helpers/countryCodes.ts';
import UiInput from '../../../../lib/UiInput.tsx';
import { allowOnlyNumbers } from '../../../../utils/helpers/inputValidation.ts';
import UiButton from '../../../../lib/UiButton.tsx';
import UiLoader from '../../../../lib/UiLoader.tsx';

interface PhoneValues {
    phone: { countryCode: string; phoneNumber: string };
    otpCode: string;
}

interface PhoneSignInProps {
    initialPhone?: string;
    onSubmit: (values: PhoneValues) => void;
}

const PhoneSignIn: React.FC<PhoneSignInProps> = ({ initialPhone, onSubmit }) => {
    const initialValues: PhoneValues = {
        phone: {
            countryCode: countryCodesOptions[0]?.value || '+1',
            phoneNumber: initialPhone?.replace(/^\+?\d+/, '') || '',
        },
        otpCode: '',
    };

    const phoneSchema = Yup.object({
        phone: Yup.object({
            countryCode: Yup.string().required('Country code is required'),
            phoneNumber: Yup.string().required('Phone Number is Required'),
        }),
        otpCode: Yup.string().required('OTP is required').length(6, 'OTP must be 6 digits'),
    });

    return (
        <Formik
            initialValues={initialValues}
            validationSchema={phoneSchema}
            onSubmit={(values, { setSubmitting }) => {
                onSubmit(values);
                setSubmitting(false); // Submission handled externally
            }}
        >
            {({ isSubmitting, setFieldValue }) => (
                <Form className="space-y-5">
                    {isSubmitting && <UiLoader label="Loading..." />}
                    <div className="flex flex-col w-full">
                        <label className="mr-2">Phone</label>
                        <div className="flex items-start">
                            <UiSelect
                                name={`phone.countryCode`}
                                options={countryCodesOptions.map((code) => ({
                                    label: code.label,
                                    value: code.value,
                                }))}
                                onChange={(value) => {
                                    setFieldValue(`phone.countryCode`, value[0] || '');
                                }}
                                containerClassName="min-w-[150px] rounded-md"
                                inputClassName={'h-10'}
                            />
                            <UiInput
                                label=""
                                name="phone.phoneNumber"
                                placeholder="234567890"
                                onKeyDown={allowOnlyNumbers}
                                required={true}
                                widthClassname={'w-full rounded-l-none border-l-none'}
                            />
                        </div>
                    </div>
                    <UiInput
                        label="OTP Code"
                        name="otpCode"
                        type="text"
                        placeholder="Enter 6-digit OTP"
                    />
                    <UiButton
                        type="submit"
                        disabled={isSubmitting}
                        className="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200"
                    >
                        {isSubmitting ? 'Verifying...' : 'Verify OTP'}
                    </UiButton>
                </Form>
            )}
        </Formik>
    );
};

export default PhoneSignIn;