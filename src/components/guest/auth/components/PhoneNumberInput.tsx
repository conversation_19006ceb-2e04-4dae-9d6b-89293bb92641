import React from 'react';
import { useField, useFormikContext } from 'formik';
import { countryCodesOptions } from '../../../../utils/helpers/countryCodes.ts';
import { allowOnlyNumbers } from '../../../../utils/helpers/inputValidation.ts';

interface PhoneNumberInputProps {
    name: string;
    label?: string;
    placeholder?: string;
    required?: boolean;
    className?: string;
}

const PhoneNumberInput: React.FC<PhoneNumberInputProps> = ({
    name,
    label = "Phone Number",
    placeholder = "Enter phone number",
    required = false,
    className = ""
}) => {
    const { setFieldValue } = useFormikContext();
    const [field, meta] = useField(name);
    const [countryCodeField] = useField(`${name}CountryCode`);

    // Get the current country code value or default to +1
    const currentCountryCode = countryCodeField.value || '+1';

    const handleCountryCodeChange = (newCountryCode: string) => {
        setFieldValue(`${name}CountryCode`, newCountryCode);
    };

    const handlePhoneNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setFieldValue(name, e.target.value);
    };

    return (
        <div className={`flex flex-col w-full ${className}`}>
            {label && (
                <label className="text-gray-700 font-medium mb-2 text-sm">
                    {label} {required && <span className="text-red-500">*</span>}
                </label>
            )}
            
            <div className="flex items-center border border-gray-300 rounded-lg overflow-hidden bg-gray-50 focus-within:border-blue-500 focus-within:bg-white transition-all duration-200">
                {/* Country Code Dropdown */}
                <div className="relative">
                    <select
                        value={currentCountryCode}
                        onChange={(e) => handleCountryCodeChange(e.target.value)}
                        className="appearance-none bg-transparent border-none outline-none px-3 py-3 pr-8 text-gray-700 font-medium cursor-pointer hover:bg-gray-100 transition-colors duration-200"
                    >
                        {countryCodesOptions.map((option) => (
                            <option key={option.value} value={option.value}>
                                {option.value}
                            </option>
                        ))}
                    </select>
                    {/* Dropdown Arrow */}
                    <div className="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
                        <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                </div>

                {/* Separator */}
                <div className="w-px h-6 bg-gray-300"></div>

                {/* Phone Number Input */}
                <input
                    {...field}
                    type="text"
                    placeholder={placeholder}
                    onChange={handlePhoneNumberChange}
                    onKeyDown={allowOnlyNumbers}
                    className="flex-1 px-3 py-3 bg-transparent border-none outline-none text-gray-700 placeholder-gray-400"
                />
            </div>

            {/* Error Message */}
            {meta.touched && meta.error && (
                <div className="text-red-500 text-sm mt-1">{meta.error}</div>
            )}
        </div>
    );
};

export default PhoneNumberInput;
