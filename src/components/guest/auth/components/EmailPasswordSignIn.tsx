import React from 'react';
import { Form, Formik, } from 'formik';
import * as Yup from 'yup';
import UiInput from '../../../../lib/UiInput.tsx';
import UiButton from '../../../../lib/UiButton.tsx';
import UiLoader from '../../../../lib/UiLoader.tsx';

interface EmailPasswordValues {
    email: string;
    password: string;
}

interface EmailPasswordSignInProps {
    email: string;
    onSubmit: (values: EmailPasswordValues) => void;
}

const EmailPasswordSignIn: React.FC<EmailPasswordSignInProps> = ({ email, onSubmit }) => {
    const initialValues: EmailPasswordValues = {
        email,
        password: '',
    };

    const validationSchema = Yup.object({
        email: Yup.string().email('Invalid email address').required('Email is required'),
        password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
    });

    return (
        <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={(values, { setSubmitting }) => {
                onSubmit(values);
                setSubmitting(false); // Submission handled externally
            }}
        >
            {({ isSubmitting }) => (
                <Form className="space-y-5">
                    {isSubmitting && <UiLoader label="Loading..." />}
                    <UiInput
                        label="Email Address"
                        name="email"
                        type="email"
                        placeholder="<EMAIL>"
                        disabled={true} // Pre-filled from initial step
                    />
                    <UiInput
                        label="Password"
                        name="password"
                        type="password"
                        placeholder="••••••••"
                    />
                    <UiButton
                        type="submit"
                        disabled={isSubmitting}
                        className="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200"
                    >
                        {isSubmitting ? 'Signing in...' : 'Sign in'}
                    </UiButton>
                </Form>
            )}
        </Formik>
    );
};

export default EmailPasswordSignIn;