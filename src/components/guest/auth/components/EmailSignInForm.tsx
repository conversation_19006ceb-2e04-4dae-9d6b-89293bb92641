import React from 'react';
import { Form, Formik, type FormikHelpers } from 'formik';
import * as Yup from 'yup';
import { XMarkIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import UiInput from '../../../../lib/UiInput';

interface EmailSignInFormValues {
    email: string;
    password: string;
}

interface EmailSignInFormProps {
    onClose?: () => void;
    onBack: () => void;
    onSubmit: (values: EmailSignInFormValues) => void;
    onForgotPassword: () => void;
    isSubmitting?: boolean;
}

const EmailSignInForm: React.FC<EmailSignInFormProps> = ({
    onClose,
    onBack,
    onSubmit,
    onForgotPassword,
    isSubmitting = false
}) => {
    const initialValues: EmailSignInFormValues = {
        email: '',
        password: ''
    };

    const validationSchema = Yup.object({
        email: Yup.string()
            .email('Invalid email address')
            .required('Email is required'),
        password: Yup.string()
            .min(6, 'Password must be at least 6 characters')
            .required('Password is required')
    });

    const handleSubmit = (values: EmailSignInFormValues, { setSubmitting }: FormikHelpers<EmailSignInFormValues>) => {
        onSubmit(values);
        setSubmitting(false);
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-3xl shadow-2xl w-full max-w-md mx-auto relative">
                {/* Header with Back and Close buttons */}
                <div className="flex items-center justify-between p-6 pb-0">
                    <button
                        onClick={onBack}
                        className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                    >
                        <ArrowLeftIcon className="w-6 h-6" />
                    </button>
                    
                    {onClose && (
                        <button
                            onClick={onClose}
                            className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                        >
                            <XMarkIcon className="w-6 h-6" />
                        </button>
                    )}
                </div>

                <div className="p-8 pt-4">
                    {/* Header */}
                    <div className="text-center mb-8">
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">Welcome Back</h1>
                        <p className="text-gray-500 text-lg">Sign in with your email</p>
                    </div>

                    <Formik
                        initialValues={initialValues}
                        validationSchema={validationSchema}
                        onSubmit={handleSubmit}
                    >
                        {({ isSubmitting: formikSubmitting }) => (
                            <Form className="space-y-6">
                                {/* Email Input */}
                                <UiInput
                                    label="Email Address"
                                    name="email"
                                    type="email"
                                    placeholder="Enter your email"
                                    required
                                />

                                {/* Password Input */}
                                <UiInput
                                    label="Password"
                                    name="password"
                                    type="password"
                                    placeholder="Enter your password"
                                    required
                                />

                                {/* Forgot Password Link */}
                                <div className="flex justify-end">
                                    <button
                                        type="button"
                                        onClick={onForgotPassword}
                                        className="text-blue-600 hover:text-blue-700 text-sm font-medium transition-colors duration-200"
                                    >
                                        Forgot password?
                                    </button>
                                </div>

                                {/* Sign In Button */}
                                <button
                                    type="submit"
                                    disabled={isSubmitting || formikSubmitting}
                                    className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-[1.02] disabled:scale-100 disabled:cursor-not-allowed"
                                >
                                    {isSubmitting || formikSubmitting ? (
                                        <div className="flex items-center justify-center">
                                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                                            Signing in...
                                        </div>
                                    ) : (
                                        'Sign In'
                                    )}
                                </button>
                            </Form>
                        )}
                    </Formik>
                </div>
            </div>
        </div>
    );
};

export default EmailSignInForm;
