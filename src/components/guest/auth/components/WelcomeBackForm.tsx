import React, { useState } from 'react';
import { Form, Formik, type FormikHelpers } from 'formik';
import * as Yup from 'yup';
import PhoneNumberInput from './PhoneNumberInput';
import { XMarkIcon } from '@heroicons/react/24/outline';

interface WelcomeBackFormValues {
    phoneNumber: string;
    phoneNumberCountryCode: string;
}

interface WelcomeBackFormProps {
    onClose?: () => void;
    onPhoneSubmit: (values: { phone: string; countryCode: string }) => void;
    onEmailSignIn: () => void;
    onGoogleSignIn: () => void;
    isSubmitting?: boolean;
}

const WelcomeBackForm: React.FC<WelcomeBackFormProps> = ({
    onClose,
    onPhoneSubmit,
    onEmailSignIn,
    onGoogleSignIn,
    isSubmitting = false
}) => {
    const initialValues: WelcomeBackFormValues = {
        phoneNumber: '',
        phoneNumberCountryCode: '+1'
    };

    const validationSchema = Yup.object({
        phoneNumber: Yup.string()
            .required('Phone number is required')
            .min(7, 'Phone number must be at least 7 digits'),
        phoneNumberCountryCode: Yup.string().required('Country code is required')
    });

    const handleSubmit = (values: WelcomeBackFormValues, { setSubmitting }: FormikHelpers<WelcomeBackFormValues>) => {
        onPhoneSubmit({
            phone: values.phoneNumber,
            countryCode: values.phoneNumberCountryCode
        });
        setSubmitting(false);
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-3xl shadow-2xl w-full max-w-md mx-auto relative">
                {/* Close Button */}
                {onClose && (
                    <button
                        onClick={onClose}
                        className="absolute top-6 right-6 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                    >
                        <XMarkIcon className="w-6 h-6" />
                    </button>
                )}

                <div className="p-8 pt-12">
                    {/* Header */}
                    <div className="text-center mb-8">
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">Welcome Back</h1>
                        <p className="text-gray-500 text-lg">Sign in to your account</p>
                    </div>

                    <Formik
                        initialValues={initialValues}
                        validationSchema={validationSchema}
                        onSubmit={handleSubmit}
                    >
                        {({ isSubmitting: formikSubmitting }) => (
                            <Form className="space-y-6">
                                {/* Phone Number Input */}
                                <PhoneNumberInput
                                    name="phoneNumber"
                                    label="Phone Number"
                                    placeholder="Enter phone number"
                                    required
                                />

                                {/* Continue Button */}
                                <button
                                    type="submit"
                                    disabled={isSubmitting || formikSubmitting}
                                    className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-[1.02] disabled:scale-100 disabled:cursor-not-allowed"
                                >
                                    {isSubmitting || formikSubmitting ? (
                                        <div className="flex items-center justify-center">
                                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                                            Sending...
                                        </div>
                                    ) : (
                                        'Continue'
                                    )}
                                </button>
                            </Form>
                        )}
                    </Formik>

                    {/* Divider */}
                    <div className="flex items-center my-8">
                        <div className="flex-1 border-t border-gray-200"></div>
                        <span className="px-4 text-gray-500 text-sm">or continue with</span>
                        <div className="flex-1 border-t border-gray-200"></div>
                    </div>

                    {/* Alternative Sign In Options */}
                    <div className="grid grid-cols-2 gap-4">
                        {/* Email Button */}
                        <button
                            onClick={onEmailSignIn}
                            className="flex items-center justify-center space-x-2 bg-white border border-gray-300 rounded-xl py-3 px-4 hover:bg-gray-50 transition-all duration-200 transform hover:scale-[1.02]"
                        >
                            <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            <span className="text-gray-700 font-medium">Email</span>
                        </button>

                        {/* Google Button */}
                        <button
                            onClick={onGoogleSignIn}
                            className="flex items-center justify-center space-x-2 bg-white border border-gray-300 rounded-xl py-3 px-4 hover:bg-gray-50 transition-all duration-200 transform hover:scale-[1.02]"
                        >
                            <svg className="w-5 h-5" viewBox="0 0 24 24">
                                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                            </svg>
                            <span className="text-gray-700 font-medium">Google</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default WelcomeBackForm;
