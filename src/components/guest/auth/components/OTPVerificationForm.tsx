import React, { useState, useEffect } from 'react';
import { Form, Formik, type FormikHelpers } from 'formik';
import * as Yup from 'yup';
import { XMarkIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';

interface OTPVerificationFormValues {
    otpCode: string;
}

interface OTPVerificationFormProps {
    onClose?: () => void;
    onBack: () => void;
    onSubmit: (otpCode: string) => void;
    onResendOTP: () => void;
    phoneNumber: string;
    countryCode: string;
    isSubmitting?: boolean;
}

const OTPVerificationForm: React.FC<OTPVerificationFormProps> = ({
    onClose,
    onBack,
    onSubmit,
    onResendOTP,
    phoneNumber,
    countryCode,
    isSubmitting = false
}) => {
    const [countdown, setCountdown] = useState(60);
    const [canResend, setCanResend] = useState(false);

    useEffect(() => {
        if (countdown > 0) {
            const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
            return () => clearTimeout(timer);
        } else {
            setCanResend(true);
        }
    }, [countdown]);

    const initialValues: OTPVerificationFormValues = {
        otpCode: ''
    };

    const validationSchema = Yup.object({
        otpCode: Yup.string()
            .required('OTP is required')
            .length(6, 'OTP must be 6 digits')
            .matches(/^\d+$/, 'OTP must contain only numbers')
    });

    const handleSubmit = (values: OTPVerificationFormValues, { setSubmitting }: FormikHelpers<OTPVerificationFormValues>) => {
        onSubmit(values.otpCode);
        setSubmitting(false);
    };

    const handleResendOTP = () => {
        onResendOTP();
        setCountdown(60);
        setCanResend(false);
    };

    const formatPhoneNumber = () => {
        return `${countryCode} ${phoneNumber}`;
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-3xl shadow-2xl w-full max-w-md mx-auto relative">
                {/* Header with Back and Close buttons */}
                <div className="flex items-center justify-between p-6 pb-0">
                    <button
                        onClick={onBack}
                        className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                    >
                        <ArrowLeftIcon className="w-6 h-6" />
                    </button>
                    
                    {onClose && (
                        <button
                            onClick={onClose}
                            className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                        >
                            <XMarkIcon className="w-6 h-6" />
                        </button>
                    )}
                </div>

                <div className="p-8 pt-4">
                    {/* Header */}
                    <div className="text-center mb-8">
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">Verify Your Phone</h1>
                        <p className="text-gray-500 text-lg mb-2">
                            We've sent a 6-digit code to
                        </p>
                        <p className="text-gray-900 font-semibold">
                            {formatPhoneNumber()}
                        </p>
                    </div>

                    <Formik
                        initialValues={initialValues}
                        validationSchema={validationSchema}
                        onSubmit={handleSubmit}
                    >
                        {({ isSubmitting: formikSubmitting, values, setFieldValue }) => (
                            <Form className="space-y-6">
                                {/* OTP Input */}
                                <div className="flex flex-col">
                                    <label className="text-gray-700 font-medium mb-2 text-sm">
                                        Enter 6-digit code
                                    </label>
                                    <input
                                        type="text"
                                        value={values.otpCode}
                                        onChange={(e) => {
                                            const value = e.target.value.replace(/\D/g, '').slice(0, 6);
                                            setFieldValue('otpCode', value);
                                        }}
                                        placeholder="000000"
                                        className="w-full px-4 py-4 text-center text-2xl font-mono tracking-widest border border-gray-300 rounded-xl bg-gray-50 focus:border-blue-500 focus:bg-white transition-all duration-200 outline-none"
                                        maxLength={6}
                                    />
                                </div>

                                {/* Resend OTP */}
                                <div className="text-center">
                                    {canResend ? (
                                        <button
                                            type="button"
                                            onClick={handleResendOTP}
                                            className="text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200"
                                        >
                                            Resend Code
                                        </button>
                                    ) : (
                                        <p className="text-gray-500">
                                            Resend code in {countdown}s
                                        </p>
                                    )}
                                </div>

                                {/* Verify Button */}
                                <button
                                    type="submit"
                                    disabled={isSubmitting || formikSubmitting || values.otpCode.length !== 6}
                                    className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-[1.02] disabled:scale-100 disabled:cursor-not-allowed"
                                >
                                    {isSubmitting || formikSubmitting ? (
                                        <div className="flex items-center justify-center">
                                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                                            Verifying...
                                        </div>
                                    ) : (
                                        'Verify Code'
                                    )}
                                </button>
                            </Form>
                        )}
                    </Formik>
                </div>
            </div>
        </div>
    );
};

export default OTPVerificationForm;
