import React, {useState} from 'react';
import {Form, Formik, type FormikHelpers} from 'formik';
import * as Yup from 'yup';
import {useApiHook} from '../../../hooks/useApi';
import {useToastHook} from '../../../hooks/useToaster.ts';
import useBoundStore from '../../../store/useBoundStore.ts';
import {useNavigate} from 'react-router';
import type {ILoginResponse} from '../../../interfaces/ILoginResponse.ts';
import storageService from '../../../services/storage-service.ts';
import UiLoader from '../../../lib/UiLoader.tsx';
import UiInput from '../../../lib/UiInput.tsx';
import UiButton from '../../../lib/UiButton.tsx';
import EmailPasswordSignIn from './components/EmailPasswordSignIn';
import PhoneSignIn from './components/SignInWithPhone';
import GoogleSignIn from './components/GoogleSignIn';
import WelcomeBackForm from './components/WelcomeBackForm';
import EmailSignInForm from './components/EmailSignInForm';
import OTPVerificationForm from './components/OTPVerificationForm';
import {
    signInWithPopup,
    signInWithPhoneNumber,
    GoogleAuthProvider,
    RecaptchaVerifier,
    type ConfirmationResult
} from "firebase/auth";
import {auth} from "../../../config/firebase.ts";

interface FormValues {
    emailOrPhone: string;
    password: string;
}

interface LoginFormProps {
    isChild?: boolean;
    redirect?: string | null;
}

type AuthStep = 'welcome' | 'email' | 'otp';

interface PhoneData {
    phone: string;
    countryCode: string;
}

const LoginForm: React.FC<LoginFormProps> = ({isChild = false, redirect = null}) => {
    const initialValues: FormValues = {
        emailOrPhone: '',
        password: '',
    };

    const { post} = useApiHook();
    const {showError, showSuccess} = useToastHook();
    const {
        updateLoggedUser,
        updateForgotPasswordPOpup,
        updateProperty,
        clearLoggedUser,
        updateRegisterPopup,
        clearLoginPopup,
    } = useBoundStore();
    const navigate = useNavigate();
    const [loginType, setLoginType] = useState<'guest' | 'partner'>('guest');
    const [isNextStep, setIsNextStep] = useState(false);
    const [isPhone, setIsPhone] = useState(false);
    const [confirmationResult, setConfirmationResult] = useState<ConfirmationResult | null>(null);

    // New state for the modern UI
    const [currentStep, setCurrentStep] = useState<AuthStep>('welcome');
    const [phoneData, setPhoneData] = useState<PhoneData>({ phone: '', countryCode: '+1' });
    const [isSubmitting, setIsSubmitting] = useState(false);

    const emailPasswordSchema = Yup.object({
        emailOrPhone: Yup.string().required('Email or phone number is required'),
    });

    const validationSchema = loginType === 'partner' ? emailPasswordSchema : null; // Initial step validation

    const handlePhoneSubmit = async (values: {
        phone: { countryCode: string; phoneNumber: string };
        otpCode: string
    }) => {
        if (!isNextStep) {
            try {
                const fullPhoneNumber = `${values.phone.countryCode}${values.phone.phoneNumber}`;

                await handleSendPhoneOtp(fullPhoneNumber);
            } catch (_err) {
                showError('Invalid OTP code');
            }
        } else {
            await handleVerifyOtp(values.otpCode);
        }
    };

    const handleSendPhoneOtp = async (phoneNumber: string) => {
        try {
            const appVerifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
                size: 'invisible',
            });
            const result = await signInWithPhoneNumber(auth, phoneNumber, appVerifier);
            setConfirmationResult(result);
            setIsNextStep(true);
        } catch (err) {
            showError('An error occurred while sending OTP');
        }
    };

    const handleVerifyOtp = async (otpCode: string) => {
        if (!confirmationResult) return;
        try {
            const result = await confirmationResult.confirm(otpCode);
            const accessToken = await result.user.getIdToken();
            console.log('🐞accessToken: ', accessToken);
            // Optionally, send token to backend for validation
            const response = await post<ILoginResponse>('/authentication/phone-login', {token: accessToken});
            if (response) {
                handleLoginSuccess(response);
            } else {
                showError('Phone login failed');
            }
        } catch (_err) {
            showError('Invalid OTP code');
        }
    };

    const handleGoogleSignIn = async () => {
        try {
            const provider = new GoogleAuthProvider();
            const res = await signInWithPopup(auth, provider);
            const accessToken = await res.user.getIdToken();
            console.log('🐞accessToken: ', accessToken);
            const response = await post<ILoginResponse>('/authentication/google-login', {token: 'google-token'}); // Replace with actual token
            if (response) {
                handleLoginSuccess(response);
            } else {
                showError('Google login failed');
            }
        } catch (err) {
            showError('An error occurred during Google login');
        }
    };

    const handleEmailPasswordSubmit = async (values: { email: string; password: string }) => {
        try {
            const response = await post<ILoginResponse>('/authentication/login', {
                email: values.email,
                password: values.password,
            });
            if (response) {
                handleLoginSuccess(response);
            } else {
                showError('Login failed');
            }
        } catch (err) {
            showError('An error occurred during login');
        }
    };

    const handleLoginSuccess = (response: ILoginResponse) => {
        const {token, user} = response;
        storageService.setItem('currentUser', response);
        updateLoggedUser({token, user});
        const properties = user.properties;
        const activeProperties = properties?.filter((property) => property.active);
        showSuccess('Login successful!');

        if (user.role.includes('superadmin')) {
            navigate(`/admin/live-tracking`);
        } else if (loginType === 'partner') {
            if (activeProperties?.length) {
                updateProperty(activeProperties[0]);
                navigate(`/${activeProperties[0]._id}/merchant/live-tracking`);
            } else {
                showError('No active properties found');
                clearLoggedUser();
            }
        } else if (redirect) {
            navigate('/' + redirect);
        } else {
            navigate('/');
        }
        clearLoginPopup();
    };

    const handleModernPhoneSubmit = async (data: PhoneData) => {
        setIsSubmitting(true);
        try {
            const fullPhoneNumber = `${data.countryCode}${data.phone}`;

            const appVerifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
                size: 'invisible',
            });

            const result = await signInWithPhoneNumber(auth, fullPhoneNumber, appVerifier);
            setConfirmationResult(result);
            setPhoneData(data);
            setCurrentStep('otp');
            showSuccess('OTP sent successfully!');
        } catch (error) {
            showError('Failed to send OTP. Please try again.');
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleModernOTPSubmit = async (otpCode: string) => {
        if (!confirmationResult) {
            showError('Please request a new OTP');
            return;
        }

        setIsSubmitting(true);
        try {
            const result = await confirmationResult.confirm(otpCode);
            const accessToken = await result.user.getIdToken();

            const response = await post<ILoginResponse>('/authentication/phone-login', {
                token: accessToken,
                phoneNumber: `${phoneData.countryCode}${phoneData.phone}`
            });

            if (response) {
                handleLoginSuccess(response);
            } else {
                showError('Phone login failed');
            }
        } catch (error) {
            showError('Invalid OTP code');
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleModernEmailSubmit = async (values: { email: string; password: string }) => {
        setIsSubmitting(true);
        try {
            const response = await post<ILoginResponse>('/authentication/login', values);
            if (response) {
                handleLoginSuccess(response);
            } else {
                showError('Invalid email or password');
            }
        } catch (error) {
            showError('Login failed. Please try again.');
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleModernGoogleSignIn = async () => {
        setIsSubmitting(true);
        try {
            const provider = new GoogleAuthProvider();
            const result = await signInWithPopup(auth, provider);
            const accessToken = await result.user.getIdToken();

            const response = await post<ILoginResponse>('/authentication/google-login', {
                token: accessToken
            });

            if (response) {
                handleLoginSuccess(response);
            } else {
                showError('Google login failed');
            }
        } catch (error) {
            showError('Google login failed. Please try again.');
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleResendOTP = async () => {
        await handleModernPhoneSubmit(phoneData);
    };

    const handleForgotPassword = () => {
        updateForgotPasswordPOpup(true);
        clearLoginPopup();
    };

    const resetToWelcome = () => {
        setCurrentStep('welcome');
        setPhoneData({ phone: '', countryCode: '+1' });
        setConfirmationResult(null);
    };

    const handleSubmit = async (values: FormValues, {setSubmitting}: FormikHelpers<FormValues>) => {
        if (!isNextStep && loginType === 'guest') {
            const isEmail = values.emailOrPhone.includes('@');
            const isPhoneNumber = /^\+?[1-9]\d{1,14}$/.test(values.emailOrPhone);
            if (isPhoneNumber) {
                setIsPhone(true);
                setIsNextStep(true);
            } else if (isEmail) {
                setIsPhone(false);
                setIsNextStep(true);
            } else {
                showError('Please enter a valid email or phone number');
                setSubmitting(false);
                return;
            }
        } else if (isPhone) {
            setSubmitting(false);
        } else {
            handleEmailPasswordSubmit({email: values.emailOrPhone, password: values.password});
            setSubmitting(false);
        }
    };

    return (
        <>
            {loginType === 'guest' && !isChild && (
                <>
                    {currentStep === 'welcome' && (
                        <WelcomeBackForm
                            onPhoneSubmit={handleModernPhoneSubmit}
                            onEmailSignIn={() => setCurrentStep('email')}
                            onGoogleSignIn={handleModernGoogleSignIn}
                            isSubmitting={isSubmitting}
                        />
                    )}

                    {currentStep === 'email' && (
                        <EmailSignInForm
                            onBack={resetToWelcome}
                            onSubmit={handleModernEmailSubmit}
                            onForgotPassword={handleForgotPassword}
                            isSubmitting={isSubmitting}
                        />
                    )}

                    {currentStep === 'otp' && (
                        <OTPVerificationForm
                            onBack={resetToWelcome}
                            onSubmit={handleModernOTPSubmit}
                            onResendOTP={handleResendOTP}
                            phoneNumber={phoneData.phone}
                            countryCode={phoneData.countryCode}
                            isSubmitting={isSubmitting}
                        />
                    )}
                </>
            )}

            {/* Traditional UI for Partner Login and Child Components - PRESERVED AS IS */}
            {(loginType === 'partner' || isChild || (loginType === 'guest' && isChild)) && (
                <>
                    {!isChild && (
                        <>
                            {!redirect && (
                                <div className="flex justify-center mb-6 space-x-4">
                                    {['guest', 'partner'].map((type) => (
                                        <button
                                            key={type}
                                            className={`relative px-6 py-2 font-semibold text-md rounded-md transition-all duration-300 ease-in-out
                            ${
                                                loginType === type
                                                    ? 'text-[var(--secondary-color)]'
                                                    : 'text-gray-500 hover:text-[var(--secondary-color)]'
                                            }`}
                                            onClick={() => {
                                                setLoginType(type as 'guest' | 'partner');
                                                setIsNextStep(false);
                                                setIsPhone(false);
                                                setCurrentStep('welcome');
                                            }}
                                        >
                                            {type === 'guest' ? 'Guest Login' : 'Partner Login'}
                                            <span
                                                className={`absolute left-1/2 transform -translate-x-1/2 bottom-0 h-[2px] w-full rounded-full transition-all duration-300 ease-in-out
                              ${loginType === type ? 'bg-[var(--secondary-color)] w-full' : 'bg-transparent w-0 group-hover:w-full'}`}
                                            />
                                        </button>
                                    ))}
                                </div>
                            )}
                        </>
                    )}

                    <div className="space-y-4">
                        {loginType === 'guest' && (
                            <div className="flex flex-col space-y-2">
                                <GoogleSignIn handleGoogleSignIn={handleGoogleSignIn}/>
                            </div>
                        )}

                        <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={handleSubmit}>
                            {({isSubmitting, values}) => (
                                <>
                                    {isSubmitting && <UiLoader label="Loading..."/>}
                                    <Form className="space-y-5">
                                        {!isNextStep && (
                                            <UiInput
                                                label="Email or Mobile Number"
                                                name="emailOrPhone"
                                                type="text"
                                                placeholder="<EMAIL> or +1234567890"
                                            />
                                        )}
                                        {isNextStep && !isPhone && (
                                            <EmailPasswordSignIn
                                                email={values.emailOrPhone}
                                                onSubmit={handleEmailPasswordSubmit}
                                            />
                                        )}
                                        {isNextStep && isPhone && (
                                            <PhoneSignIn
                                                initialPhone={values.emailOrPhone}
                                                onSubmit={handlePhoneSubmit}
                                            />
                                        )}

                                        {loginType === 'partner' && !isNextStep && (
                                            <EmailPasswordSignIn
                                                email={values.emailOrPhone}
                                                onSubmit={handleEmailPasswordSubmit}
                                            />
                                        )}

                                        {(loginType === 'partner' || (isNextStep && !isPhone)) && (
                                            <div className="flex items-center justify-end">
                                                <div className="text-sm theme-text">
                              <span
                                  className="cursor-pointer"
                                  onClick={() => {
                                      updateForgotPasswordPOpup(true);
                                      clearLoginPopup();
                                  }}
                              >
                                Forgot password?
                              </span>
                                                </div>
                                            </div>
                                        )}

                                        <UiButton
                                            type="submit"
                                            disabled={isSubmitting}
                                            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center"
                                        >
                                            {isSubmitting ? (
                                                <>
                                                    <svg
                                                        className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        fill="none"
                                                        viewBox="0 0 24 24"
                                                    >
                                                        <circle
                                                            className="opacity-25"
                                                            cx="12"
                                                            cy="12"
                                                            r="10"
                                                            stroke="currentColor"
                                                            strokeWidth="4"
                                                        ></circle>
                                                        <path
                                                            className="opacity-75"
                                                            fill="currentColor"
                                                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                                        ></path>
                                                    </svg>
                                                    Signing in...
                                                </>
                                            ) : isNextStep ? (
                                                'Verify'
                                            ) : (
                                                'Next'
                                            )}
                                        </UiButton>

                                        {loginType === 'guest' && !isNextStep && (
                                            <div className="text-center text-sm text-gray-600 mt-6">
                                                Don't have an account?{' '}
                                                <span
                                                    onClick={() => {
                                                        clearLoginPopup();
                                                        updateRegisterPopup(true);
                                                    }}
                                                    className="font-medium theme-text cursor-pointer"
                                                >
                              Register now
                            </span>
                                            </div>
                                        )}
                                    </Form>
                                </>
                            )}
                        </Formik>
                    </div>
                </>
            )}

            <div id="recaptcha-container"></div>
        </>
    );
};

export default LoginForm;