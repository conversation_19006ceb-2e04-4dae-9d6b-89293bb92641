import { Formik, Form, type FormikHelpers } from 'formik';
import * as Yup from 'yup';
import { countryCodesOptions } from '../../../utils/helpers/countryCodes.ts';
import useBoundStore from '../../../store/useBoundStore.ts';
import { useToastHook } from '../../../hooks/useToaster.ts';
import { useApiHook } from '../../../hooks/useApi.ts';
import type { IUser } from '../../../interfaces/IUser.ts';
import storageService from '../../../services/storage-service.ts';
import UiLoader from '../../../lib/UiLoader.tsx';
import UiInput from '../../../lib/UiInput.tsx';
import UiSelect from '../../../lib/UiSelect.tsx';
import { allowOnlyNumbers } from '../../../utils/helpers/inputValidation.ts';
import UiButton from '../../../lib/UiButton.tsx';
import type { ProfilePopup } from '../../../constants/variables.ts';

interface FormValues {
    email: string;
    firstName: string;
    lastName: string;
    phone: {
        countryCode: string;
        phoneNumber: string;
    };
}

interface RegisterFormProps {
    setPopup: (popup: ProfilePopup | null) => void;
}

const validationSchema = Yup.object({
    firstName: Yup.string().required('First name is required'),
    lastName: Yup.string().required('Last name is required'),
    email: Yup.string().email('Invalid email address').required('Email is required'),
    phone: Yup.object({
        countryCode: Yup.string().required('Country code is required'),
        phoneNumber: Yup.string()
            .required('Phone Number is Required')
            .test('min-length-by-country', function (value) {
                const { parent } = this;
                const selectedCode = countryCodesOptions.find(code => code.value === parent.countryCode);
                const minLength = selectedCode?.phoneLength || 10;
                if (!value) return this.createError({ message: 'Phone Number is Required' });
                if (value.length !== minLength) {
                    return this.createError({
                        message: `Phone number must be exactly ${minLength} digits`,
                    });
                }
                return true;
            }),
    }),
});

const EditProfile = ({ setPopup }: RegisterFormProps) => {
    const { loggedUser, updateLoggedUser } = useBoundStore();
    const userData = loggedUser.user;
    const { showError, showSuccess } = useToastHook();
    const { patch } = useApiHook();

    const initialValues = {
        firstName: userData.firstName || '',
        lastName: userData.lastName || '',
        email: userData.email || '',
        phone: {
            countryCode: userData.phone.countryCode || '+91',
            phoneNumber: userData.phone.phoneNumber || '',
        },
    };

    const handleSubmit = async (values: FormValues, { setSubmitting }: FormikHelpers<FormValues>) => {
        try {
            const { email: _email, ...rest } = values;
            setSubmitting(true);
            const user = await patch<IUser>(`users/${userData.id}`, rest, {
                headers: { Authorization: `Bearer ${loggedUser.token}` },
            });
            storageService.setItem('currentUser', { token: loggedUser.token, user });
            updateLoggedUser({ token: loggedUser.token, user });
            setPopup(null);
            showSuccess('Profile updated successfully');
        } catch (_e) {
            showError('Failed to update profile. Please try again later');
        } finally {
            setSubmitting(false);
        }
    };

    return (
        <div className={'p-4'}>
            <Formik
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
                enableReinitialize
            >
                {({ isSubmitting, setFieldValue }) => (
                    <>
                        {isSubmitting && <UiLoader label="Loading..." />}
                        <Form className="">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                                <UiInput label="First Name" name="firstName" type="text" placeholder="John" />
                                <UiInput label="Last Name" name="lastName" placeholder="Doe" />
                            </div>

                            <UiInput
                                label="Email Address"
                                name="email"
                                type="email"
                                placeholder="<EMAIL>"
                                disabled
                            />

                            <div className="flex flex-col w-full">
                                <label className="mr-2">Phone Number</label>
                                <div className="flex items-start">
                                    <UiSelect
                                        name="phone.countryCode"
                                        options={countryCodesOptions.map(code => ({
                                            label: code.label,
                                            value: code.value,
                                        }))}
                                        onChange={value => {
                                            setFieldValue('phone.countryCode', value[0] || '');
                                        }}
                                        containerClassName="min-w-[150px]"
                                    />
                                    <UiInput
                                        label=""
                                        name="phone.phoneNumber"
                                        placeholder="Enter phone number"
                                        onKeyDown={allowOnlyNumbers}
                                    />
                                </div>
                            </div>

                            <UiButton
                                type="submit"
                                disabled={isSubmitting}
                                className="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center"
                            >
                                {isSubmitting ? (
                                    <>
                                        <svg
                                            className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                        >
                                            <circle
                                                className="opacity-25"
                                                cx="12"
                                                cy="12"
                                                r="10"
                                                stroke="currentColor"
                                                strokeWidth="4"
                                            ></circle>
                                            <path
                                                className="opacity-75"
                                                fill="currentColor"
                                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                            ></path>
                                        </svg>
                                        Processing...
                                    </>
                                ) : (
                                    'Submit'
                                )}
                            </UiButton>
                        </Form>
                    </>
                )}
            </Formik>
        </div>
    );
};

export default EditProfile;
