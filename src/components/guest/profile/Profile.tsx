import { EnvelopeIcon, PhoneIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';
import useBoundStore from '../../../store/useBoundStore.ts';
import UiPopup from '../../../lib/UiPopup.tsx';
import EditProfile from './EditProfile.tsx';
import { ProfilePopup } from '../../../constants/variables.ts';
import ResetPassword from './ResetPassword.tsx';

const Profile = () => {
    const { loggedUser } = useBoundStore();
    const user = loggedUser.user;
    const [popup, setPopup] = useState<ProfilePopup | null>(null);

    const popupTitle = popup === ProfilePopup.RESET ? 'Reset Password' : 'Edit Profile';
    return (
        <>
            <div className="max-w-md w-full bg-white rounded-lg shadow-lg px-2 py-4 sticky top-20">
                <div className="flex flex-col items-center w-full">
                    <div className="theme-background h-20 w-20 flex items-center justify-center rounded-full text-white text-2xl font-bold ">
                        {(user?.firstName?.[0].toUpperCase() || '') + (user?.lastName?.[0].toUpperCase() || '')}
                    </div>
                    <h2 className="mt-2 text-3xl font-extrabold text-gray-900 tracking-tight capitalize">
                        {(user?.firstName || '') + ' ' + (user?.lastName || '')}
                    </h2>
                    <p className="mt-2 text-gray-600 text-sm">@{user?.username || 'user'}</p>
                </div>

                <div className="mt-2 flex flex-col items-center gap-3">
                    <div className="flex items-center">
                        <EnvelopeIcon className="h-5 w-5 text-orange-500 mr-2" />
                        <p className="text-gray-700">{user.email}</p>
                    </div>
                    <div className="flex items-center">
                        <PhoneIcon className="h-5 w-5 text-orange-500 mr-2" />
                        <p className="text-gray-700">{`${user.phone?.countryCode} ${user.phone?.phoneNumber}`}</p>
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="w-full mt-4 flex items-center justify-center gap-3">
                    <button
                        className="theme-background text-white px-4 py-2.5 rounded-md text-sm hover:bg-blue-700 transition-colors"
                        onClick={() => setPopup(ProfilePopup.EDIT)}
                    >
                        Edit Profile
                    </button>
                    <button
                        className="text-orange-600 px-4 py-2.5 rounded-md text-sm hover:bg-orange-100 transition-colors"
                        onClick={() => setPopup(ProfilePopup.RESET)}
                    >
                        Reset Password
                    </button>
                </div>
            </div>
            <UiPopup isOpen={!!popup} onClose={() => setPopup(null)} title={popupTitle} width={'w-150'}>
                {popup === ProfilePopup.EDIT ? (
                    <EditProfile setPopup={setPopup} />
                ) : (
                    <ResetPassword setPopup={setPopup} />
                )}
            </UiPopup>
        </>
    );
};

export default Profile;
