import { Formik, Form, type FormikHelpers } from 'formik';
import * as Yup from 'yup';
import type { ProfilePopup } from '../../../constants/variables.ts';
import { useApiHook } from '../../../hooks/useApi.ts';
import useBoundStore from '../../../store/useBoundStore.ts';
import { useToastHook } from '../../../hooks/useToaster.ts';
import UiLoader from '../../../lib/UiLoader.tsx';
import UiInput from '../../../lib/UiInput.tsx';
import UiButton from '../../../lib/UiButton.tsx';

interface FormValues {
    currentPassword: string;
    password: string;
    confirmPassword: string;
}

interface ResetPasswordProps {
    setPopup: (popup: ProfilePopup | null) => void;
}

const validationSchema = Yup.object({
    currentPassword: Yup.string()
        .min(6, 'Password must be at least 6 characters')
        .required('Current Password is required'),
    password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
    confirmPassword: Yup.string()
        .oneOf([Yup.ref('password')], 'Passwords must match')
        .required('Confirm password is required'),
});

const ResetPassword = ({ setPopup }: ResetPasswordProps) => {
    const { post } = useApiHook();
    const { loggedUser } = useBoundStore();
    const { showError, showSuccess } = useToastHook();
    const initialValues: FormValues = {
        currentPassword: '',
        password: '',
        confirmPassword: '',
    };
    const handleSubmit = async (values: FormValues, { setSubmitting }: FormikHelpers<FormValues>) => {
        const { currentPassword, password } = values;
        setSubmitting(true);
        try {
            await post(
                'authentication/change-password',
                {
                    currentPassword,
                    newPassword: password,
                },
                { headers: { Authorization: `Bearer ${loggedUser.token}` } }
            );
            setPopup(null);
            showSuccess('Password changed successfully');
        } catch (_e) {
            showError('Failed to change password. Please try again later');
        } finally {
            setSubmitting(false);
        }
    };

    return (
        <div className={'px-10 py-4'}>
            <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={handleSubmit}>
                {({ isSubmitting }) => (
                    <>
                        {isSubmitting && <UiLoader label="Loading..." />}
                        <Form className="">
                            <UiInput
                                label="Current Password"
                                name="currentPassword"
                                type="password"
                                placeholder="••••••••"
                            />
                            <UiInput label="New Password" name="password" type="password" placeholder="••••••••" />

                            <UiInput
                                label="Confirm Password"
                                name="confirmPassword"
                                type="password"
                                placeholder="••••••••"
                            />

                            <UiButton
                                type="submit"
                                disabled={isSubmitting}
                                className="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center"
                            >
                                {isSubmitting ? (
                                    <>
                                        <svg
                                            className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                        >
                                            <circle
                                                className="opacity-25"
                                                cx="12"
                                                cy="12"
                                                r="10"
                                                stroke="currentColor"
                                                strokeWidth="4"
                                            ></circle>
                                            <path
                                                className="opacity-75"
                                                fill="currentColor"
                                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                            ></path>
                                        </svg>
                                        Processing...
                                    </>
                                ) : (
                                    'Submit'
                                )}
                            </UiButton>
                        </Form>
                    </>
                )}
            </Formik>
        </div>
    );
};

export default ResetPassword;
