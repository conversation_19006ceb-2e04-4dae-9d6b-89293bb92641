import { useState } from 'react';

const services = [
    { name: 'Stays', disabled: false },
    { name: 'Nap Capsules', disabled: true },
    { name: 'Shower Only', disabled: true },
    { name: 'Workspace', disabled: true },
    { name: 'Spa', disabled: true },
];

function classNames(...classes: string[]) {
    return classes.filter(Boolean).join(' ');
}

function ServiceFilter() {
    const [selectedIndex, setSelectedIndex] = useState(0);

    return (
        <div className="relative">
            <div
                className="flex space-x-2 rounded-lg mb-4 p-1  overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
                role="tablist"
                aria-label="Service tabs"
            >
                {services.map((service, idx) => {
                    const selected = selectedIndex === idx;
                    return (
                        <button
                            key={service.name}
                            type="button"
                            role="tab"
                            aria-selected={selected}
                            aria-disabled={service.disabled}
                            tabIndex={selected ? 0 : -1}
                            disabled={service.disabled}
                            onClick={() => !service.disabled && setSelectedIndex(idx)}
                            className={classNames(
                                'relative flex items-center gap-2 px-4 py-2 transition-all duration-200 min-w-[150px] xl:min-w-[120px] justify-center',
                                selected ? '' : 'text-gray-600 hover:bg-gray-100 hover:text-red-600',
                                service.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer',
                                'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1'
                            )}
                        >
                            <div
                                className={classNames(
                                    'w-2 h-2 rounded-full',
                                    selected ? 'bg-black' : 'bg-gray-400',
                                    service.disabled ? 'bg-gray-300' : ''
                                )}
                            />
                            <span className="font-semibold text-xs xl:text-sm">{service.name}</span>
                        </button>
                    );
                })}
            </div>
        </div>
    );
}

export default ServiceFilter;
