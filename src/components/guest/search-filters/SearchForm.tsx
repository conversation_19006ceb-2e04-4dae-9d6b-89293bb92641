import { type ChangeEvent, useEffect, useState } from 'react';
import * as Yup from 'yup';
import { Formik, Form } from 'formik';
import Select from 'react-dropdown-select';
import DatePicker from 'react-datepicker';
import { Tooltip } from 'react-tooltip';
import { MagnifyingGlassIcon, InformationCircleIcon, CalendarIcon } from '@heroicons/react/24/outline';
import { MapPinIcon } from '@heroicons/react/16/solid';
import type { ILocation } from '../../../interfaces/ILocation.ts';
import { useApiHook } from '../../../hooks/useApi.ts';
import { useToastHook } from '../../../hooks/useToaster.ts';
import useBoundStore from '../../../store/useBoundStore.ts';
import { SearchFormInitialValues } from '../../../utils/constants/SearchFormInitialValues.ts';
import GuestsRoomsSelector from '../../common/GuestsRoomsSelector.tsx';
import UiButton from '../../../lib/UiButton.tsx';

export const SearchFormValidationSchema = Yup.object().shape({
    airport: Yup.array().min(1, 'Please select at least one airport').required('Please select at least one airport'),
    checkInDate: Yup.date().required('Check-in date is required'),
    checkOutDate: Yup.date()
        .required('Check-out date is required')
        .test('checkout-after-checkin', 'Check-out date must be after check-in date', function (value) {
            const { checkInDate } = this.parent;
            if (!checkInDate || !value) return true;
            return value > checkInDate;
        }),

    checkInTime: Yup.string()
        .required('Check-in time is required')
        .test('checkin-time-validation', 'Check-in time cannot be in the past for today', function (value) {
            const { checkInDate } = this.parent;
            if (!checkInDate || !value) return true;

            const now = new Date();
            now.setSeconds(0, 0);

            const checkInDateTime = new Date(checkInDate);
            const [hours, minutes] = value.split(':').map(Number);
            checkInDateTime.setHours(hours, minutes, 0, 0);

            if (checkInDateTime.toDateString() === now.toDateString()) {
                return checkInDateTime.getTime() >= now.getTime();
            }

            return true;
        }),

    checkOutTime: Yup.string()
        .required('Check-out time is required')
        .test(
            'checkout-time-validation',
            'Check-out time must be after check-in time on the same day',
            function (value) {
                const { checkInDate, checkOutDate, checkInTime } = this.parent;
                if (!checkInDate || !checkOutDate || !checkInTime || !value) return true;

                // If check-in and check-out are on the same day, check-out time must be after check-in time
                if (checkInDate.toDateString() === checkOutDate.toDateString()) {
                    const checkInTimeMinutes =
                        parseInt(checkInTime.split(':')[0]) * 60 + parseInt(checkInTime.split(':')[1]);
                    const checkOutTimeMinutes = parseInt(value.split(':')[0]) * 60 + parseInt(value.split(':')[1]);
                    return checkOutTimeMinutes > checkInTimeMinutes;
                }
                return true;
            }
        ),
    noOfAdults: Yup.number().min(1, 'There must be at least 1 adult').required(),
    noOfChildren: Yup.number().min(0, 'noOfChildren cannot be negative'),
    rooms: Yup.number().min(1, 'There must be at least 1 room').required(),
});

export interface ISearchOptions {
    airport: Array<{
        label: string;
        value: string;
    }>;
    checkInDate: Date;
    checkInTime: string;
    checkOutDate: Date;
    checkOutTime: string;
    noOfAdults: number;
    noOfChildren: number;
    rooms: number;
    area: Array<'Airside' | 'Landside'>;
}

function removeDuplicateCities(locations: ILocation[]): ILocation[] {
    const cityMap = new Map<string, ILocation>();

    for (const location of locations) {
        if (!cityMap.has(location.city)) {
            cityMap.set(location.city, location);
        }
    }

    return Array.from(cityMap.values());
}

const SearchForm = ({
    initialValues,
    onSubmit,
}: {
    initialValues: ISearchOptions | null;
    onSubmit: (values: ISearchOptions) => void;
}) => {
    const [showPopover, setShowPopover] = useState(false);
    const [noOfAdults, setAdults] = useState(initialValues?.noOfAdults || 1);
    const [noOfChildren, setChildren] = useState(initialValues?.noOfChildren || 0);
    const [rooms, setRooms] = useState(initialValues?.rooms || 1);
    const [searchInput, setSearchInput] = useState('');
    const { get } = useApiHook();
    const { showError } = useToastHook();
    const { locations, setLocations, clearSearch } = useBoundStore();

    useEffect(() => {
        clearSearch();
    }, []);

    useEffect(() => {
        const fetchLocations = async () => {
            try {
                const response = await get<ILocation[]>('/locations');
                if (response) {
                    const locationsResponse = removeDuplicateCities(response);
                    setLocations(locationsResponse);
                }
            } catch (_err) {
                showError('Error fetching locations:');
            }
        };

        if (!locations.length) {
            fetchLocations();
        }
    }, []);

    const customNoDataRenderer = () => <p className="hidden">Search...</p>;

    return (
        <Formik
            initialValues={initialValues || SearchFormInitialValues}
            validateOnMount={true}
            validateOnChange={true}
            validateOnBlur={true}
            validationSchema={SearchFormValidationSchema}
            onSubmit={async (values, { setSubmitting }) => {
                setSubmitting(true);
                try {
                    await onSubmit(values);
                } catch (_error) {
                    showError('Submission failed');
                } finally {
                    setSubmitting(false);
                }
            }}
        >
            {({ values, setFieldValue, isSubmitting, setTouched, validateForm, submitForm }) => {
                const handleCheckboxChange = (e: ChangeEvent<HTMLInputElement>) => {
                    const { value, checked } = e.target;

                    if (checked) {
                        setFieldValue('area', [...values.area, value]);
                    } else {
                        setFieldValue(
                            'area',
                            values.area.filter((a: string) => a !== value)
                        );
                    }
                };

                return (
                    <div className="w-full rounded-3xl xl:rounded-full shadow-lg  px-6 py-4 bg-white border border-gray-300">
                        <Form className="flex flex-col xl:flex-row items-stretch xl:items-center justify-between gap-4 w-full">
                            {/* Location */}
                            <div className="w-full xl:w-auto xl:flex-1 flex flex-col justify-center px-4 py-2 border-b-2 xl:border-b-0 xl:border-r-1 border-gray-300">
                                <label className="text-sm font-semibold text-black">Location</label>
                                <div className={'w-full flex items-center gap-0.5'}>
                                    <MapPinIcon className={'w-5 h-5 text-gray-400'} />
                                    <Select
                                        options={
                                            searchInput.length > 0
                                                ? locations.map(location => ({
                                                      value: location._id || '',
                                                      label: `${location.code}` || '',
                                                  }))
                                                : []
                                        }
                                        onChange={selected => setFieldValue('airport', selected)}
                                        values={values.airport || []}
                                        placeholder="Find Your Hotel"
                                        className={`w-full text-md bg-transparent text-gray-300 xl:max-w-[138px] align-baseline ${values.airport.length > 0 ? 'select-placeholder-hide' : ''}`}
                                        style={{
                                            border: 'none',
                                            minHeight: '24px',
                                            boxShadow: 'none',
                                            outline: 'none',
                                            color: 'gray',
                                        }}
                                        dropdownHandle={false}
                                        labelField="label"
                                        valueField="value"
                                        multi={false}
                                        handleKeyDownFn={e => {
                                            const key = e.event.key;
                                            if (/^[a-zA-Z0-9]$/.test(key)) {
                                                setSearchInput(prev => prev + key);
                                            } else if (key === 'Backspace') {
                                                setSearchInput(prev => prev.slice(0, -1));
                                            } else if (key === 'Escape' || key === 'Enter') {
                                                setSearchInput('');
                                            }
                                        }}
                                        noDataRenderer={customNoDataRenderer}
                                    />
                                </div>
                            </div>

                            {/* Check-in */}
                            <div className="w-full xl:w-auto xl:flex-1 flex items-center gap-4 px-4 py-2 border-b-2 xl:border-b-0 xl:border-r-1 border-gray-300">
                                <div className="flex-1 min-w-[150px]">
                                    <label className="text-sm font-semibold text-black">Check in</label>
                                    <div className={'w-full flex items-center gap-2 relative'}>
                                        <CalendarIcon className="w-5 h-5 text-gray-400" />
                                        <DatePicker
                                            selected={values.checkInDate ? new Date(values.checkInDate) : null}
                                            onChange={date => setFieldValue('checkInDate', date)}
                                            minDate={new Date()}
                                            dateFormat="dd-MM-yyyy"
                                            className="w-full text-md bg-transparent text-gray-400 !border-0 !outline-none focus:!ring-0 cursor-pointer p-0"
                                            popperClassName="z-50"
                                        />
                                    </div>
                                </div>
                                <div className="h-full border-l border-gray-300 pl-4 min-w-[70px]">
                                    <label className="text-sm font-semibold text-gray-400 invisible">&nbsp;</label>
                                    <DatePicker
                                        selected={
                                            values.checkInTime ? new Date(`1970-01-01T${values.checkInTime}`) : null
                                        }
                                        onChange={date =>
                                            setFieldValue(
                                                'checkInTime',
                                                date
                                                    ? `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
                                                    : ''
                                            )
                                        }
                                        showTimeSelect
                                        showTimeSelectOnly
                                        timeIntervals={15}
                                        timeCaption="Time"
                                        dateFormat="HH:mm"
                                        className="w-full text-md bg-transparent text-gray-400  !border-0 !outline-none focus:!ring-0 cursor-pointer p-0"
                                        popperClassName="z-50"
                                    />
                                </div>
                            </div>

                            {/* Check-out */}
                            <div className="w-full xl:w-auto xl:flex-1 flex items-center gap-4 px-4 py-2 border-b-2 xl:border-b-0 xl:border-r-1 border-gray-300">
                                <div className="flex-1 min-w-[150px]">
                                    <label className="text-sm font-semibold text-black">Check out</label>
                                    <div className={'w-full flex items-center gap-2 relative'}>
                                        <CalendarIcon className="w-5 h-5 text-gray-400" />
                                        <DatePicker
                                            selected={values.checkOutDate ? new Date(values.checkOutDate) : null}
                                            onChange={date => setFieldValue('checkOutDate', date)}
                                            minDate={values.checkInDate || new Date()}
                                            dateFormat="dd-MM-yyyy"
                                            className="w-full text-md bg-transparent text-gray-400  !border-0 !outline-none focus:!ring-0 cursor-pointer p-0"
                                            popperClassName="z-50"
                                        />
                                    </div>
                                </div>
                                <div className="h-full border-l border-gray-300 pl-4 min-w-[70px]">
                                    <label className="text-sm font-semibold text-gray-400 invisible">&nbsp;</label>
                                    <DatePicker
                                        selected={
                                            values.checkOutTime ? new Date(`1970-01-01T${values.checkOutTime}`) : null
                                        }
                                        onChange={date =>
                                            setFieldValue(
                                                'checkOutTime',
                                                date
                                                    ? `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
                                                    : ''
                                            )
                                        }
                                        showTimeSelect
                                        showTimeSelectOnly
                                        timeIntervals={15}
                                        timeCaption="Time"
                                        dateFormat="HH:mm"
                                        className="w-full text-md bg-transparent text-gray-400 !border-0 !outline-none focus:!ring-0 cursor-pointer p-0"
                                        popperClassName="z-100"
                                    />
                                </div>
                            </div>

                            {/* Guests & Rooms */}
                            <div className="w-full xl:w-auto xl:flex-1 flex flex-col justify-center px-4 py-2 border-b-2 xl:border-b-0 xl:border-r-1 border-gray-300 relative min-w-[160px]">
                                <label className="text-sm font-semibold text-black">Guests & Rooms</label>
                                <button
                                    type="button"
                                    className="w-full text-left text-md text-gray-400  bg-transparent border-0 p-0"
                                    onClick={() => setShowPopover(!showPopover)}
                                >
                                    {`${noOfAdults + noOfChildren} Guest, ${rooms} Room`}
                                </button>
                                <div className="absolute top-full left-0 xl:left-auto xl:right-0 mt-2 z-50 w-full xl:w-auto min-w-[280px]">
                                    <GuestsRoomsSelector
                                        show={showPopover}
                                        onClose={() => setShowPopover(false)}
                                        noOfAdults={noOfAdults}
                                        noOfChildren={noOfChildren}
                                        rooms={rooms}
                                        setAdults={value => {
                                            setAdults(value);
                                            setFieldValue('noOfAdults', value);
                                        }}
                                        setChildren={value => {
                                            setChildren(value);
                                            setFieldValue('noOfChildren', value);
                                        }}
                                        setRooms={value => {
                                            setRooms(value);
                                            setFieldValue('rooms', value);
                                        }}
                                    />
                                </div>
                            </div>

                            {/* Area */}
                            <div className="w-full xl:w-auto flex flex-col justify-center px-4 py-2 border-b-2 xl:border-b-0 xl:border-r-1 border-gray-300">
                                <label className="inline-flex items-center gap-2 text-sm text-gray-400 cursor-pointer">
                                    <input
                                        type="checkbox"
                                        value="Airside"
                                        checked={values.area?.includes('Airside')}
                                        onChange={handleCheckboxChange}
                                        className="h-4 w-4 bg-transparent border-gray-400 rounded accent-red-600 focus:ring-red-500 text-red-600"
                                    />
                                    Airside
                                    <InformationCircleIcon
                                        className="h-4 w-4 text-gray-400 cursor-help"
                                        data-tooltip-id="airside-tooltip"
                                        data-tooltip-content="Airside is the area inside the airport"
                                    />
                                </label>
                                <label className="inline-flex items-center gap-2 text-sm text-gray-400 cursor-pointer mt-2">
                                    <input
                                        type="checkbox"
                                        value="Landside"
                                        checked={values.area?.includes('Landside')}
                                        onChange={handleCheckboxChange}
                                        className="h-4 w-4 bg-transparent border-gray-400 rounded accent-red-600 focus:ring-red-500 text-red-600"
                                    />
                                    Landside
                                    <InformationCircleIcon
                                        className="h-4 w-4 text-gray-400 cursor-help"
                                        data-tooltip-id="landside-tooltip"
                                        data-tooltip-content="Landside is the area outside the airport"
                                    />
                                </label>

                                {/* Tooltip */}
                                <Tooltip
                                    id="airside-tooltip"
                                    className="max-w-xs bg-gray-800 text-gray-400 text-sm rounded-lg shadow-lg border border-gray-600 z-50"
                                    place="top"
                                    delayShow={300}
                                    delayHide={100}
                                />
                                <Tooltip
                                    id="landside-tooltip"
                                    className="max-w-xs bg-gray-800 text-gray-400 text-sm rounded-lg shadow-lg border border-gray-600 z-50"
                                    place="top"
                                    delayShow={300}
                                    delayHide={100}
                                />
                            </div>

                            {/* Search Button */}
                            <div className="w-full xl:w-auto flex items-center justify-center">
                                <UiButton
                                    type="button"
                                    isTheme={false}
                                    disabled={isSubmitting}
                                    className={`w-full flex items-center justify-center rounded-full theme-background  transition-all ${
                                        !isSubmitting
                                            ? 'bg-gray-800 hover:bg-black text-gray-400'
                                            : 'bg-gray-600 text-gray-300 cursor-not-allowed'
                                    }`}
                                    onClick={async () => {
                                        setTouched({
                                            // TODO: Fix the types for Formik
                                            // @ts-expect-error Formik types are not fully compatible with this structure
                                            airport: true,
                                            checkInDate: true,
                                            checkInTime: true,
                                            checkOutDate: true,
                                            checkOutTime: true,
                                            noOfAdults: true,
                                            noOfChildren: true,
                                            rooms: true,
                                            area: true,
                                        });

                                        const errors = await validateForm();

                                        if (Object.keys(errors).length === 0) {
                                            await submitForm();
                                        } else {
                                            const firstErrorMessage = Object.values(errors)[0];

                                            if (typeof firstErrorMessage === 'string') {
                                                showError(firstErrorMessage);
                                            } else {
                                                showError('Please correct the form errors');
                                            }
                                        }
                                    }}
                                >
                                    <div className="flex p-2 items-center justify-center">
                                        <MagnifyingGlassIcon className="h-5 w-5 " />
                                        {/*<span className="font-semibold">Search</span>*/}
                                    </div>
                                </UiButton>
                            </div>
                        </Form>
                    </div>
                );
            }}
        </Formik>
    );
};

export default SearchForm;
