import { UserGroupIcon, ChartBarIcon, CursorArrowRaysIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';

const Partner = () => {
    return (
        <div className="flex flex-col items-center justify-center py-10">
            <div className="relative">
                <h1 className="text-3xl font-bold text-center mb-8">Features</h1>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-2 max-w-6xl mx-auto">
                <div className="bg-white p-6 rounded-xl shadow-lg text-center">
                    <div className="flex justify-center mb-4">
                        <ChartBarIcon className="w-12 h-12 text-red-600" />
                    </div>
                    <h2 className="text-xl font-semibold mb-2">Targeted traffic</h2>
                    <p className="text-gray-600">
                        The portal was launched in 2017 and its traffic is growing exponentially. Now our monthly
                        audience is 170,000 visitors.
                    </p>
                </div>

                {/* International Lead Generation */}
                <div className="bg-white p-6 rounded-xl shadow-lg text-center">
                    <div className="flex justify-center mb-4">
                        <UserGroupIcon className="w-12 h-12 text-red-600" />
                    </div>
                    <h2 className="text-xl font-semibold mb-2">International lead generation</h2>
                    <p className="text-gray-600">International reach expands across more than 150 countries.</p>
                </div>

                {/* High Conversion Rate */}
                <div className="bg-white p-6 rounded-xl shadow-lg text-center">
                    <div className="flex justify-center mb-4">
                        <CursorArrowRaysIcon className="w-12 h-12 text-red-600" />
                    </div>
                    <h2 className="text-xl font-semibold mb-2">The high conversion rate</h2>
                    <p className="text-gray-600">
                        TopFranchise.com visitors are real entrepreneurs, investors, and prospective franchisees. The
                        average conversion rate is 3%.
                    </p>
                </div>

                {/* High Ranking */}
                <div className="bg-white p-6 rounded-xl shadow-lg text-center">
                    <div className="flex justify-center mb-4">
                        <MagnifyingGlassIcon className="w-12 h-12 text-red-600" />
                    </div>
                    <h2 className="text-xl font-semibold mb-2">High ranking on major search engines</h2>
                    <p className="text-gray-600">
                        We work hard to get our portal to the top of Google SERPs for thematic search queries.
                    </p>
                </div>
            </div>
        </div>
    );
};

export default Partner;
