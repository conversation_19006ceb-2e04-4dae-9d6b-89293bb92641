import { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/solid';
import faqData from '../../../constants/FAQ.json';

const FAQ = () => {
    const [openIndex, setOpenIndex] = useState<number | null>(0);

    const toggleFAQ = (index: number) => {
        setOpenIndex(openIndex === index ? null : index);
    };

    return (
        <div className="mx-auto">
            <h2 className="text-2xl font-bold text-center mb-8">FAQs</h2>
            <div className="space-y-4">
                {faqData.map((faq, index) => (
                    <div
                        key={index}
                        className="border-b border-gray-100 pb-4 cursor-pointer"
                        onClick={() => toggleFAQ(index)}
                    >
                        <div className="flex justify-between items-center">
                            <h3 className="text-md font-medium text-gray-900">{faq.question}</h3>
                            <span className="text-red-500 text-xl">
                                {openIndex === index ? (
                                    <ChevronUpIcon className={'h-5 w-5 font-semiboldtext-red-500 '} />
                                ) : (
                                    <ChevronDownIcon className={'h-5 w-5 text-red-500 '} />
                                )}
                            </span>
                        </div>
                        {openIndex === index && faq.answer && (
                            <p className="mt-5 text-sm text-gray-500">{faq.answer}</p>
                        )}
                    </div>
                ))}
            </div>
        </div>
    );
};

export default FAQ;
