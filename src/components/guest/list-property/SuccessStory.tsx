import image from '../../../assets/success-story.png';

const SuccessStory = () => {
    return (
        <div className="flex flex-col md:flex-row items-center justify-between bg-white gap-5">
            {/* Left Text Section */}
            <div className="flex-1 text-center md:text-left">
                <h2 className="text-3xl font-bold  mb-4">Our Success Story</h2>
                <p className="text-gray-600 leading-relaxed">
                    StayTransit was born from a simple idea: make airport layovers restful, productive, and stress-free.
                    We launched with one transit hotel and have since grown into a trusted platform offering hourly
                    bookings for hotels, capsules, showers, workspaces, and spas across major airports. Thousands of
                    travelers now rely on StayTransit to relax, refresh, or work between flights. By partnering with
                    airport authorities and premium hospitality brands, we’ve redefined the short-stay experience. Our
                    seamless digital platform, 24/7 support, and airport-first focus continue to drive growth — making
                    StayTransit the go-to solution for modern, time-conscious travelers worldwide.
                </p>
            </div>

            {/* Right Image Section */}
            <div className="flex-1 flex justify-end items-center">
                <img src={image} alt="Man presenting to group" className="rounded-2xl w-full max-w-md object-cover" />
            </div>
        </div>
    );
};

export default SuccessStory;
