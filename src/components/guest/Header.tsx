import React, { useState, useEffect } from 'react';
import { Transition } from '@headlessui/react';
import { Bars3Icon, XMarkIcon, UserCircleIcon } from '@heroicons/react/24/outline';
import useBoundStore from '../../store/useBoundStore.ts';
import { Link, useNavigate, useSearchParams } from 'react-router';
import Logo from '../common/Logo.tsx';
import UserMenu from '../common/UserMenu.tsx';
import UiPopup from '../../lib/UiPopup.tsx';
import LoginForm from './auth/LoginForm.tsx';
import RegisterForm from './auth/RegisterForm.tsx';
import ForgotPassword from './auth/ForgotPassword.tsx';

const Header: React.FC = () => {
    const {
        clearCart,
        updateGuestProperty,
        openForgotPasswordPopup,
        clearForgotPasswordPopup,
        loggedUser,
        clearLoggedUser,
        openLoginPopup,
        openRegisterPopup,
        updateLoginPopup,
        updateRegisterPopup,
        clearLoginPopup,
        clearRegisterPopup,
    } = useBoundStore();
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
    const [isScrolled, setIsScrolled] = useState(false);
    const [searchParams] = useSearchParams();
    const redirectUrl = searchParams.get('redirect');
    const navigate = useNavigate();
    const logOut = () => {
        clearLoggedUser();
        navigate('/guest', { replace: true });
    };

    useEffect(() => {
        const handleScroll = () => {
            setIsScrolled(window.scrollY > 10);
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    return (
        <>
            <header
                className={`bg-white fixed w-full top-0 z-50 h-[var(--topbar-height)] transition-colors duration-300 ${
                    isScrolled ? 'shadow-md' : 'shadow-none'
                }`}
            >
                <div className="mx-auto h-full px-4 sm:px-6 lg:px-10 flex items-center justify-between">
                    {/* Left Section: Logo */}
                    <div className="flex-shrink-0">
                        <div
                            onClick={() => {
                                clearCart();
                                updateGuestProperty(undefined);
                                navigate('/guest');
                            }}
                            className="cursor-pointer"
                        >
                            <Logo size="lg" />
                        </div>
                    </div>

                    {/* Center Section: Search Bar (Desktop) */}
                    {/* <div className="hidden md:flex flex-1 justify-end px-8">
                    <div className="flex items-center bg-gray-100 border-2 border-gray-200 rounded-full shadow-sm py-1 px-2 w-full max-w-lg">
                        <div className="flex-1">
                            <input type="text" placeholder="Search..." className="w-full focus:outline-none bg-transparent px-4 py-1" />
                        </div>
                        <div className="h-8 border-l border-gray-300"></div>
                        <div className="flex-1 flex items-center pl-2">
                            <MapPinIcon className="h-5 w-5 text-gray-500" />
                            <input type="text" placeholder="Chicago" className="w-full focus:outline-none bg-transparent ml-2 py-1" />
                        </div>
                        <button className="theme-background text-white p-2 rounded-full">
                            <MagnifyingGlassIcon className="h-5 w-5" />
                        </button>
                    </div>
                </div> */}

                    {/* Right Section: Links and Auth (Desktop) */}
                    <nav className="hidden md:flex items-center space-x-4 flex-shrink-0">
                        <Link
                            to={`${
                                loggedUser?.user?.role?.includes('merchant')
                                    ? '/onboarding'
                                    : '/guest/list-your-property?redirect=onboarding'
                            }`}
                            className="font-semibold flex items-center"
                        >
                            <span>Become a Partner</span>
                        </Link>

                        {loggedUser.token ? (
                            <UserMenu user={loggedUser.user} onSignOut={logOut} />
                        ) : (
                            <div className="flex items-center space-x-4">
                                <button
                                    onClick={() => updateLoginPopup(true)}
                                    className=" theme-text border font-semibold transition-colors duration-200 rounded-full hover:bg-red-600 hover:text-white px-4 py-2"
                                >
                                    Login
                                </button>
                                <button
                                    onClick={() => updateRegisterPopup(true)}
                                    className="theme-background px-4 py-2 text-white rounded-full font-semibold"
                                >
                                    Sign Up
                                </button>
                            </div>
                        )}
                    </nav>

                    {/* Mobile Menu Button */}
                    <div className="md:hidden flex items-center">
                        <button
                            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                            className="p-2 rounded-lg text-gray-600 hover:bg-gray-100 transition-colors duration-200 focus:outline-none"
                        >
                            {mobileMenuOpen ? <XMarkIcon className="h-6 w-6" /> : <Bars3Icon className="h-6 w-6" />}
                        </button>
                    </div>
                </div>

                {/* Mobile Menu */}
                <Transition
                    show={mobileMenuOpen}
                    enter="transition duration-200 ease-out"
                    enterFrom="transform -translate-y-2 opacity-0"
                    enterTo="transform translate-y-0 opacity-100"
                    leave="transition duration-150 ease-in"
                    leaveFrom="transform translate-y-0 opacity-100"
                    leaveTo="transform -translate-y-2 opacity-0"
                >
                    <div className="md:hidden bg-white border-t border-gray-100 shadow-lg">
                        <div className="px-4 py-3 space-y-1">
                            <Link
                                to="/onboarding"
                                className="block px-3 py-2 rounded-lg text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200"
                                onClick={() => setMobileMenuOpen(false)}
                            >
                                Become a Partner
                            </Link>

                            {loggedUser.token ? (
                                <>
                                    <div className="border-t border-gray-100 my-2 pt-2">
                                        <div className="px-3 py-2 text-sm text-gray-500">
                                            Signed in as{' '}
                                            <span className="font-medium text-gray-900">{loggedUser.user?.email}</span>
                                        </div>
                                        <Link
                                            to={`/guest/profile`}
                                            className="flex items-center px-3 py-2 rounded-lg text-gray-700 hover:bg-red-50 hover:text-red-600 transition-colors duration-200"
                                            onClick={() => setMobileMenuOpen(false)}
                                        >
                                            <UserCircleIcon className="w-5 h-5 mr-2" />
                                            Profile
                                        </Link>
                                        <button
                                            onClick={() => {
                                                logOut();
                                                setMobileMenuOpen(false);
                                            }}
                                            className="flex items-center w-full px-3 py-2 rounded-lg text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors duration-200"
                                        >
                                            <svg
                                                className="w-5 h-5 mr-2"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <path
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth={2}
                                                    d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                                                />
                                            </svg>
                                            Sign Out
                                        </button>
                                    </div>
                                </>
                            ) : (
                                <div className="border-t border-gray-100 pt-3 mt-2 space-y-2">
                                    <Link
                                        to={'/guest'}
                                        onClick={() => setMobileMenuOpen(false)}
                                        className="block w-full px-4 py-2 bg-blue-600 text-white rounded-lg text-center hover:bg-blue-700 transition-all duration-200 font-medium"
                                    >
                                        Login
                                    </Link>
                                    <Link
                                        to={'/guest'}
                                        onClick={() => setMobileMenuOpen(false)}
                                        className="block w-full px-4 py-2 bg-white text-blue-600 border border-blue-600 rounded-lg text-center hover:bg-blue-50 transition-all duration-200 font-medium"
                                    >
                                        Sign Up
                                    </Link>
                                </div>
                            )}
                        </div>
                    </div>
                </Transition>
            </header>
            <UiPopup
                isOpen={openLoginPopup}
                onClose={() => clearLoginPopup()}
                width="w-[500px] max-w-[90vw]"
                height="max-h-[600px]"
                showCloseButton={true}
                closeOnOverlayClick={false}
                className="overflow-hidden"
                title="Login"
            >
                <div className="p-6">
                    <LoginForm redirect={redirectUrl} />
                </div>
            </UiPopup>

            <UiPopup
                isOpen={openRegisterPopup}
                onClose={() => clearRegisterPopup()}
                width="w-[600px] max-w-[90vw]"
                height="max-h-[600px]"
                showCloseButton={true}
                closeOnOverlayClick={false}
                className="overflow-hidden"
                title="Sign Up"
            >
                <div className="p-6">
                    <RegisterForm redirect={redirectUrl} />
                </div>
            </UiPopup>
            <UiPopup
                isOpen={openForgotPasswordPopup}
                onClose={() => clearForgotPasswordPopup()}
                width="w-[400px] max-w-[90vw]"
                height="max-h-[600px]"
                showCloseButton={true}
                closeOnOverlayClick={false}
                className="overflow-hidden"
                title="Forgot Password"
            >
                <div className="p-6">
                    <ForgotPassword />
                </div>
            </UiPopup>
        </>
    );
};

export default Header;
