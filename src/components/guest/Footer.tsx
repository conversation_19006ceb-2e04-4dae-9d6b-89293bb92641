import { GlobeAltIcon, AtSymbolIcon, PhoneIcon, EnvelopeIcon, UserGroupIcon } from '@heroicons/react/24/solid';
import { useSearchParams } from 'react-router';
import useBoundStore from '../../store/useBoundStore.ts';
import logo from '../../assets/logo.png';
import UiPopup from '../../lib/UiPopup.tsx';
import JoinUsForm from './join-us';

const Footer = () => {
    const [searchParams] = useSearchParams();
    const redirectUrl = searchParams.get('redirect');
    const { openJoinUsPopup, clearJoinUsPopup, updateJoinUsPopup } = useBoundStore();

    const footerSections = [
        {
            title: 'About Us',
            links: ['People', 'Future Development'],
        },
        {
            title: 'Privacy',
            links: ['Policy', 'Settings'],
        },
        {
            title: 'Terms',
            links: ['Usage', 'Legal'],
        },
        {
            title: 'Careers',
            links: ['Join Us'],
        },
    ];

    const socialLinks = [
        { href: '#', label: 'Website', icon: GlobeAltIcon },
        { href: '#', label: 'Email', icon: AtSymbolIcon },
        { href: '#', label: 'Contact', icon: EnvelopeIcon },
        { href: '#', label: 'Phone', icon: PhoneIcon },
        { href: '#', label: 'Community', icon: UserGroupIcon },
    ];

    return (
        <footer className="w-full bg-white border-t border-gray-200 px-5">
            <div className=" mx-auto py-12 px-4 sm:px-6 lg:px-8">
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-8 md:gap-0">
                    {/* Logo + Social */}
                    <div className="space-y-6">
                        <div>
                            <img src={logo} alt="StayTransit Logo" width={150} height={150} className="object-cover" />
                        </div>
                        <div className="flex items-center gap-3 border border-gray-300 p-2 rounded-full w-fit">
                            <p className="text-sm font-semibold pl-2">Follow</p>
                            <div className="flex items-center gap-2 px-2">
                                {socialLinks.map((link, index) => (
                                    <a
                                        key={index}
                                        href={link.href}
                                        aria-label={link.label}
                                        className="p-2 border border-gray-300 rounded-full hover:bg-red-50 transition"
                                    >
                                        <link.icon className="w-5 h-5 text-gray-700" />
                                    </a>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Footer Sections */}
                    {footerSections.map(section => (
                        <div key={section.title} className="space-y-4">
                            <h3 className="font-bold text-base text-gray-800">{section.title}</h3>
                            <ul className="space-y-2">
                                {section.links.map(link => (
                                    <li key={link}>
                                        {link === 'Join Us' ? (
                                            <button
                                                type="button"
                                                onClick={() => updateJoinUsPopup(true)}
                                                className="text-gray-600 hover:text-red-600 text-sm"
                                            >
                                                {link}
                                            </button>
                                        ) : (
                                            <a href="#" className="text-gray-600 hover:text-red-600 text-sm transition">
                                                {link}
                                            </a>
                                        )}
                                    </li>
                                ))}
                            </ul>
                        </div>
                    ))}
                </div>
            </div>

            {/* Footer Bottom */}
            <div className="py-4 border-t border-gray-300 text-center">
                <p className="text-sm text-gray-400">© {new Date().getFullYear()} StayTransit. All rights reserved.</p>
            </div>

            {/* Join Us Popup */}
            <UiPopup
                isOpen={openJoinUsPopup}
                onClose={() => clearJoinUsPopup()}
                width="w-[500px] max-w-[90vw]"
                height="max-h-[900px]"
                showCloseButton
                closeOnOverlayClick={false}
                className="overflow-hidden"
                title="Join Us"
            >
                <div className="p-6">
                    <JoinUsForm redirect={redirectUrl} />
                </div>
            </UiPopup>
        </footer>
    );
};

export default Footer;
