import { useEffect, useState } from 'react';
import { BuildingOfficeIcon } from '@heroicons/react/24/outline';
import { NoSymbolIcon } from '@heroicons/react/16/solid';
import { useApiHook } from '../../../hooks/useApi.ts';
import { useToastHook } from '../../../hooks/useToaster.ts';
import type { GroupReservation } from '../../../interfaces/IBooking.ts';
import { invoiceTemplate } from '../../../template/invoice.ts';
import UiLoader from '../../../lib/UiLoader.tsx';
import SectionTitle from '../../common/SectionTitle.tsx';
import { Link } from 'react-router';

const TABS = ['Upcoming', 'Completed', 'Cancelled'] as const;
type TabType = (typeof TABS)[number];

const BookingHistory = () => {
    const { get } = useApiHook();
    const { showError } = useToastHook();
    const [bookings, setBooking] = useState<GroupReservation[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [activeTab, setActiveTab] = useState<TabType>('Upcoming');

    const fetchBooking = async () => {
        try {
            setIsLoading(true);
            const response = await get<GroupReservation[]>('users/reservations');
            setBooking(response);
        } catch (_e) {
            showError('Failed to fetch bookings. Please try again later');
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchBooking();
    }, []);

    const filterBookings = () => {
        const now = new Date().getTime();
        return bookings.filter(booking => {
            const status = booking.reservations[0].status.toLowerCase();
            const checkInDate = new Date(booking.reservations[0].startDateTime).getTime();

            if (activeTab === 'Completed') {
                return checkInDate < now && status !== 'cancelled';
            } else if (activeTab === 'Cancelled') {
                return status === 'cancelled';
            } else if (activeTab === 'Upcoming') {
                return checkInDate > now && status !== 'cancelled';
            }
            return true;
        });
    };

    const handleInvoiceDownload = (bookingId: string) => {
        const existingInvoice = bookings.find(booking => booking._id === bookingId);
        if (!existingInvoice) {
            showError('Booking not found for invoice generation');
            return;
        }
        const newWindow = window.open('', '_blank', 'width=1000,height=1000');
        if (newWindow) {
            newWindow.document.write(invoiceTemplate(existingInvoice));
            newWindow.document.close();
            newWindow.onload = () => {
                newWindow.focus();
                newWindow.print();
            };
        } else {
            alert('Pop-up blocked. Please allow pop-ups for this site.');
        }
    };

    if (isLoading) {
        return <UiLoader label={'Loading....'} />;
    }

    const filteredBookings = filterBookings();

    return (
        <div>
            <SectionTitle title="My Bookings" />
            <div className="flex space-x-2 overflow-x-auto no-scrollbar mb-6">
                {TABS.map(tab => (
                    <button
                        key={tab}
                        onClick={() => setActiveTab(tab)}
                        className={`relative px-5 py-2 text-sm font-semibold rounded-full transition-all duration-300 
        ${activeTab === tab ? 'theme-background text-white shadow-md' : ' theme-border text-orange-600'}`}
                    >
                        {tab}
                    </button>
                ))}
            </div>

            {/* Booking List */}
            <div className="flex flex-col gap-4">
                {filteredBookings.length === 0 ? (
                    <div className="flex flex-col items-center justify-center text-center py-10 px-4 bg-gray-50 rounded-lg ">
                        <NoSymbolIcon className={'h-12 w-12'} />
                        <h3 className="text-lg font-semibold text-gray-700">
                            No {activeTab.toLowerCase()} bookings found
                        </h3>
                        <p className="text-sm text-gray-500 mt-1">
                            You haven’t made any {activeTab.toLowerCase()} bookings yet.
                        </p>
                    </div>
                ) : (
                    filteredBookings.map(booking => (
                        <div key={booking._id} className="border rounded-lg overflow-hidden flex flex-col">
                            <div className="flex items-center justify-between flex-wrap gap-2 p-2">
                                <div className="flex items-center gap-2">
                                    <div className="w-15 h-15 rounded-full bg-gray-200 flex items-center justify-center mr-2">
                                        <BuildingOfficeIcon className="w-10 h-10" />
                                    </div>
                                    <div className="flex-1">
                                        <h2 className="text-sm font-medium">Stays</h2>
                                        <p className="text-md font-semibold">{booking.propertyId.name}</p>
                                        <p className="text-md text-gray-600 max-w-150">
                                            {booking.propertyId.address.address1}
                                        </p>
                                        {booking.reservationCode && (
                                            <p className="text-md text-gray-600">
                                                Booking id - {booking.reservationCode}
                                            </p>
                                        )}
                                    </div>
                                </div>
                                <div className="w-full md:w-auto flex flex-col gap-2 items-center justify-end">
                                    <Link
                                        to={`/guest/booking/${booking._id}`}
                                        className="text-sm text-orange-600 w-36 text-center  py-2 theme-border rounded-lg"
                                    >
                                        View Booking
                                    </Link>
                                    <button
                                        onClick={() => handleInvoiceDownload(booking._id)}
                                        className="text-sm theme-background text-white w-36 text-center py-2 border rounded-lg"
                                    >
                                        Download Invoice
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))
                )}
            </div>
        </div>
    );
};

export default BookingHistory;
