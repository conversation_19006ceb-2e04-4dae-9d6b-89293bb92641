import React from 'react';
import { StarIcon } from '@heroicons/react/24/solid';
import type { ICompany } from '../../../interfaces/ICompany.ts';
import StatusBadge from '../../common/StatusBadge.tsx';
import CurrencyFormat from '../../common/CurrencyFormat.tsx';

interface PropertyCardProps {
    property: ICompany;
    highlight?: boolean;
    onSelect: (propertyId: string) => void;
}

const MinimalPropertyCard: React.FC<PropertyCardProps> = ({ property, onSelect, highlight = false }) => {
    const cheapestPackage = property?.packages.sort((a, b) => a.price - b.price)[0];
    // const tax = cheapestPackage?.price * (cheapestPackage?.taxes?.reduce((acc, tax) => acc + Number(tax.value), 0) / 100);
    return (
        <div
            className={`rounded-lg shadow-lg cursor-pointer mb-2 ${highlight ? 'border-t-4 border-red-600' : 'bg-white border border-gray-200'}`}
        >
            <div
                className={`flex flex-col rounded-lg  sm:flex-row items-start sm:items-center  gap-2 sm:gap-3 p-2 sm:p-3 ${highlight ? '' : 'bg-white'}`}
                onClick={() => onSelect(property._id)}
            >
                {/* Info */}
                <div className="flex-1 space-y-0.5 sm:space-y-1">
                    <div className="flex justify-between items-center">
                        <h4 className="text-lg font-semibold text-gray-800 line-clamp-1">{property.name}</h4>
                    </div>
                    <div className="flex flex-wrap items-center gap-2">
                        <div className="text-xs text-gray-500 flex items-center gap-1">
                            {[1, 2, 3, 4, 5].map(index => (
                                <StarIcon key={index} className={`h-4 w-4 text-yellow-500`} />
                            ))}
                        </div>
                        <span className={`text-xs px-2 py-0.5 rounded-full font-medium`}>
                            <StatusBadge type={property.premisesType} />
                        </span>
                        <p className="text-lg font-bold text-gray-800">
                            {cheapestPackage?.price && (
                                <>
                                    <CurrencyFormat amount={cheapestPackage?.price} />
                                </>
                            )}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default MinimalPropertyCard;
