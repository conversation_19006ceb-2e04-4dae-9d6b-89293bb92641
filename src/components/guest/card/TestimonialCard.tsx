import { StarIcon } from '@heroicons/react/24/solid';

interface TestimonialCardProps {
    name: string;
    rating: number;
    comment: string;
    date?: string;
    location?: string;
    avatar?: string;
    service?: string;
}

function TestimonialCard({ name, rating, comment, date, location, avatar, service }: TestimonialCardProps) {
    return (
        <div className="group h-66 relative bg-white rounded-2xl p-4 shadow-sm hover:shadow-lg transition-all duration-300  my-3">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                    {/* Avatar */}
                    <div className="w-15 h-15 rounded-full border-3 bg-red-50 border-indigo-200 text-red-600 flex items-center justify-center font-bold">
                        {avatar ? (
                            <img src={avatar} alt={name} className="w-full h-full rounded-full object-cover" />
                        ) : (
                            name.charAt(0).toUpperCase() + name.charAt(1).toUpperCase()
                        )}
                    </div>

                    {/* User Details */}
                    <div>
                        <h4 className="font-semibold text-gray-900 text-md">{name}</h4>
                        {location && <p className="text-sm text-gray-400">{location}</p>}
                    </div>
                </div>
            </div>
            {/* Comment */}
            <p className="text-gray-700 w-full line-clamp-3 leading-relaxed m-4 text-sm">{comment}</p>

            {/* Service Badge */}
            {service && (
                <div className="inline-block theme-border text-xs font-semibold px-3 py-1 rounded-full mb-4">
                    {service}
                </div>
            )}

            {/* Rating */}
            <div className={'flex items-center justify-between'}>
                <div className="flex items-center gap-1">
                    {Array.from({ length: 5 }, (_, index) => (
                        <StarIcon
                            key={index}
                            className={`w-4 h-4 ${
                                index < rating
                                    ? 'text-[var(--primary-color)] fill-[var(--primary-color)]'
                                    : 'text-gray-300'
                            }`}
                        />
                    ))}
                    <span className="ml-2 text-sm font-medium text-gray-600">{rating}.0</span>
                </div>
                <div>
                    {/* Date */}
                    {date && (
                        <span className="text-xs text-gray-400">
                            {new Date(date).toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                year: 'numeric',
                            })}
                        </span>
                    )}
                </div>
            </div>

            {/* Hover Effect Border */}
            <div className="absolute inset-0 rounded-2xl border-2 border-transparent group-hover:theme-border pointer-events-none transition-colors duration-300" />
        </div>
    );
}

export default TestimonialCard;
