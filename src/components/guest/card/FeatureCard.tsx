import React from 'react';

interface StayBenefit {
    title: string;
    description: string;
    icon: React.ElementType;
}

const FeatureCard = ({ title, icon: Icon, description }: StayBenefit) => {
    return (
        <div className="flex gap-4 items-start p-4 border-b border-gray-200  hover:bg-gray-50 transition-all duration-300 ease-in-out">
            <div className="flex-shrink-0">
                <div className="flex items-center justify-center w-20 h-20 rounded-full bg-indigo-50 transition-colors duration-300 group-hover:bg-indigo-200">
                    <Icon className="h-10 w-10 text-red-600" aria-hidden="true" />
                </div>
            </div>
            <div className="flex-1">
                <h4 className="font-semibold text-xl text-gray-900 tracking-tight">{title}</h4>
                <p className="mt-1 text-sm text-gray-600 leading-6">{description}</p>
            </div>
        </div>
    );
};

export default FeatureCard;
