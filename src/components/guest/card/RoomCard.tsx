import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { A11y, Pagination, Scrollbar } from 'swiper/modules';
import type { IAmenity } from '../../../interfaces/IPackage.ts';

// Define the props interface for type safety
interface RoomCardProps {
    roomName: string;
    size: number;
    bedType: string;
    amenities: IAmenity[];
    images: string[];
}

const RoomCard: React.FC<RoomCardProps> = ({ roomName, size, bedType, amenities, images }) => {
    return (
        <div className="rounded-lg overflow-hidden w-inherit shadow-sm bg-white">
            <Swiper
                modules={[Pagination, Scrollbar, A11y]}
                spaceBetween={0}
                slidesPerView={1}
                loop={true}
                pagination={{ clickable: true, dynamicBullets: true }}
                className="w-full h-82 max-w-[85vw]"
            >
                {images.map((image, index) => (
                    <SwiperSlide key={index} className="pb-8">
                        <img
                            src={image}
                            alt={`Room image ${index + 1}`}
                            width={320}
                            height={200}
                            className="w-full h-full object-cover rounded-lg"
                        />
                    </SwiperSlide>
                ))}
            </Swiper>
            <div className="px-4 py-1">
                <h2 className="text-lg font-semibold">{roomName}</h2>
                <p className="text-sm text-gray-600">
                    ({size + `sq.ft`} | {bedType})
                </p>
                <ul className="grid grid-cols-2 gap-2 mt-2 text-sm text-gray-700">
                    {amenities.map((amenity, index) => (
                        <li key={index} className="flex items-center">
                            • {amenity.name}
                        </li>
                    ))}
                </ul>
                {/* <button className="mt-4 theme-text hover:underline text-sm block">
                    More Details
                </button> */}
            </div>
        </div>
    );
};

export default RoomCard;
