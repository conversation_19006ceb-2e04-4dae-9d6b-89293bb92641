import React from 'react';
import { MinusIcon } from '@heroicons/react/16/solid';
import { PlusIcon } from '@heroicons/react/24/outline';
import type { IPackage } from '../../../interfaces/IPackage.ts';
import CurrencyFormat from '../../common/CurrencyFormat.tsx';

interface PackageCardProps {
    pkg: IPackage;
    cart: IPackage[];
    onSelect?: (packageId: string) => void;
    onRemoveItem?: (packageId: string) => void;
}

const PackageCard: React.FC<PackageCardProps> = ({ pkg, cart, onSelect, onRemoveItem }) => {
    const handleSelect = () => {
        if (onSelect) {
            onSelect(pkg._id);
        }
    };

    const removeItem = () => {
        if (onRemoveItem) {
            onRemoveItem(pkg._id);
        }
    };

    const totalDiscount =
        pkg.rateCardPrice && pkg.price > pkg.rateCardPrice
            ? Math.floor(((pkg.price - pkg.rateCardPrice) * 100) / pkg.price)
            : 0;

    // Calculate total taxes
    const totalTaxAmount = pkg.taxes.reduce((acc, tax) => {
        return acc + (pkg.rateCardPrice || pkg.price) * (Number(tax.value) / 100);
    }, 0);

    return (
        <div
            className="rounded-lg bg-white shadow-md border border-gray-100 p-4 w-full flex flex-col md:flex-row justify-between gap-4"
            key={pkg._id + pkg.name}
        >
            {/* Left Section: Package Details */}
            <div className="flex-1">
                <h2 className="text-lg font-semibold">{pkg.name}</h2>
                <p className="text-sm text-gray-600">{pkg.description}</p>
                {/* <ul className="mt-2 text-sm text-gray-700">
                    {pkg.amenities.map((amenity) => (
                        <li key={amenity.code} className="flex items-center gap-2">
                            {amenity.icon && <span>{amenity.icon}</span>}
                            {amenity.name}
                        </li>
                    ))}
                </ul> */}
                <div className="my-2 text-sm">
                    <p>
                        Duration: <span className={'font-semibold'}>{pkg.duration} hrs</span>
                    </p>
                    {/* <p>Capacity: {pkg.noOfAdults} adults, {pkg.noOfChildren} children</p> */}
                </div>
                <ul className="text-sm text-gray-700">
                    {pkg.amenities &&
                        pkg.amenities.map((amenity, index) => (
                            <>
                                {index === 0 && (
                                    <li key={index + 'amenities'}>
                                        <b>Amenities</b>
                                    </li>
                                )}
                                <li key={index} className="flex items-center ml-1">
                                    • {amenity.name}
                                </li>
                            </>
                        ))}
                </ul>
                {/* <button className="mt-2 text-blue-600 hover:underline text-sm">
                    More Details
                </button> */}
            </div>

            {/* Right Section: Pricing + CTA */}
            <div className="text-right md:text-right">
                <div className={'flex gap-2 items-center justify-end mb-2'}>
                    {pkg.rateCardPrice && pkg.price > pkg.rateCardPrice && (
                        <p className="text-sm line-through text-red-600">
                            <CurrencyFormat amount={pkg.price.toFixed(2)} />
                        </p>
                    )}
                    <p className="text-xl font-bold">
                        <CurrencyFormat amount={(pkg.rateCardPrice || pkg.price).toFixed(2)} />
                    </p>
                </div>
                {totalDiscount > 0 && (
                    <div>
                        <p className="text-sm text-green-600">
                            <span className="font-semibold">Discount: </span>
                            {totalDiscount}% off
                        </p>
                    </div>
                )}
                <div className="text-sm text-gray-600 pb-2">
                    {/* {pkg.taxes.map((tax) => (
                        <p key={tax.name}><CurrencyFormat amount={(pkg.price * (Number(tax.value) / 100)).toFixed(2)} /> {tax.name}</p>
                    ))} */}
                    {pkg.taxes.length > 0 && (
                        <p className={'pb-2 text-black'}>
                            <CurrencyFormat amount={totalTaxAmount.toFixed(2)} /> Taxes & Fees
                        </p>
                    )}
                    <p className="font-semibold text-black">
                        Total with taxes:{' '}
                        <CurrencyFormat amount={((pkg.rateCardPrice || pkg.price) + totalTaxAmount).toFixed(2)} />
                    </p>
                </div>
                <div className="flex items-center gap-2 justify-end mt-2 bor">
                    {cart.filter(c => c._id == pkg._id).length > 0 ? (
                        <div className={'border border-gray-200 rounded-md flex items-center gap-2 p-2'}>
                            <button
                                className="theme-background text-white w-7 h-7 rounded-md text-sm hover:bg-blue-700 transition-colors flex items-center justify-center"
                                onClick={removeItem}
                            >
                                <MinusIcon className="w-4 h-4" />
                            </button>
                            <span className="text-lg font-medium w-8 text-center">
                                {cart.filter(c => c._id == pkg._id).length}
                            </span>
                            <button
                                className="theme-background text-white w-7 h-7 rounded-md text-sm hover:bg-blue-700 transition-colors flex items-center justify-center"
                                onClick={handleSelect}
                            >
                                <PlusIcon className="w-4 h-4" />
                            </button>
                        </div>
                    ) : (
                        <button
                            className="theme-background text-white px-4 py-1.5 rounded-md text-sm hover:bg-blue-700 transition-colors"
                            onClick={handleSelect}
                        >
                            Add Room
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default PackageCard;
