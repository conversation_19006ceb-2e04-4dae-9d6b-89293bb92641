import React from 'react';
import { Formik, Form, Field, type FieldProps } from 'formik';
import * as Yup from 'yup';
import { countryCodesOptions } from '../../../utils/helpers/countryCodes.ts';
import useBoundStore from '../../../store/useBoundStore.ts';
import UiLoader from '../../../lib/UiLoader.tsx';
import UiInput from '../../../lib/UiInput.tsx';
import UiSelect from '../../../lib/UiSelect.tsx';
import { allowOnlyNumbers } from '../../../utils/helpers/inputValidation.ts';
import UiFileUpload from '../../../lib/UiFileUpload.tsx';
import UiButton from '../../../lib/UiButton.tsx';

interface FormValues {
    fullName: string;
    email: string;
    linkedIn: File | null;
    resume: File | [];
    whyStayTransit: string;
    phone: {
        countryCode: string;
        phoneNumber: string;
    };
    consent: boolean;
}

interface RegisterFormProps {
    redirect?: string | null;
}

const JoinUsForm: React.FC<RegisterFormProps> = () => {
    const validationSchema = Yup.object({
        fullName: Yup.string().required('Full name is required'),
        email: Yup.string().email('Invalid email').required('Email is required'),
        resume: Yup.array()
            .of(Yup.string().url('Invalid file URL'))
            .min(1, 'Resume is required')
            .required('Resume is required'),

        whyStayTransit: Yup.string().required('Why StayTransit is required'),
        consent: Yup.boolean().oneOf([true], 'You must agree to be contacted').required('Agreement is required'),
        phone: Yup.object({
            countryCode: Yup.string().required('Country code is required'),
            phoneNumber: Yup.string()
                .required('Phone number is required')
                .test('valid-length', function (value) {
                    const { countryCode } = this.parent;
                    const selected = countryCodesOptions.find(c => c.value === countryCode);
                    const minLength = selected?.phoneLength || 10;
                    if (!value) return this.createError({ message: 'Phone number is required' });
                    if (value.length !== minLength) return this.createError({ message: `Must be ${minLength} digits` });
                    return true;
                }),
        }),
    });

    const initialValues: FormValues = {
        fullName: '',
        email: '',
        linkedIn: null,
        resume: [],
        whyStayTransit: '',
        phone: {
            countryCode: '+91',
            phoneNumber: '',
        },
        consent: false,
    };

    const { clearJoinUsPopup } = useBoundStore();

    const handleSubmit = (values: FormValues) => {
        console.warn(values);
        clearJoinUsPopup();
    };

    return (
        <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={handleSubmit}>
            {({ isSubmitting, errors, touched, setFieldValue, values }) => {
                return (
                    <div>
                        {isSubmitting && <UiLoader label="Loading..." />}
                        <Form className="space-y-5">
                            <UiInput label="Full Name" name="fullName" type="text" placeholder="John" />
                            <UiInput label="Email Address" name="email" type="email" placeholder="<EMAIL>" />

                            <div className="flex flex-col mb-0">
                                <label>Phone Number</label>
                                <div className="flex gap-2">
                                    <UiSelect
                                        name="phone.countryCode"
                                        options={countryCodesOptions.map(code => ({
                                            label: code.label,
                                            value: code.value,
                                        }))}
                                        onChange={val => setFieldValue('phone.countryCode', val[0])}
                                        containerClassName="min-w-[150px]"
                                    />
                                    <UiInput
                                        name="phone.phoneNumber"
                                        placeholder="Enter phone number"
                                        onKeyDown={allowOnlyNumbers}
                                    />
                                </div>
                            </div>

                            {/* <UiInput
                            label="Resume / CV"
                            name="resume"
                            type="file"
                            accept="application/pdf"
                            onChange={(event: any) => setFieldValue("resume", event.currentTarget.files[0])}
                        /> */}

                            <UiInput
                                label="Why do you want to join StayTransit?"
                                name="whyStayTransit"
                                type="text"
                                placeholder="Please Enter"
                            />

                            <UiInput
                                label="LinkedIn Profile / Portfolio"
                                name="linkedIn"
                                type="text"
                                placeholder="Enter LinkedIn or Portfolio"
                            />

                            <Field name="resume">
                                {({ field, form }: FieldProps<string[], FormValues>) => (
                                    <UiFileUpload
                                        name={field.name}
                                        onFileUpload={urls => form.setFieldValue(field.name, urls)}
                                        label="Resume / CV"
                                        value={field.value}
                                        allowedTypes="documents"
                                    />
                                )}
                            </Field>
                            {typeof errors.resume === 'string' && touched.resume && (
                                <p className="text-sm text-red-600">{errors.resume}</p>
                            )}

                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id="agree"
                                    name="consent"
                                    checked={values.consent}
                                    onChange={e => setFieldValue('consent', e.target.checked)}
                                    className="h-4 w-4 border-gray-300 rounded"
                                />
                                <label htmlFor="agree" className="text-sm text-gray-700">
                                    I agree to be contacted by StayTransit
                                </label>
                            </div>

                            {/* Display error message for consent */}
                            {errors.consent && <p className="text-red-500 text-sm mt-1">{errors.consent}</p>}

                            <div className="text-center">
                                <UiButton
                                    type="submit"
                                    disabled={isSubmitting}
                                    className="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center"
                                >
                                    Submit
                                </UiButton>
                            </div>
                        </Form>
                    </div>
                );
            }}
        </Formik>
    );
};

export default JoinUsForm;
