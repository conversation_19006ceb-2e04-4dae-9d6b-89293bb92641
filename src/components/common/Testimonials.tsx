import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Pagination, Navigation } from 'swiper/modules';
import TestimonialCard from '../guest/card/TestimonialCard.tsx';

// Dummy testimonials data
const dummyTestimonials = [
    {
        id: 1,
        name: '<PERSON>',
        rating: 5,
        comment:
            'Absolutely amazing experience! The staff was incredibly friendly and the rooms were spotless. The breakfast buffet was delicious and the spa services were top-notch. Will definitely return!',
        date: '2024-01-15',
        location: 'New York, NY',
        service: 'Luxury Suite',
        avatar: '',
    },
    {
        id: 2,
        name: '<PERSON>',
        rating: 5,
        comment:
            "Best hotel I've ever stayed at! The concierge service was exceptional, and they helped us plan our entire city tour. The room was spacious and the view was breathtaking.",
        date: '2024-01-10',
        location: 'San Francisco, CA',
        service: 'Concierge Service',
        avatar: '',
    },
    {
        id: 3,
        name: '<PERSON>',
        rating: 4,
        comment:
            'Great location and excellent service. The restaurant had amazing food and the pool area was perfect for relaxation. Only minor issue was the WiFi speed in the lobby.',
        date: '2024-01-08',
        location: 'Miami, FL',
        service: 'Dining & Pool',
        avatar: '',
    },
    {
        id: 4,
        name: '<PERSON>',
        rating: 5,
        comment:
            'Perfect for business travel. The conference facilities were excellent and the business center was well-equipped. The staff went above and beyond to accommodate our needs.',
        date: '2024-01-05',
        location: 'Chicago, IL',
        service: 'Business Center',
        avatar: '',
    },
    {
        id: 5,
        name: 'Lisa Wang',
        rating: 5,
        comment:
            "The spa treatment was incredible! The therapists were professional and the facilities were immaculate. The hotel's attention to detail is remarkable.",
        date: '2024-01-03',
        location: 'Los Angeles, CA',
        service: 'Spa & Wellness',
        avatar: '',
    },
    {
        id: 6,
        name: 'James Wilson',
        rating: 4,
        comment:
            'Very comfortable stay with excellent amenities. The gym was well-maintained and the room service was prompt. Great value for money.',
        date: '2024-01-01',
        location: 'Seattle, WA',
        service: 'Fitness Center',
        avatar: '',
    },
];

function Testimonials() {
    return (
        <Swiper
            modules={[Autoplay, Pagination, Navigation]}
            spaceBetween={24}
            slidesPerView={1}
            breakpoints={{
                640: {
                    slidesPerView: 2,
                    spaceBetween: 24,
                },
                768: {
                    slidesPerView: 2,
                    spaceBetween: 32,
                },
                1024: {
                    slidesPerView: 3,
                    spaceBetween: 32,
                },
                1280: {
                    slidesPerView: 3,
                    spaceBetween: 40,
                },
            }}
            autoplay={{
                delay: 5000,
                disableOnInteraction: false,
            }}
            pagination={{
                clickable: true,
                el: '.swiper-pagination',
                bulletClass: 'swiper-pagination-bullet',
                bulletActiveClass: 'swiper-pagination-bullet-active',
            }}
            navigation={{
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            }}
            loop={true}
            className="testimonials-swiper"
        >
            {dummyTestimonials.map(testimonial => (
                <SwiperSlide key={testimonial.id}>
                    <div className="h-full flex items-stretch">
                        <TestimonialCard
                            name={testimonial.name}
                            rating={testimonial.rating}
                            comment={testimonial.comment}
                            date={testimonial.date}
                            location={testimonial.location}
                            avatar={testimonial.avatar}
                            service={testimonial.service}
                        />
                    </div>
                </SwiperSlide>
            ))}
        </Swiper>
    );
}

export default Testimonials;
