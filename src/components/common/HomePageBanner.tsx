import bannerImage from '../../assets/banner.png';
function HomePageBanner() {
    return (
        <div className="relative w-full mt-16">
            {/* Banner Image */}
            <img
                src={bannerImage}
                alt="Traveler looking at an airplane through a terminal window"
                className="object-fill object-top w-full h-full"
            />
            {/* Overlay for better text contrast */}
            <div className="absolute" />
            {/* Text Content */}
            <div className="absolute inset-0 flex flex-col items-start justify-center text-white p-1 sm:p-2 md:p-2 text-center max-w-7xl mx-2 xl:mx-10">
                <h1 className="text-2xl sm:text-5xl md:text-7xl font-bold xl:pb-5">Stay Transit</h1>
                <p className="text-xl sm:text-4xl md:text-5xl font-normal mt-2 xl:mt-4 max-w-md md:max-w-lg">
                    Sometimes
                </p>
                <p className="text-xl sm:text-4xl md:text-5xl font-normal mt-2 xl:mt-4 max-w-md md:max-w-lg font-semibold mt-2">
                    Long Flights
                </p>
                <p className={'text-xl sm:text-4xl md:text-5xl font-normal mt-2 xl:mt-4 max-w-md md:max-w-lg'}>
                    are like
                </p>
                <p className="text-xl sm:text-4xl md:text-5xl font-normal mt-2 xl:mt-4 max-w-md md:max-w-lg font-semibold">
                    Long Standing
                </p>
            </div>
        </div>
    );
}

export default HomePageBanner;
