import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { A11y, Navigation, Pagination, Scrollbar } from 'swiper/modules';
import img from '../../assets/h1.jpg';
import type { IAmenity } from '../../interfaces/IPackage.ts';

interface RoomDetailsProps {
    roomName: string;
    size: number;
    bedType: string;
    amenities: IAmenity[];
    images: string[];
}

const RoomDetails: React.FC<RoomDetailsProps> = ({ roomName, size, bedType, amenities, images }) => {
    return (
        <div className="rounded-lg overflow-hidden w-inherit shadow-md bg-white">
            <Swiper
                modules={[Navigation, Pagination, Scrollbar, A11y]}
                navigation
                spaceBetween={0}
                slidesPerView={1}
                loop={true}
                pagination={{ clickable: true, dynamicBullets: true }}
                className="w-full h-48 max-w-[85vw]"
            >
                {images.map((_image, index) => (
                    <SwiperSlide key={index} className="pb-8">
                        <img
                            src={img}
                            alt={`Room image ${index + 1}`}
                            width={320}
                            height={192}
                            className="w-full h-full object-cover rounded-lg"
                        />
                    </SwiperSlide>
                ))}
                <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded">
                    {images.length} PHOTOS
                </div>
            </Swiper>
            <div className="p-4">
                <h2 className="text-lg font-semibold">{roomName}</h2>
                <p className="text-sm text-gray-600">
                    ({size} | {bedType})
                </p>
                <ul className="grid grid-cols-2 gap-2 mt-2 text-sm text-gray-700">
                    {amenities.map((amenity, index) => (
                        <li key={index} className="flex items-center">
                            • {amenity.name}
                        </li>
                    ))}
                </ul>
            </div>
        </div>
    );
};

export default RoomDetails;
