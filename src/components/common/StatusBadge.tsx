import React from 'react';
import { Tooltip } from 'react-tooltip';

interface StatusBadgeProps {
    type: 'Airside' | 'Landside';
    className?: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ type, className = '' }) => {
    const config = {
        Airside: {
            label: 'Airside',
            description: 'Hotels located within airport terminals',
            bgColor: 'bg-gray-100',
            textColor: 'text-gray-700',
        },
        Landside: {
            label: 'Landside',
            description: 'Hotels located outside airport security',
            bgColor: 'bg-red-100',
            textColor: 'text-red-700',
        },
    };

    if (!type) return null;
    const { label, description, bgColor, textColor } = config[type];

    return (
        <>
            <span
                data-tooltip-id={`status-${type}-tooltip`}
                data-tooltip-content={description}
                className={`text-sm px-2 py-0.5 rounded-full font-medium ${bgColor} ${textColor} ${className}`}
            >
                {label}
            </span>
            <Tooltip id={`status-${type}-tooltip`} />
        </>
    );
};

export default StatusBadge;
