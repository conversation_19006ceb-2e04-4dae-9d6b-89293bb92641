import { Transition } from '@headlessui/react';
import type { StayBenefit } from '../../utils/constants/Features.ts';
import SectionTitle from './SectionTitle.tsx';
import FeatureCard from '../guest/card/FeatureCard.tsx';
import aboutImage from '../../assets/about.png';

function StayTransitBenefits({ benefits }: { benefits: StayBenefit[] }) {
    return (
        <Transition
            show={true}
            enter="transition ease-out duration-700"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition ease-in duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
        >
            <section>
                <div className="w-full flex flex-col md:flex-row gap-6 items-center justify-center ">
                    <div className={'flex-1 h-full flex flex-col gap-2 md:gap-6 items-start justify-center'}>
                        <SectionTitle title="Why choose StayTransit?" />
                        {benefits.map((benefit, index) => (
                            <FeatureCard
                                key={index}
                                icon={benefit.icon}
                                title={benefit.title}
                                description={benefit.description}
                            />
                        ))}
                    </div>
                    <div className={'flex-1'}>
                        <img
                            src={aboutImage}
                            alt={`About image of StayTransit`}
                            width={1000}
                            height={400}
                            className="object-cover rounded-lg"
                        />
                    </div>
                </div>
            </section>
        </Transition>
    );
}

export default StayTransitBenefits;
