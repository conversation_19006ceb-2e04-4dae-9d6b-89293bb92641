import { FieldArray } from 'formik';
import { TrashIcon } from '@heroicons/react/24/outline';
import UiInput from '../../../lib/UiInput.tsx';

const CustomRules = () => {
    return (
        <div className="bg-gray-50 p-6 border border-gray-200 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Custom Policy</h3>
            <p className="text-sm text-gray-600 mb-6">
                Add rules that define how refunds are handled based on custom timing.
            </p>

            <FieldArray name="customPolicy">
                {({ push, remove, form }) => (
                    <div className="space-y-4">
                        {form.values.customPolicy?.map((_: unknown, index: number) => (
                            <div
                                key={index}
                                className="p-4 border border-gray-300 rounded-md bg-white flex flex-col md:flex-row md:items-center gap-4"
                            >
                                <div className="grid grid-cols-1 gap-4 flex-1">
                                    <UiInput label="Title" name={`customPolicy[${index}].title`} />
                                </div>

                                <div className="flex items-center justify-center">
                                    <button
                                        type="button"
                                        onClick={() => remove(index)}
                                        className="p-2 rounded hover:bg-red-100 transition"
                                        aria-label="Remove rule"
                                    >
                                        <TrashIcon className="h-5 w-5 text-red-500" />
                                    </button>
                                </div>
                            </div>
                        ))}

                        <button
                            type="button"
                            onClick={() =>
                                push({
                                    title: '',
                                    refund_percent: '',
                                    description: '',
                                })
                            }
                            className="px-4 py-2 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700"
                        >
                            + Add Rule
                        </button>
                    </div>
                )}
            </FieldArray>
        </div>
    );
};

export default CustomRules;
