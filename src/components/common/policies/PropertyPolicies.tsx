import { type JSX, useEffect, useState, useMemo } from 'react';
import useBoundStore from '../../../store/useBoundStore.ts';
import { useApiHook } from '../../../hooks/useApi.ts';
import type { IPolicy } from '../../../interfaces/IPolicy.ts';
import UiPopup from '../../../lib/UiPopup.tsx';
import UiLoader from '../../../lib/UiLoader.tsx';

function PropertyPolicies() {
    const { guestSelectedProperty } = useBoundStore();
    const { get } = useApiHook();
    const [policies, setPolicies] = useState<Map<string, IPolicy>>(new Map());
    const [error, setError] = useState<string | null>(null);
    const [viewMore, setViewMore] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    // Fetch policies from API
    useEffect(() => {
        const fetchPolicies = async () => {
            setIsLoading(true);
            try {
                const response = await get<{ policies: IPolicy[] }>('/policy');
                if (response?.policies) {
                    setPolicies(new Map(response.policies.map(policy => [policy._id, policy])));
                    setError(null);
                } else {
                    setError('No policies found');
                }
            } catch (err) {
                setError('Failed to fetch policies');
                console.error('Error fetching policies:', err);
            } finally {
                setIsLoading(false);
            }
        };
        fetchPolicies();
    }, [get]);

    // Safely access customFields
    const customFields = guestSelectedProperty?.customFields ?? {};
    const propertyPolicies = customFields.policies ?? {};
    const cancelPolicies: IPolicy[] = (customFields.cancellationPolicy as IPolicy[]) ?? [];
    const customPolicy: IPolicy[] = (customFields.customPolicy as IPolicy[]) ?? [];

    // Memoize policy categories
    const policyCategories = useMemo(() => {
        const categories = new Map<string, { id: string; element: JSX.Element }[]>();
        Object.entries(propertyPolicies).forEach(([policyId, policyValue]) => {
            const policy = policies.get(policyId);
            if (policy) {
                const categoryName = policy.category.name;
                const description =
                    policy.input_type === 'text'
                        ? policy.description
                        : policy.options?.find(option => option.value === policyValue)?.description;
                if (description) {
                    if (!categories.has(categoryName)) {
                        categories.set(categoryName, []);
                    }
                    categories.get(categoryName)!.push({
                        id: policyId,
                        element: (
                            <li key={policyId} className="text-md text-gray-700">
                                {description}
                            </li>
                        ),
                    });
                }
            }
        });
        return categories;
    }, [policies, propertyPolicies]);

    if (isLoading) {
        return <UiLoader label={'Loading policies...'} />;
    }

    return (
        <div>
            <UiPopup isOpen={viewMore} onClose={() => setViewMore(false)} title={'Property Policies'}>
                <PolicyPopUp
                    policyCategories={policyCategories}
                    customPolicy={customPolicy}
                    cancelPolicies={cancelPolicies}
                />
            </UiPopup>

            {/* Custom Policies */}
            {customPolicy.length > 0 && (
                <div className="pt-2">
                    <p className="font-semibold">Custom Policies</p>
                    <ul className="list-disc text-md text-gray-700 pl-6">
                        {customPolicy.slice(0, 1).map((policy, index) => (
                            <li key={policy._id || `${policy.title}-${index}`}>{policy.title}</li>
                        ))}
                    </ul>
                </div>
            )}
            {/* Property Policies */}
            {Object.keys(propertyPolicies).length > 0 && (
                <div>
                    {/*<p className="font-bold text-lg">House Rules & Information</p>*/}
                    {Array.from(policyCategories)
                        .slice(0, 1)
                        .map(([categoryName, policies]) => {
                            if (policies.length === 0) return null;
                            return (
                                <div key={categoryName} className="pt-2">
                                    <p className="font-semibold">{categoryName}</p>
                                    <ul className="list-disc text-md text-gray-700 pl-6">
                                        {policies.map(({ element }) => element)}
                                    </ul>
                                </div>
                            );
                        })}
                </div>
            )}

            {/* Cancellation Policies */}
            {cancelPolicies.length > 0 && (
                <div className="pt-2">
                    <p className="font-semibold">Cancellation Policies</p>
                    <ul className="list-disc text-md text-gray-700 pl-6">
                        {cancelPolicies.slice(0, 1).map((policy, index) => (
                            <li key={policy._id || `${policy.description}-${index}`}>{policy.description}</li>
                        ))}
                    </ul>
                </div>
            )}

            {Array.from(policyCategories).length > 1 && (
                <button
                    className="text-red-600 hover:underline pl-6"
                    onClick={() => setViewMore(true)}
                    aria-label="View more policies"
                >
                    Read more
                </button>
            )}

            {/* Error Handling */}
            {error && <p className="text-red-500">{error}</p>}
        </div>
    );
}

export default PropertyPolicies;

const PolicyPopUp = ({
    policyCategories,
    customPolicy,
    cancelPolicies,
}: {
    policyCategories: Map<string, { id: string; element: JSX.Element }[]>;
    customPolicy: IPolicy[];
    cancelPolicies: IPolicy[];
}) => {
    return (
        <div className="px-5 py-2 h-140 overflow-y-auto">
            {customPolicy.length > 0 && (
                <div className="pt-2">
                    <p className="font-semibold">Custom Policies</p>
                    <ul className="list-disc text-md text-gray-700 pl-6">
                        {customPolicy.slice(0, 1).map((policy, index) => (
                            <li key={policy._id || `${policy.title}-${index}`}>{policy.title}</li>
                        ))}
                    </ul>
                </div>
            )}
            {Array.from(policyCategories).map(([categoryName, policies]) => {
                if (policies.length === 0) return null;
                return (
                    <div key={categoryName} className="pt-2">
                        <p className="font-semibold">{categoryName}</p>
                        <ul className="list-disc text-md text-gray-700 pl-6">
                            {policies.map(({ element }) => element)}
                        </ul>
                    </div>
                );
            })}
            {cancelPolicies.length > 0 && (
                <div className="pt-2">
                    <p className="font-semibold">Cancellation Policies</p>
                    <ul className="list-disc text-md text-gray-700 pl-6">
                        {cancelPolicies.slice(0, 1).map((policy, index) => (
                            <li key={policy._id || `${policy.description}-${index}`}>{policy.description}</li>
                        ))}
                    </ul>
                </div>
            )}
        </div>
    );
};
