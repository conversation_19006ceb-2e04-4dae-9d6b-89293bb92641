import { FieldArray, Field, ErrorMessage } from 'formik';
import { TrashIcon } from '@heroicons/react/24/outline';

const CancellationRules = () => {
    return (
        <div className="bg-gray-50 p-6 border border-gray-200 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
                Cancellation Policy <span className="text-red-500">*</span>
            </h3>
            <p className="text-sm text-gray-600 mb-6">
                Add rules that define how refunds are handled based on cancellation timing.
            </p>

            <FieldArray name="cancellationPolicy">
                {({ push, remove, form }) => (
                    <div className="space-y-4">
                        {form.values.cancellationPolicy?.map((_: unknown, index: number) => (
                            <div
                                key={index}
                                className="p-4 border border-gray-300 rounded-md bg-white flex flex-col md:flex-row md:items-center gap-4"
                            >
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 flex-1">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">
                                            Hours <span className="text-red-500">*</span>
                                        </label>
                                        <Field
                                            type="number"
                                            name={`cancellationPolicy[${index}].hours`}
                                            className="mt-1 block w-full border border-gray-300 rounded-md p-2 text-sm shadow-sm"
                                        />
                                        <ErrorMessage
                                            name={`cancellationPolicy[${index}].hours`}
                                            component="div"
                                            className="text-sm text-red-500 mt-1"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">
                                            Refund % <span className="text-red-500">*</span>
                                        </label>
                                        <Field
                                            type="number"
                                            name={`cancellationPolicy[${index}].refund_percent`}
                                            className="mt-1 block w-full border border-gray-300 rounded-md p-2 text-sm shadow-sm"
                                        />
                                        <ErrorMessage
                                            name={`cancellationPolicy[${index}].refund_percent`}
                                            component="div"
                                            className="text-sm text-red-500 mt-1"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">
                                            Description <span className="text-red-500">*</span>
                                        </label>
                                        <Field
                                            type="text"
                                            name={`cancellationPolicy[${index}].description`}
                                            className="mt-1 block w-full border border-gray-300 rounded-md p-2 text-sm shadow-sm"
                                        />
                                        <ErrorMessage
                                            name={`cancellationPolicy[${index}].description`}
                                            component="div"
                                            className="text-sm text-red-500 mt-1"
                                        />
                                    </div>
                                </div>

                                <div className="flex items-center justify-center">
                                    <button
                                        type="button"
                                        onClick={() => remove(index)}
                                        className="p-2 rounded hover:bg-red-100 transition"
                                        aria-label="Remove rule"
                                    >
                                        <TrashIcon className="h-5 w-5 text-red-500" />
                                    </button>
                                </div>
                            </div>
                        ))}

                        <button
                            type="button"
                            onClick={() =>
                                push({
                                    hours: '',
                                    refund_percent: '',
                                    description: '',
                                })
                            }
                            className="px-4 py-2 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700"
                        >
                            + Add Rule
                        </button>
                    </div>
                )}
            </FieldArray>
        </div>
    );
};

export default CancellationRules;
