import { forwardRef, useImperative<PERSON>andle, useState } from 'react';
import { Formik, Form, FieldArray } from 'formik';
import * as Yup from 'yup';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { XMarkIcon, InformationCircleIcon, PlusIcon } from '@heroicons/react/24/outline';
import { useLoadScript } from '@react-google-maps/api';
import useBoundStore from '../../store/useBoundStore';
import { GOOGLE_MAPS_API_KEY } from '../../constants/env.ts';
import { countryCodesOptions } from '../../utils/helpers/countryCodes.ts';
import UiLoader from '../../lib/UiLoader.tsx';
import UiInput from '../../lib/UiInput.tsx';
import {
    allowOnlyAlphabets,
    allowOnlyAlphabetsAndNumbers,
    allowOnlyNumbers,
} from '../../utils/helpers/inputValidation.ts';
import UiSelect from '../../lib/UiSelect.tsx';
import UiMapInput from '../../lib/UiMapInput.tsx';
import TimePicker from '../../lib/UiTimepicker.tsx';

interface Guest {
    firstName: string;
    lastName: string;
    email: string;
    phone: {
        countryCode: string;
        phoneNumber: string;
    };
}

interface FormValues {
    guests: Guest[];
    specialRequest: string;
    flightDetails: {
        number: string;
        from: string;
        to: string;
        arrivalDate: Date | null;
        arrivalTime: string;
        departureDate: Date | null;
        departureTime: string;
    };
}

export interface GuestDetailsRef {
    triggerSubmit: () => Promise<FormValues | null>;
    isValid: boolean;
    errors: string[];
}

type ErrorObject = {
    [key: string]: string | ErrorObject | Array<string | ErrorObject>;
};

const GuestDetailsPage = forwardRef<GuestDetailsRef>((_, ref) => {
    const { guestLocationDetails, search } = useBoundStore();
    const [formErrors, setFormErrors] = useState<string[]>([]);

    // Helper function to combine date and time
    const combineDateTime = (date: Date | null, time: string): Date | null => {
        if (!date || !time) return null;
        const [hours, minutes] = time.split(':').map(Number);
        const combined = new Date(date);
        combined.setHours(hours, minutes, 0, 0);
        return combined;
    };

    const { isLoaded, loadError } = useLoadScript({
        googleMapsApiKey: GOOGLE_MAPS_API_KEY || '',
        libraries: ['places'],
    });

    const validationSchema = Yup.object()
        .shape({
            guests: Yup.array().of(
                Yup.object().shape({
                    firstName: Yup.string().required('First name is required'),
                    lastName: Yup.string().required('Last name is required'),
                    email: Yup.string()
                        .email('Invalid email address')
                        .test(
                            'email-required-for-first-guest',
                            'Email is required for the primary guest',
                            function (value) {
                                const { path } = this;
                                const pathMatch = path.match(/guests\[(\d+)\]\.email/);
                                if (pathMatch) {
                                    const guestIndex = parseInt(pathMatch[1]);
                                    if (guestIndex === 0) {
                                        return Boolean(value && value.length > 0);
                                    }
                                }
                                return true;
                            }
                        ),
                    phone: Yup.object().shape({
                        countryCode: Yup.string().test(
                            'country-code-required-for-first-guest',
                            'Country code is required',
                            function (value) {
                                const { path } = this;
                                const pathMatch = path.match(/guests\[(\d+)\]\.phone\.countryCode/);
                                if (pathMatch) {
                                    const guestIndex = parseInt(pathMatch[1]);
                                    if (guestIndex === 0) {
                                        return Boolean(value && value.length > 0);
                                    }
                                }
                                return true;
                            }
                        ),
                        phoneNumber: Yup.string()
                            .test(
                                'phone-number-required-for-first-guest',
                                'Phone Number is required',
                                function (value) {
                                    const { path } = this;
                                    const pathMatch = path.match(/guests\[(\d+)\]\.phone\.phoneNumber/);
                                    if (pathMatch) {
                                        const guestIndex = parseInt(pathMatch[1]);
                                        if (guestIndex === 0) {
                                            return Boolean(value && value.length > 0);
                                        }
                                    }
                                    return true;
                                }
                            )
                            .test('min-length-by-country', function (value) {
                                const { parent } = this;
                                const selectedCode = countryCodesOptions.find(
                                    code => code.value === parent.countryCode
                                );
                                const minLength = selectedCode?.phoneLength || 10;
                                if (value && value.length !== minLength) {
                                    return this.createError({
                                        message: `Phone number must be exactly ${minLength} digits`,
                                    });
                                }
                                return true;
                            }),
                    }),
                })
            ),
            specialRequest: Yup.string(),
            flightDetails: Yup.object().shape({
                arrivalDate: Yup.date().required('Arrival date is required'),
                arrivalTime: Yup.string()
                    .required('Arrival time is required')
                    .test('arrival-before-checkin', 'Arrival must be before check-in time', function (value) {
                        const { arrivalDate } = this.parent;
                        const { checkInDate, checkInTime } = search;

                        if (!arrivalDate || !value || !checkInDate || !checkInTime) return true;

                        const arrivalDateTime = combineDateTime(arrivalDate, value);
                        const checkInDateTime = combineDateTime(checkInDate, checkInTime);

                        if (!arrivalDateTime || !checkInDateTime) return false;

                        return arrivalDateTime.getTime() < checkInDateTime.getTime();
                    }),
                departureDate: Yup.date()
                    .required('Departure date is required')
                    .test('departure-before-arrival', 'Departure must be before arrival', function (value) {
                        const { arrivalDate, arrivalTime, departureTime } = this.parent;
                        if (!value || !arrivalDate || !arrivalTime || !departureTime) return true;

                        const departureDateTime = combineDateTime(value, departureTime);
                        const arrivalDateTime = combineDateTime(arrivalDate, arrivalTime);

                        if (!departureDateTime || !arrivalDateTime) return true;

                        return departureDateTime < arrivalDateTime;
                    }),
                departureTime: Yup.string()
                    .required('Departure time is required')
                    .test('departure-before-checkin', 'Departure must be before check-in time', function (value) {
                        const { departureDate } = this.parent;
                        const { checkInDate, checkInTime } = search;

                        if (!departureDate || !value || !checkInDate || !checkInTime) return true;

                        const departureDateTime = combineDateTime(departureDate, value);
                        const checkInDateTime = combineDateTime(checkInDate, checkInTime);

                        if (!departureDateTime || !checkInDateTime) return false;

                        return departureDateTime.getTime() < checkInDateTime.getTime();
                    }),
                number: Yup.string().required('Flight number is required'),
                from: Yup.string().required('Departure from is required'),
                to: Yup.string().required('Departure to is required'),
            }),
        })
        .test('arrival-before-checkin', 'Arrival must be before check-in time', function (value) {
            const { flightDetails } = value;
            if (!flightDetails.arrivalDate || !flightDetails.arrivalTime || !search.checkInDate || !search.checkInTime)
                return true;

            const arrivalDateTime = combineDateTime(flightDetails.arrivalDate, flightDetails.arrivalTime);
            const checkInDateTime = combineDateTime(search.checkInDate, search.checkInTime);

            if (!arrivalDateTime || !checkInDateTime) return true;

            return arrivalDateTime < checkInDateTime;
        });

    const initialValues: FormValues = {
        guests: [
            {
                firstName: '',
                lastName: '',
                email: '',
                phone: {
                    countryCode: guestLocationDetails.countryPhoneCode || '',
                    phoneNumber: '',
                },
            },
        ],
        specialRequest: '',
        flightDetails: {
            number: '',
            from: '',
            to: search?.airport?.[0]?.label || '',
            arrivalDate: null,
            arrivalTime: '',
            departureDate: null,
            departureTime: '',
        },
    };

    return (
        <div className="bg-white">
            <Formik
                initialValues={initialValues}
                validationSchema={validationSchema}
                enableReinitialize
                onSubmit={() => {}}
            >
                {({ values, isValid, validateForm, setFieldValue, errors, setTouched, touched }) => {
                    // TODO: Handle form submission logic here if needed
                    // eslint-disable-next-line react-hooks/rules-of-hooks
                    useImperativeHandle(ref, () => ({
                        triggerSubmit: async () => {
                            const touchedFields = {
                                guests: values.guests.map(() => ({
                                    firstName: true,
                                    lastName: true,
                                    email: true,
                                    phone: {
                                        countryCode: true,
                                        phoneNumber: true,
                                    },
                                })),
                                specialRequest: true,
                                flightDetails: {
                                    number: true,
                                    from: true,
                                    to: true,
                                    arrivalDate: true,
                                    arrivalTime: true,
                                    departureDate: true,
                                    departureTime: true,
                                },
                            };
                            setTouched(touchedFields);
                            const formErrors: unknown = await validateForm();

                            // Extract error messages for display
                            const errorMessages: string[] = [];
                            const extractErrors = (obj: ErrorObject, prefix = '') => {
                                Object.keys(obj).forEach(key => {
                                    const error = obj[key];
                                    const fieldPath = prefix ? `${prefix}.${key}` : key;

                                    if (typeof error === 'string') {
                                        errorMessages.push(error);
                                    } else if (Array.isArray(error)) {
                                        error.forEach((item, index: number) => {
                                            if (typeof item === 'string') {
                                                errorMessages.push(item);
                                            } else if (item && typeof item === 'object') {
                                                extractErrors(item, `${fieldPath}[${index}]`);
                                            }
                                        });
                                    } else if (error && typeof error === 'object') {
                                        extractErrors(error, fieldPath);
                                    }
                                });
                            };

                            extractErrors(formErrors as ErrorObject);
                            setFormErrors(errorMessages);
                            return Object.keys(formErrors as ErrorObject).length === 0 ? values : null;
                        },
                        isValid,
                        errors: formErrors,
                    }));

                    return (
                        <Form className="space-y-4">
                            {loadError && <div>Error loading map</div>}
                            {!isLoaded && <UiLoader label="Loading..." />}
                            <div className={'shadow-[0px_0px_6px_4px_rgba(0,_0,_0,_0.1)] rounded-lg p-4 bg-white'}>
                                <h2 className="text-2xl font-semibold mb-4">Guest Details</h2>
                                <FieldArray name="guests">
                                    {({ push, remove }) => (
                                        <div className="space-y-4">
                                            {values.guests.map((_, index) => (
                                                <div key={index} className=" rounded-lg px-4 relative">
                                                    <>
                                                        <h3 className="text-sm mb-3 font-bold">Guest {index + 1}</h3>
                                                        {index > 0 && (
                                                            <button
                                                                type="button"
                                                                onClick={() => remove(index)}
                                                                className="absolute top-3 right-3 text-red-500"
                                                            >
                                                                <XMarkIcon className="h-6 w-6" />
                                                            </button>
                                                        )}
                                                    </>
                                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
                                                        <UiInput
                                                            label="First Name"
                                                            name={`guests[${index}].firstName`}
                                                            placeholder="John"
                                                            required
                                                            onKeyDown={allowOnlyAlphabets}
                                                        />
                                                        <UiInput
                                                            label="Last Name"
                                                            name={`guests[${index}].lastName`}
                                                            placeholder="Doe"
                                                            required
                                                            onKeyDown={allowOnlyAlphabets}
                                                        />
                                                    </div>
                                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                                        <UiInput
                                                            label="Email"
                                                            name={`guests[${index}].email`}
                                                            type="email"
                                                            placeholder="<EMAIL>"
                                                            required={index === 0}
                                                        />
                                                        <div className="flex flex-col w-full">
                                                            <label className="mr-2">
                                                                Phone {index === 0 ? '*' : ''}
                                                            </label>
                                                            <div className="flex items-start">
                                                                <UiSelect
                                                                    name={`guests[${index}].phone.countryCode`}
                                                                    options={countryCodesOptions.map(code => ({
                                                                        label: code.label,
                                                                        value: code.value,
                                                                    }))}
                                                                    onChange={value => {
                                                                        setFieldValue(
                                                                            `guests[${index}].phone.countryCode`,
                                                                            value[0] || ''
                                                                        );
                                                                    }}
                                                                    containerClassName="min-w-[150px]"
                                                                    inputClassName={'h-10 rounded-r-none border-r-0'}
                                                                />
                                                                <UiInput
                                                                    label=""
                                                                    name={`guests[${index}].phone.phoneNumber`}
                                                                    placeholder="Enter phone number"
                                                                    widthClassname={'w-full rounded-l-none border-l-0'}
                                                                    onKeyDown={allowOnlyNumbers}
                                                                />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                            <button
                                                type="button"
                                                onClick={() =>
                                                    push({
                                                        firstName: '',
                                                        lastName: '',
                                                        email: '',
                                                        phone: {
                                                            countryCode: '',
                                                            phoneNumber: '',
                                                        },
                                                    })
                                                }
                                                className="ml-4 px-4 py-2  theme-background rounded-full font-medium flex items-center gap-2"
                                            >
                                                <div className="text-white flex items-center gap-2">
                                                    {' '}
                                                    Add Another Guest <PlusIcon className={'h-4 w-4'} />
                                                </div>
                                            </button>
                                        </div>
                                    )}
                                </FieldArray>
                            </div>

                            <div className="shadow-[0px_0px_4px_4px_rgba(0,_0,_0,_0.1)] rounded-lg p-4 bg-white mt-4">
                                <h3 className="text-2xl font-semibold">Flight Details</h3>
                                <p className="text-xs text-gray-600 mb-4">
                                    Please ensure the details entered are accurate, as they will be verified during
                                    check-in
                                </p>
                                <div className="rounded-lg p-4 grid grid-cols-1 md:grid-cols-2 gap-3">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                        {/* {isLoaded && (
                                            <UiMapInput
                                                label="From *"
                                                onLocationSelect={(location) => setFieldValue('flightDetails.from', location.address)}
                                            />
                                            
                                        )} */}

                                        {isLoaded && (
                                            <div>
                                                <UiMapInput
                                                    label="From *"
                                                    onLocationSelect={location =>
                                                        setFieldValue('flightDetails.from', location.address)
                                                    }
                                                />
                                                {touched.flightDetails?.from && errors.flightDetails?.from && (
                                                    <div className="text-red-500 text-sm mt-[-12px]">
                                                        {errors.flightDetails.from}
                                                    </div>
                                                )}
                                            </div>
                                        )}

                                        <UiInput label="To" name="flightDetails.to" readonly={true} />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Departure Date and Time *
                                        </label>
                                        <div className="flex">
                                            <div className="w-1/2">
                                                <DatePicker
                                                    selected={values.flightDetails.departureDate}
                                                    onChange={date => {
                                                        setFieldValue('flightDetails.departureDate', date);
                                                        // setTouched({
                                                        //     ...touched,
                                                        //     flightDetails: {
                                                        //         ...touched.flightDetails,
                                                        //         departureDate: true,
                                                        //     },
                                                        // });
                                                    }}
                                                    onBlur={() =>
                                                        setTouched({
                                                            ...touched,
                                                            flightDetails: {
                                                                ...touched.flightDetails,
                                                                departureDate: true,
                                                            },
                                                        })
                                                    }
                                                    minDate={new Date(new Date().setDate(new Date().getDate() - 1))}
                                                    dateFormat="MM/dd/yyyy"
                                                    className="w-full border border-gray-300 h-10 px-3 text-sm text-gray-800 focus:ring-0 focus:outline-none"
                                                    placeholderText="Select Date"
                                                    required
                                                />
                                                {touched.flightDetails?.departureDate &&
                                                    errors.flightDetails?.departureDate && (
                                                        <div className="text-red-500 text-sm mt-1">
                                                            {errors.flightDetails.departureDate}
                                                        </div>
                                                    )}
                                            </div>
                                            <div className="w-1/2">
                                                <TimePicker
                                                    name="flightDetails.departureTime"
                                                    className="h-[40px] border border-gray-300 rounded-none bg-transparent text-sm text-gray-800 flex"
                                                    inputClassName="w-full h-full pl-3 pr-3 border-none focus:ring-0 focus:outline-none"
                                                    onChange={value => {
                                                        setFieldValue('flightDetails.departureTime', value);
                                                        // setTouched({
                                                        //     ...touched,
                                                        //     flightDetails: {
                                                        //         ...touched.flightDetails,
                                                        //         departureTime: true,
                                                        //     },
                                                        // });
                                                    }}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Flight Number *
                                        </label>
                                        <UiInput
                                            label=""
                                            name="flightDetails.number"
                                            placeholder="Enter flight number"
                                            onKeyDown={allowOnlyAlphabetsAndNumbers}
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Arrival Date and Time *
                                        </label>
                                        <div className="flex">
                                            <div className="w-1/2">
                                                <DatePicker
                                                    selected={values.flightDetails.arrivalDate}
                                                    onChange={date => {
                                                        setFieldValue('flightDetails.arrivalDate', date);
                                                        // setTouched({
                                                        //     ...touched,
                                                        //     flightDetails: {
                                                        //         ...touched.flightDetails,
                                                        //         arrivalDate: true,
                                                        //     },
                                                        // });
                                                    }}
                                                    onBlur={() =>
                                                        setTouched({
                                                            ...touched,
                                                            flightDetails: {
                                                                ...touched.flightDetails,
                                                                arrivalDate: true,
                                                            },
                                                        })
                                                    }
                                                    minDate={new Date(new Date().setDate(new Date().getDate() - 1))}
                                                    dateFormat="MM/dd/yyyy"
                                                    className="w-full border border-gray-300 h-10 px-3 text-sm text-gray-800 focus:ring-0 focus:outline-none"
                                                    placeholderText="Select Date"
                                                    required
                                                />
                                                {touched.flightDetails?.arrivalDate &&
                                                    errors.flightDetails?.arrivalDate && (
                                                        <div className="text-red-500 text-sm mt-1">
                                                            {errors.flightDetails.arrivalDate}
                                                        </div>
                                                    )}
                                            </div>
                                            <div className="w-1/2">
                                                <TimePicker
                                                    name="flightDetails.arrivalTime"
                                                    className="h-[40px] border border-gray-300 rounded-none bg-transparent text-sm text-gray-800 flex"
                                                    inputClassName="w-full h-full pl-3 pr-3 border-none focus:ring-0 focus:outline-none"
                                                    onChange={value => {
                                                        setFieldValue('flightDetails.arrivalTime', value);
                                                        // setTouched({
                                                        //     ...touched,
                                                        //     flightDetails: {
                                                        //         ...touched.flightDetails,
                                                        //         arrivalTime: true,
                                                        //     },
                                                        // });
                                                    }}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                    <UiInput
                                        label="Special Request"
                                        name="specialRequest"
                                        placeholder="Any special requests?"
                                    />
                                </div>
                            </div>

                            <div className="flex items-center gap-2 text-md p-3 rounded-lg my-3 border border-gray-200">
                                <InformationCircleIcon className="h-5 w-5 flex-shrink-0 text-dark" />
                                <b>We will use these details to share your booking information</b>
                            </div>
                        </Form>
                    );
                }}
            </Formik>
        </div>
    );
});

GuestDetailsPage.displayName = 'GuestDetailsPage';

export default GuestDetailsPage;
