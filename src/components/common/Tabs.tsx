import React from 'react';

interface Tab {
    id: string;
    label: string;
}

interface TabsProps {
    tabs: Tab[];
    activeTab: string;
    onChange: (tabId: string) => void;
    className?: string;
}

const Tabs: React.FC<TabsProps> = ({ tabs, activeTab, onChange, className = '' }) => {
    return (
        <div className={` ${className}`}>
            <nav className="flex space-x-4 relative" aria-label="Tabs">
                {tabs.map(tab => (
                    <button
                        key={tab.id}
                        onClick={() => onChange(tab.id)}
                        className={`py-4 px-1 text-md cursor-pointer ${
                            activeTab === tab.id
                                ? ' font-semibold text-gray-900 border-gray-900'
                                : 'border-transparent font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                    >
                        <span className={`${activeTab === tab.id ? 'theme-text' : 'text-gray-500'}`}>{tab.label}</span>
                        {activeTab === tab.id && <div className={'w-full theme-background h-[3px] rounded-full'} />}
                    </button>
                ))}
            </nav>
        </div>
    );
};

export default Tabs;
