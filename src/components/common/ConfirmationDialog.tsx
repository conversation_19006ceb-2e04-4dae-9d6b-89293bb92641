import React from 'react';
import UiPopup from '../../lib/UiPopup.tsx';

interface ConfirmationDialogProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    title?: string;
    description?: string;
    confirmText?: string;
    cancelText?: string;
    confirmButtonClassName?: string;
    cancelButtonClassName?: string;
    isLoading?: boolean;
    loadingText?: string;
}

const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
    isOpen,
    onClose,
    onConfirm,
    title = 'Confirm Action',
    description = 'Are you sure you want to proceed with this action?',
    confirmText = 'Yes',
    cancelText = 'No',
    confirmButtonClassName = '',
    cancelButtonClassName = '',
    isLoading = false,
    loadingText = 'Processing...',
}) => {
    const handleConfirm = () => {
        if (!isLoading) {
            onConfirm();
        }
    };

    const handleCancel = () => {
        if (!isLoading) {
            onClose();
        }
    };

    return (
        <UiPopup
            isOpen={isOpen}
            onClose={handleCancel}
            width="max-w-md"
            showCloseButton={false}
            closeOnOverlayClick={!isLoading}
        >
            <div className="p-6">
                {/* Title */}
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>

                {/* Description */}
                <p className="text-sm text-gray-600 mb-6">{description}</p>

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3">
                    {cancelText && (
                        <button
                            type="button"
                            onClick={handleCancel}
                            disabled={isLoading}
                            className={`cursor-pointer px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors ${cancelButtonClassName}`}
                        >
                            {cancelText}
                        </button>
                    )}
                    <button
                        type="button"
                        onClick={handleConfirm}
                        disabled={isLoading}
                        className={`cursor-pointer px-4 py-2 text-sm font-semibold text-white theme-background rounded-md  transition-colors ${confirmButtonClassName}`}
                    >
                        {isLoading ? loadingText : confirmText}
                    </button>
                </div>
            </div>
        </UiPopup>
    );
};

export default ConfirmationDialog;
