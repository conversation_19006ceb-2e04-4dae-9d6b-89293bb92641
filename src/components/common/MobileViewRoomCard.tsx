import React, { useState } from 'react';
import img from '../../assets/h1.jpg';
import type { IAmenity } from '../../interfaces/IPackage.ts';
import UiDrawer from '../../lib/UiDrawer.tsx';
import RoomDetails from './RoomDetails.tsx';

// Define the props interface for type safety
interface MobileViewRoomCardProps {
    roomName: string;
    size: number;
    bedType: string;
    amenities: IAmenity[];
    images: string[];
}

const MobileViewRoomCard: React.FC<MobileViewRoomCardProps> = ({ roomName, size, bedType, amenities, images }) => {
    const [isOpen, setIsOpen] = useState(false);

    return (
        <>
            <div className="rounded-lg flex flex-row overflow-hidden shadow-md bg-white w-full p-2">
                <div className="w-2/5 relative">
                    <img
                        src={images[0] || img}
                        alt={`Room image`}
                        width={320}
                        height={192}
                        className="h-full object-cover rounded-lg"
                    />
                </div>
                <div className="pl-4 w-3/5">
                    <h2 className="text-md font-semibold">{roomName}</h2>
                    <p className="text-sm text-gray-600">
                        ({size + `sq.ft`} | {bedType})
                    </p>
                    {/* <button
                        className="mt-4 text-blue-600 hover:underline text-sm"
                        onClick={() => setIsOpen(true)}
                    >
                        View Details
                    </button> */}
                </div>
            </div>
            <UiDrawer isOpen={isOpen} onClose={() => setIsOpen(false)} title={roomName}>
                <RoomDetails roomName={roomName} size={size} bedType={bedType} amenities={amenities} images={images} />
            </UiDrawer>
        </>
    );
};

export default MobileViewRoomCard;
