import { useEffect, useState } from 'react';
import { useApiHook } from '../../hooks/useApi.ts';
import { useNavigate } from 'react-router';
import { useToastHook } from '../../hooks/useToaster.ts';
import UiPopup from '../../lib/UiPopup.tsx';
import CurrencyFormat from './CurrencyFormat.tsx';
import type { GroupReservation } from '../../interfaces/IBooking.ts';

interface CancelPopupProps {
    isOpen: boolean;
    onClose: () => void;
    booking: GroupReservation;
    isMail?: boolean;
}

interface RefundType {
    refundableAmount: number;
    refundablePercentage: number;
    description: string;
}

const CancelPopup = ({ isOpen, onClose, booking, isMail = false }: CancelPopupProps) => {
    const { get, post } = useApiHook();
    const navigate = useNavigate();
    const { showError, showSuccess } = useToastHook();
    const [refundableAmount, setRefundableAmount] = useState<RefundType | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [isReFunding, setIsRunding] = useState(false);

    const fetchRefund = async () => {
        setIsLoading(true);
        try {
            const response = await get<RefundType>(
                `reservations/${booking._id}/refundable-amount?cancellationTime=${new Date().toISOString()}`
            );
            setRefundableAmount(response);
        } catch (error) {
            console.error('Error processing refund', error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleRefundInit = async () => {
        try {
            setIsRunding(true);
            await post(
                `payments/${booking.groupReservationId}/stripe/refund?cancellationTime=${new Date().toISOString()}`
            );
            showSuccess('Refund initiated successfully');
            onClose();
            if (isMail) {
                navigate('/guest', { replace: true });
            } else {
                navigate(0);
            }
        } catch (error) {
            showError('Failed to initiate refund');
            console.error('Error initiating refund', error);
        } finally {
            setIsRunding(false);
        }
    };

    useEffect(() => {
        if (isOpen) {
            fetchRefund();
        }
    }, [isOpen]);

    return (
        <UiPopup isOpen={isOpen} onClose={onClose} title="Cancel Booking">
            <div className="p-6 sm:p-8 max-w-md w-full bg-white rounded-2xl shadow-xl transform transition-all duration-300 ease-in-out">
                <p className="text-gray-600 text-sm sm:text-base mb-6 leading-relaxed">
                    Are you sure you want to cancel this booking? This action cannot be undone.
                </p>

                {isLoading ? (
                    <div className="text-center mb-6">
                        <div className="animate-pulse flex items-center justify-center space-x-2">
                            <div className="w-3 h-3 bg-blue-400 rounded-full animate-bounce"></div>
                            <div className="w-3 h-3 bg-blue-400 rounded-full animate-bounce delay-100"></div>
                            <div className="w-3 h-3 bg-blue-400 rounded-full animate-bounce delay-200"></div>
                        </div>
                        <p className="text-gray-500 text-sm mt-2">Loading refund details...</p>
                    </div>
                ) : refundableAmount && refundableAmount.refundableAmount > 0 ? (
                    <div className="mb-6 p-5 bg-gradient-to-br from-blue-50 to-gray-100 rounded-xl border border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-800 mb-3">Refund Details</h3>
                        <div className="space-y-3 text-sm text-gray-700">
                            <p>
                                <span className="font-medium">Refundable Amount: </span>
                                <CurrencyFormat amount={refundableAmount.refundableAmount.toFixed(2)} />
                            </p>
                            <p>
                                <span className="font-medium">Refund Percentage: </span>
                                <span className="text-orange-600 font-semibold">
                                    {refundableAmount.refundablePercentage}%
                                </span>
                            </p>
                            <p>
                                <span className="font-medium">Details: </span>
                                <span className="text-gray-600">{refundableAmount.description}</span>
                            </p>
                        </div>
                    </div>
                ) : (
                    <p className="text-gray-600 text-sm sm:text-base mb-6 bg-red-50 p-4 rounded-xl border border-red-200">
                        No refund is available for this booking.
                    </p>
                )}

                <div className="flex justify-end gap-3">
                    <button
                        className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-300 hover:shadow-md transition-all duration-200 ease-in-out transform hover:-translate-y-0.5"
                        onClick={onClose}
                    >
                        Keep Booking
                    </button>
                    <button
                        className="px-4 py-2 bg-red-500 text-white rounded-lg text-sm font-medium hover:bg-red-600 hover:shadow-md transition-all duration-200 ease-in-out transform hover:-translate-y-0.5"
                        onClick={handleRefundInit}
                        disabled={isReFunding || isLoading}
                    >
                        {isReFunding ? 'Submitting....' : 'Confirm Cancellation'}
                    </button>
                </div>
            </div>
        </UiPopup>
    );
};

export default CancelPopup;
