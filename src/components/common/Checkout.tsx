import { useRef, useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import CurrencyFormat from './CurrencyFormat';
import type { IPackage } from '../../interfaces/IPackage.ts';
import { useApiHook } from '../../hooks/useApi.ts';
import useBoundStore from '../../store/useBoundStore.ts';
import GuestDetailsPage, { type GuestDetailsRef } from './GuestDetailsPage.tsx';
import { useToastHook } from '../../hooks/useToaster.ts';
import { formatApiDate, formatDate } from '../../utils/helpers/dateHelpers.ts';
import type { GroupReservationResponse } from '../../interfaces/IBooking.ts';
import UiButton from '../../lib/UiButton.tsx';

const stripePromise = loadStripe(
    'pk_test_51KYjskCmnzrHbnSWwTX97glhGp1PLM1ni94uqDxpyIGaW8xyGGDVa4exlaiNWIqpXhiqcTCPotxyDw3S7hHf96oU00nfeq6Zis'
);

type GroupedCartItem = IPackage & { quantity: number; totalPrice: number };

const Checkout = () => {
    const guestDetailsRef = useRef<GuestDetailsRef>(null);
    const [isConsentChecked, setIsConsentChecked] = useState(false);
    const [formErrors, setFormErrors] = useState<string[]>([]);
    const [loading, setLoading] = useState(false);
    const { cart, search, guestSelectedProperty } = useBoundStore();
    const { post } = useApiHook();
    const { showError } = useToastHook();

    const initialValues = search;

    const calculateTotals = () => {
        const subtotal = cart.reduce((acc, c) => acc + (c.rateCardPrice ? c.rateCardPrice : c.price), 0);
        const taxAmount = cart.reduce(
            (acc, c) =>
                acc +
                (c.rateCardPrice ? c.rateCardPrice : c.price) *
                    (c.taxes.reduce((a, b) => a + Number(b.value), 0) / 100),
            0
        );
        return {
            subtotal,
            taxAmount,
            grandTotal: subtotal + Number(taxAmount.toFixed(2)),
        };
    };

    const handleProceedToPay = async () => {
        try {
            setLoading(true);
            if (!guestDetailsRef.current) return;
            if (!isConsentChecked) {
                showError('Please agree to the terms and conditions');
                return;
            }
            const formData = await guestDetailsRef.current.triggerSubmit();
            if (!formData) {
                // Get errors from the ref and display them
                const errors = guestDetailsRef.current?.errors || [];
                setFormErrors(errors);
                if (errors.length > 0) {
                    showError('Please fix the form errors before proceeding');
                }
                return;
            }

            // Clear any previous errors
            setFormErrors([]);

            if (cart.length === 0) {
                throw new Error('Cart is empty');
            }

            const reservations = cart.map(c => ({
                packageId: c._id,
                startDateTime: new Date(
                    `${formatApiDate(initialValues.checkInDate) + ' ' + initialValues.checkInTime}`
                ).toUTCString(),
                endDateTime: new Date(
                    `${formatApiDate(initialValues.checkOutDate) + ' ' + initialValues.checkOutTime}`
                ).toUTCString(),
                noOfAdults: initialValues.noOfAdults,
                noOfChildren: initialValues.noOfChildren,
                guestDetails: formData.guests,
                flightDetails: {
                    number: formData.flightDetails.number,
                    from: formData.flightDetails.from,
                    to: formData.flightDetails.to,
                    arrivalDateTime: new Date(
                        `${formatApiDate(formData.flightDetails.arrivalDate || new Date()) + ' ' + formData.flightDetails.arrivalTime}`
                    ).toUTCString(),
                    departureDateTime: new Date(
                        `${formatApiDate(formData.flightDetails.departureDate || new Date()) + ' ' + formData.flightDetails.departureTime}`
                    ).toUTCString(),
                },
                specialRequest: formData.specialRequest,
            }));

            const response = await post<GroupReservationResponse>(`reservations/${cart[0].propertyId}/block`, {
                reservations,
                bookerDetails: { ...formData.guests[0], email: formData.guests[0].email || '<EMAIL>' },
            });

            const paymentResponse = await post<{ sessionId: string }>(`payments/stripe/checkout`, {
                currency: 'usd',
                reservationId: response.bookingDetails.groupReservationId,
                propertyId: cart[0].propertyId,
            });
            const stripe = await stripePromise;
            stripe?.redirectToCheckout({ sessionId: paymentResponse.sessionId });
        } catch (error: unknown) {
            const err = error as Error;
            showError(err.message);
        } finally {
            setLoading(false);
        }
    };

    const { subtotal, taxAmount, grandTotal } = calculateTotals();

    // Group cart items by _id with proper typing
    const groupedCart = cart.reduce<Record<string, GroupedCartItem>>((acc, item) => {
        const key = item._id;
        if (!acc[key]) {
            acc[key] = { ...item, quantity: 1, totalPrice: item.price };
        } else {
            acc[key].quantity += 1;
            acc[key].totalPrice += item.price;
        }
        return acc;
    }, {});
    const groupedCartArray: GroupedCartItem[] = Object.values(groupedCart);

    return (
        <div className="bg-white">
            <GuestDetailsPage ref={guestDetailsRef} />
            <div className="space-y-4 mt-4">
                <div className="p-6 border border-gray-400 rounded-2xl  text-black flex flex-col gap-6">
                    <h3 className="text-center text-lg font-semibold mb-2">{guestSelectedProperty?.name}</h3>
                    <div className="grid grid-cols-2 gap-6">
                        <div className={'border-2 border-gray-200 rounded-md p-2 flex flex-col pl-5'}>
                            <p className={'text-md'}>Check In</p>
                            <p className={'text-sm text-gray-600'}>{formatDate(search.checkInDate)}</p>
                        </div>
                        <div className={'border-2 border-gray-200 rounded-md p-2 flex flex-col pl-5'}>
                            <p className={'text-md'}>Check Out</p>
                            <p className={'text-sm text-gray-600'}>{formatDate(search.checkOutDate)}</p>
                        </div>
                    </div>

                    {groupedCartArray.map((c, i) => (
                        <div key={`${c._id}-${i}`} className="flex justify-between items-center py-2">
                            <div>
                                <div className="font-semibold text-lg">{c.name}</div>
                                <div className="text-sm text-gray-500">{c.duration} hours</div>
                                {c.quantity > 1 && <span className="text-xs text-gray-500">x {c.quantity}</span>}
                            </div>
                            <span className="font-semibold">
                                <CurrencyFormat amount={(c.rateCardPrice || c.totalPrice).toFixed(2)} />
                            </span>
                        </div>
                    ))}

                    <div className="flex flex-col gap-1">
                        <div className="flex justify-between items-center">
                            <span className="font-semibold">Sub total</span>
                            <span className="font-semibold">
                                <CurrencyFormat amount={subtotal.toFixed(2)} />
                            </span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-xs text-gray-500">Tax & Fees</span>
                            <span className="font-semibold">
                                <CurrencyFormat amount={taxAmount.toFixed(2)} />
                            </span>
                        </div>
                        <div className="flex justify-between items-center mt-2">
                            <span className="font-bold text-base">Grand total</span>
                            <span className="font-bold text-lg theme-text">
                                <CurrencyFormat amount={grandTotal.toFixed(2)} />
                            </span>
                        </div>
                    </div>

                    <div className="flex items-center mt-4">
                        <input
                            type="checkbox"
                            className="w-4 h-4 rounded mr-2 accent-red-600"
                            onChange={e => setIsConsentChecked(e.target.checked)}
                            checked={isConsentChecked}
                        />
                        <span className="text-xs">
                            I agree to the{' '}
                            <a href="#" className="underline">
                                terms and conditions
                            </a>{' '}
                            and consent to the processing of my personal data.
                        </span>
                    </div>
                </div>

                {/* Error Display */}
                {formErrors.length > 0 && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h4 className="text-red-800 font-medium mb-2">Please fix the following errors:</h4>
                        <ul className="list-disc list-inside space-y-1">
                            {formErrors.map((error, index) => (
                                <li key={index} className="text-red-700 text-sm">
                                    {error}
                                </li>
                            ))}
                        </ul>
                    </div>
                )}
                <div className={'w-full flex items-center justify-center gap-4'}>
                    <UiButton
                        className="text-xl px-5 transition-colors duration-200 disabled:bg-blue-300 disabled:cursor-not-allowed font-medium"
                        // disabled={!isConsentChecked || !guestDetailsRef.current?.isValid || cart.length === 0}
                        onClick={handleProceedToPay}
                        loading={loading}
                    >
                        Proceed To Pay
                    </UiButton>
                </div>
            </div>
        </div>
    );
};

export default Checkout;
