import React, { useEffect, useRef } from 'react';

interface GuestsRoomsSelectorProps {
    show: boolean;
    onClose: () => void;
    noOfAdults: number;
    noOfChildren: number;
    rooms: number;
    setAdults: (value: number) => void;
    setChildren: (value: number) => void;
    setRooms: (value: number) => void;
}

const GuestsRoomsSelector: React.FC<GuestsRoomsSelectorProps> = ({
    show,
    onClose,
    noOfAdults,
    noOfChildren,
    rooms,
    setAdults,
    setChildren,
    setRooms,
}) => {
    const popoverRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
                onClose();
            }
        };

        const handleEscape = (event: KeyboardEvent) => {
            if (event.key === 'Escape') {
                onClose();
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        document.addEventListener('keydown', handleEscape);

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('keydown', handleEscape);
        };
    }, [onClose]);

    if (!show) return null;

    const renderControl = (label: string, value: number, setValue: (val: number) => void) => (
        <div className="flex items-center justify-between mb-4">
            <span className="font-medium">{label}</span>
            <div className="flex items-center gap-2">
                <button
                    type="button"
                    className="w-8 h-8 flex items-center justify-center bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-full shadow-sm"
                    onClick={() => setValue(Math.max(0, value - 1))}
                >
                    -
                </button>
                <span className="w-8 text-center">{value}</span>
                <button
                    type="button"
                    className="w-8 h-8 flex items-center justify-center bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-full shadow-sm"
                    onClick={() => setValue(value + 1)}
                >
                    +
                </button>
            </div>
        </div>
    );

    return (
        <div ref={popoverRef} className="absolute z-10 bg-white border shadow-lg p-4 rounded-md w-64 ">
            {renderControl('Adults', noOfAdults, setAdults)}
            {renderControl('Children', noOfChildren, setChildren)}
            {renderControl('Rooms', rooms, setRooms)}
        </div>
    );
};

export default GuestsRoomsSelector;
