interface SectionTitleProps {
    title: string;
    subtitle?: string;
    margin?: string;
    appendClass?: string;
}

function SectionTitle({ title, subtitle, appendClass, margin = 'mb-6' }: SectionTitleProps) {
    return (
        <div className={`${margin} ${appendClass}`}>
            <h2 className="text-3xl font-bold mb-0">{title}</h2>
            {subtitle && <p className="text-base text-gray-400">{subtitle}</p>}
        </div>
    );
}

export default SectionTitle;
