import React from 'react';
import { Menu, Transition } from '@headlessui/react';
import { UserCircleIcon } from '@heroicons/react/24/outline';
import type { IUser } from '../../interfaces/IUser.ts';
import { Link, useParams } from 'react-router';

interface UserMenuProps {
    user: IUser;
    isDashboard?: boolean;
    onSignOut: () => void;
}

const UserMenu: React.FC<UserMenuProps> = ({ user, onSignOut, isDashboard = false }) => {
    const { propertyId } = useParams();

    return (
        <Menu as="div" className="relative inline-block text-left">
            <Menu.Button className="flex items-center space-x-2 focus:outline-none group">
                <span className="inline-flex items-center justify-center w-9 h-9 rounded-full bg-gradient-to-br from-red-50 to-red-100 text-red-700 font-semibold uppercase shadow-sm group-hover:from-red-100 group-hover:to-red-200 transition-all duration-200">
                    {`${user?.firstName?.[0] ?? ''}${user?.lastName?.[0] ?? ''}`}
                </span>
            </Menu.Button>

            <Transition
                enter="transition duration-100 ease-out"
                enterFrom="transform scale-95 opacity-0"
                enterTo="transform scale-100 opacity-100"
                leave="transition duration-75 ease-in"
                leaveFrom="transform scale-100 opacity-100"
                leaveTo="transform scale-95 opacity-0"
            >
                <Menu.Items className="absolute right-0 mt-2 w-56 origin-top-right bg-white border border-gray-100 rounded-lg shadow-lg focus:outline-none z-10 divide-y divide-gray-100">
                    <div className="p-2">
                        <div className="px-3 py-2 text-sm text-gray-500">
                            Signed in as
                            <p className="font-medium text-gray-900 truncate">{user?.email}</p>
                        </div>
                    </div>
                    <div className="p-2">
                        <Menu.Item>
                            {({ active }) => (
                                <Link
                                    to={isDashboard ? `/${propertyId}/merchant/profile` : `/guest/profile`}
                                    className={`flex items-center px-3 py-2 rounded-md text-sm ${
                                        active ? 'bg-red-50 text-red-700' : 'text-gray-700'
                                    }`}
                                >
                                    <UserCircleIcon className="w-5 h-5 mr-2" />
                                    Profile
                                </Link>
                            )}
                        </Menu.Item>
                        {/*{isGuest && (*/}
                        {/*    <Menu.Item>*/}
                        {/*        {({ active }) => (*/}
                        {/*            <Link*/}
                        {/*                to={`/guest/profile`}*/}
                        {/*                className={`flex items-center px-3 py-2 rounded-md text-sm ${*/}
                        {/*                    active ? 'bg-red-50 text-red-700' : 'text-gray-700'*/}
                        {/*                }`}*/}
                        {/*            >*/}
                        {/*                <CalendarIcon className="w-5 h-5 mr-2" />*/}
                        {/*                My Bookings*/}
                        {/*            </Link>*/}
                        {/*        )}*/}
                        {/*    </Menu.Item>*/}
                        {/*)}*/}
                        {/* {!isDashboard && <Menu.Item>
                            {({ active }) => (
                                <button
                                    className={`w-full flex items-center px-3 py-2 rounded-md text-sm ${active ? "bg-blue-50 text-blue-700" : "text-gray-700"
                                        }`}
                                >
                                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    Settings
                                </button>
                            )}
                        </Menu.Item>} */}
                    </div>
                    <div className="p-2">
                        <Menu.Item>
                            {({ active }) => (
                                <button
                                    onClick={onSignOut}
                                    className={`w-full flex items-center px-3 py-2 rounded-md text-sm ${
                                        active ? 'bg-red-50 text-red-700' : 'text-red-600'
                                    }`}
                                >
                                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                                        />
                                    </svg>
                                    Sign Out
                                </button>
                            )}
                        </Menu.Item>
                    </div>
                </Menu.Items>
            </Transition>
        </Menu>
    );
};

export default UserMenu;
