import React, { useState } from 'react';
import useBoundStore from '../../store/useBoundStore.ts';
import { Link, useNavigate } from 'react-router';
import Logo from '../common/Logo.tsx';
import UserMenu from '../common/UserMenu.tsx';

const OnboardingHeader: React.FC = () => {
    const [isSignedIn, _setIsSignedIn] = useState(true);
    const { loggedUser, clearLoggedUser } = useBoundStore();

    const navigate = useNavigate();
    const logOut = () => {
        clearLoggedUser();
        navigate('/guest', { replace: true });
    };

    return (
        <>
            {isSignedIn && (
                <header className="bg-white shadow sticky top-0 z-50">
                    <div className="container mx-auto px-4 py-3 flex items-center justify-between">
                        {/* Logo */}
                        <Link to="/admin/live-tracking" className="cursor-pointer">
                            <Logo size={'lg'} />
                        </Link>

                        <div className="ml-auto">
                            <UserMenu user={loggedUser.user} onSignOut={logOut} />
                        </div>
                    </div>
                </header>
            )}
        </>
    );
};

export default OnboardingHeader;
