import { useEffect, useState } from 'react';
import { Formik, Form, Field, FieldArray, type FieldProps } from 'formik';
import * as Yup from 'yup';
import {
    DocumentTextIcon,
    IdentificationIcon,
    BanknotesIcon,
    InformationCircleIcon,
} from '@heroicons/react/24/outline';
import { useNavigate, useParams } from 'react-router';
import { useApiHook } from '../../hooks/useApi.ts';
import { useToastHook } from '../../hooks/useToaster.ts';
import type { IDomainValue } from '../../interfaces/IDomainValue.ts';
import type { ICompany } from '../../interfaces/ICompany.ts';
import { domainValue } from '../../utils/constants/domainValues.ts';
import SectionTitle from '../common/SectionTitle.tsx';
import UiInput from '../../lib/UiInput.tsx';
import { allowOnlyAlphabetsAndNumbers, allowOnlyNumbers } from '../../utils/helpers/inputValidation.ts';
import UiSelect from '../../lib/UiSelect.tsx';
import UiRadioGroupField from '../../lib/UiRadioGroupField.tsx';
import UiFileUpload from '../../lib/UiFileUpload.tsx';
import UiBackButton from '../../lib/UiBackButton.tsx';
import UiNextButton from '../../lib/UiNextButton.tsx';
import type { BusinessDetails } from '../../interfaces/IBusiness.ts';
import { XMarkIcon } from '@heroicons/react/24/solid';

const validationSchema = Yup.object({
    registrationNumber: Yup.string().required('Registration Number is required'),
    businessTaxId: Yup.string().required('Business Tax ID is required'),
    financialYearStart: Yup.string().required('Start month is required'),
    financialYearEnd: Yup.string().required('End month is required'),
    idType: Yup.string().required('ID Type is required'),
    idCardNumber: Yup.string().required('ID Card Number is required'),
    idProof: Yup.mixed().required('ID Proof is required'),
    bankAccountNumber: Yup.string().required('Bank Account Number is required'),
    reEnterBankAccountNumber: Yup.string()
        .oneOf([Yup.ref('bankAccountNumber')], 'Bank Account Numbers must match')
        .required('Please re-enter your Bank Account Number'),
    receivingBankCodeCode: Yup.string().required('Receiving Bank Code is required'),
    bankName: Yup.string().required('Bank Name is required'),
    salesTax: Yup.array()
        .of(
            Yup.object({
                name: Yup.string().required('Name is Required'),
                value: Yup.string().required('Value is Required'),
            })
        )
        .min(1, 'At least one Sales Tax entry is required'),
    commission: Yup.object({
        percentage: Yup.string().required('Commission Percentage is required'),
        frequency: Yup.string().required('Commission Frequency is required'),
    }),
    chargesTypes: Yup.array().of(Yup.string()).required('Charges Types are required'),
});

const BusinessDetailsPage = () => {
    const { propertyId } = useParams();
    const { patch, get } = useApiHook();
    const { showSuccess, showError } = useToastHook();
    const [property, setProperty] = useState<ICompany | null>(null);
    const navigate = useNavigate();
    const [bankNameOptions, setBankNameOptions] = useState<IDomainValue[]>([]);
    const [nameOptions, setNameOptions] = useState<IDomainValue[]>([]);
    const [chargesTypes, setChargesTypes] = useState<IDomainValue[]>([]);
    const [commissionFrequency, setCommissionFrequency] = useState<IDomainValue[]>([]);
    const isOnboarding = location.pathname.includes('onboarding');

    const fetchProperty = async () => {
        const response = await get<ICompany>(`/properties/${propertyId}`);
        setProperty(response);
    };

    const fetchBankNameOptions = async () => {
        const response = await get<IDomainValue[]>(`/domain-values/categoryId/${domainValue.Banks}`);
        setBankNameOptions(response);
    };

    const fetchChargesTypes = async () => {
        const response = await get<IDomainValue[]>(`/domain-values/categoryId/${domainValue.Charges}`);
        setChargesTypes(response);
    };

    const fetchCommissionFrequency = async () => {
        const response = await get<IDomainValue[]>(`/domain-values/categoryId/${domainValue.CommissionFrequency}`);
        setCommissionFrequency(response);
    };

    const fetchNameOptions = async () => {
        const response = await get<IDomainValue[]>(`/domain-values/categoryId/${domainValue.salesTax}`);
        setNameOptions(response);
    };

    const businessDetails = (property?.customFields?.businessDetails as BusinessDetails) ?? {};

    const initialValues = {
        registrationNumber: businessDetails.registrationNumber ?? '',
        businessTaxId: businessDetails.businessTaxId ?? '',
        financialYearStart: businessDetails.financialYearStart ?? '',
        financialYearEnd: businessDetails.financialYearEnd ?? '',
        registrationdoc: businessDetails.registrationdoc ?? '',
        registrationDocument:
            Array.isArray(businessDetails.registrationDocument) && businessDetails.registrationDocument.length > 0
                ? businessDetails.registrationDocument
                : [],
        idType: businessDetails.idType ?? '',
        otherIdType: businessDetails.otherIdType ?? '',
        idCardNumber: businessDetails.idCardNumber ?? '',
        idProof:
            Array.isArray(businessDetails.idProof) && businessDetails.idProof.length > 0 ? businessDetails.idProof : [],
        bankAccountNumber: businessDetails.bankAccountNumber ?? '',
        reEnterBankAccountNumber: businessDetails.reEnterBankAccountNumber ?? '',
        receivingBankCodeCode: businessDetails.receivingBankCodeCode ?? '',
        bankName: businessDetails.bankName ?? '',
        salesTax:
            Array.isArray(businessDetails.salesTax) && businessDetails.salesTax.length > 0
                ? businessDetails.salesTax
                : [{ name: '', value: '' }],
        commission: businessDetails.commission ?? {
            percentage: '',
            frequency: '',
        },
        chargesTypes: businessDetails.chargesTypes ?? [],
    };

    const handleSubmit = async (values: typeof initialValues) => {
        try {
            const response = await patch(`/properties/${propertyId}/customFields`, {
                customFields: {
                    ...property?.customFields,
                    businessDetails: values,
                },
            });
            if (response) {
                showSuccess('Business details updated successfully');
            }
            if (isOnboarding) {
                navigate(`/admin/merchant-management`);
            }
        } catch (_error) {
            showError('Failed to update Business Details Info');
        }
    };

    // Month options for financial year
    const monthOptions = Array.from({ length: 12 }, (_, i) => {
        const month = new Date(0, i).toLocaleString('default', { month: 'long' });
        return { value: month, label: month };
    });

    // ID Type options
    const idTypeOptions = [
        { value: 'National ID', label: 'National ID (Recommended)' },
        { value: 'Passport', label: 'Passport' },
        { value: 'Driver License', label: 'Driver License' },
        { value: 'Other', label: 'Other' },
    ];

    useEffect(() => {
        fetchProperty();
        fetchBankNameOptions();
        fetchChargesTypes();
        fetchCommissionFrequency();
        fetchNameOptions();
    }, []);

    return (
        <div className="mt-2 p-6 bg-white rounded-lg">
            <div className="mb-8">
                <SectionTitle title={'Business Details'} subtitle="Kindly configure your business settings" />
            </div>

            <Formik
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
                enableReinitialize
            >
                {({ values, errors, touched }) => (
                    <Form className="space-y-6">
                        {/* Registration Details Section */}
                        <div className="border-b border-gray-200 pb-6">
                            <h2 className="text-lg font-medium text-gray-900 flex items-center mb-4">
                                <DocumentTextIcon className="h-5 w-5 text-blue-500 mr-2" />
                                Registration Details
                            </h2>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <UiInput
                                    name="registrationNumber"
                                    label="Registration Number *"
                                    type="text"
                                    placeholder="Enter registration number"
                                    onKeyDown={allowOnlyAlphabetsAndNumbers}
                                />

                                <UiInput
                                    name="businessTaxId"
                                    label="Business Tax ID *"
                                    type="text"
                                    placeholder="Enter business tax ID"
                                    onKeyDown={allowOnlyAlphabetsAndNumbers}
                                />

                                <div className="md:col-span-2  ">
                                    <h3 className="text-md font-medium text-gray-900 mb-2">Sales Tax Details *</h3>
                                    <FieldArray name="salesTax">
                                        {({ push, remove }) => (
                                            <div>
                                                {values.salesTax.map((_, index) => (
                                                    <div
                                                        key={index}
                                                        className="flex items-center gap-2 p-2 mb-2 bg-gray-50 border border-gray-100 relative"
                                                    >
                                                        <div className={'w-full'}>
                                                            <UiSelect
                                                                name={`salesTax.${index}.name`}
                                                                label="Sales Tax ID"
                                                                required
                                                                options={nameOptions.map(name => ({
                                                                    value: name._id,
                                                                    label: name.name,
                                                                }))}
                                                            />
                                                        </div>
                                                        <div className={'w-full'}>
                                                            <UiInput
                                                                name={`salesTax.${index}.value`}
                                                                label="Value"
                                                                required
                                                                placeholder="Enter value"
                                                                onKeyDown={allowOnlyAlphabetsAndNumbers}
                                                            />
                                                        </div>
                                                        <div className="flex items-end absolute right-1 top-1">
                                                            <button
                                                                type="button"
                                                                className="text-red-600 hover:text-red-800"
                                                                onClick={() => remove(index)}
                                                                disabled={values.salesTax.length === 1}
                                                            >
                                                                <XMarkIcon className={'w-5 h-5'} />
                                                            </button>
                                                        </div>
                                                    </div>
                                                ))}
                                                <button
                                                    type="button"
                                                    className="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                                                    onClick={() => push({ name: '', value: '' })}
                                                >
                                                    Add Sales Tax
                                                </button>
                                                {typeof errors.salesTax === 'string' && touched.salesTax && (
                                                    <p className="text-sm text-red-600 mt-2">{errors.salesTax}</p>
                                                )}
                                            </div>
                                        )}
                                    </FieldArray>
                                </div>

                                <div className="md:col-span-2">
                                    <h2 className="text-lg font-medium text-gray-900 flex items-center mb-4">
                                        <DocumentTextIcon className="h-5 w-5 text-blue-500 mr-2" />
                                        Financial Year *
                                    </h2>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <UiSelect
                                            name="financialYearStart"
                                            label="Start Month *"
                                            options={monthOptions}
                                        />
                                        <UiSelect name="financialYearEnd" label="End Month *" options={monthOptions} />
                                    </div>
                                </div>

                                <UiRadioGroupField
                                    name="registrationdoc"
                                    label="Do you have the registration document?"
                                    required
                                    options={[
                                        { value: 'true', label: 'Yes' },
                                        { value: 'false', label: 'No' },
                                    ]}
                                />
                                {values.registrationdoc === 'true' && (
                                    <div className="md:col-span-2">
                                        <Field name="registrationDocument">
                                            {({ field, form }: FieldProps) => (
                                                <UiFileUpload
                                                    name={field.name}
                                                    onFileUpload={urls => form.setFieldValue(field.name, urls)}
                                                    label="Registration Document"
                                                    value={field.value}
                                                    allowedTypes="all"
                                                    multiple={true}
                                                />
                                            )}
                                        </Field>

                                        {typeof errors.registrationDocument === 'string' &&
                                            touched.registrationDocument && (
                                                <p className="text-sm text-red-600">{errors.registrationDocument}</p>
                                            )}
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Identification Section */}
                        <div className="border-b border-gray-200 pb-6">
                            <h2 className="text-lg font-medium text-gray-900 flex items-center mb-4">
                                <IdentificationIcon className="h-5 w-5 text-blue-500 mr-2" />
                                Identification
                            </h2>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <UiSelect name="idType" label="ID Type *" options={idTypeOptions} />
                                {values.idType === 'Other' && (
                                    <UiInput
                                        name="otherIdType"
                                        label="Specify ID Type *"
                                        type="text"
                                        placeholder="Enter document type"
                                        onKeyDown={allowOnlyAlphabetsAndNumbers}
                                    />
                                )}

                                <UiInput
                                    name="idCardNumber"
                                    label="ID Card Number *"
                                    type="text"
                                    placeholder="Enter ID card number"
                                    onKeyDown={allowOnlyAlphabetsAndNumbers}
                                />

                                <Field name="idProof">
                                    {({ field, form }: FieldProps) => (
                                        <UiFileUpload
                                            name={field.name}
                                            onFileUpload={urls => form.setFieldValue(field.name, urls)}
                                            label="ID Proof"
                                            value={field.value}
                                            allowedTypes="all"
                                            multiple={true}
                                        />
                                    )}
                                </Field>

                                {typeof errors.idProof === 'string' && touched.idProof && (
                                    <p className="text-sm text-red-600">{errors.idProof}</p>
                                )}
                            </div>
                        </div>

                        {/* Commission Details Section */}
                        <div>
                            <h2 className="text-lg font-medium text-gray-900 flex items-center mb-4">
                                <InformationCircleIcon className="h-5 w-5 text-blue-500 mr-2" />
                                Commission Details
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <UiInput
                                    name="commission.type"
                                    label="Type *"
                                    type="text"
                                    readonly={true}
                                    placeholder="Per Transaction"
                                />

                                <UiInput
                                    name="commission.percentage"
                                    label="Percentage Charged by platform *"
                                    type="text"
                                    placeholder="Enter percentage"
                                    onKeyDown={allowOnlyNumbers}
                                />
                                <UiSelect
                                    name="commission.frequency"
                                    label="Pay out Frequency"
                                    options={commissionFrequency.map(service => ({
                                        value: service._id,
                                        label: service.name,
                                    }))}
                                />
                            </div>
                        </div>

                        {/* Service Charges Section */}
                        <div>
                            <h2 className="text-lg font-medium text-gray-900 flex items-center mb-4">
                                <InformationCircleIcon className="h-5 w-5 text-blue-500 mr-2" />
                                Service Charges
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <UiSelect
                                    name="chargesTypes"
                                    label="Charges Types"
                                    multiple={true}
                                    options={chargesTypes.map(service => ({
                                        value: service._id,
                                        label: service.name,
                                    }))}
                                />
                            </div>
                        </div>

                        {/* Bank Details Section */}
                        <div>
                            <h2 className="text-lg font-medium text-gray-900 flex items-center mb-4">
                                <BanknotesIcon className="h-5 w-5 text-blue-500 mr-2" />
                                Bank Details
                            </h2>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <UiInput
                                    name="bankAccountNumber"
                                    label="Bank Account Number *"
                                    type="password"
                                    placeholder="Enter account number"
                                    onKeyDown={allowOnlyAlphabetsAndNumbers}
                                />

                                <UiInput
                                    name="reEnterBankAccountNumber"
                                    label="Re-enter Bank Account Number *"
                                    type="password"
                                    placeholder="Re-enter account number"
                                    onKeyDown={allowOnlyAlphabetsAndNumbers}
                                />

                                <UiInput
                                    name="receivingBankCodeCode"
                                    label="Receiving Bank Code *"
                                    type="text"
                                    placeholder="Enter Receiving Bank Code"
                                    onKeyDown={allowOnlyAlphabetsAndNumbers}
                                />

                                <UiSelect
                                    name="bankName"
                                    label="Bank Name"
                                    options={bankNameOptions.map(bank => ({
                                        value: bank._id,
                                        label: bank.name,
                                    }))}
                                />
                            </div>
                        </div>

                        <div className="flex justify-between mt-6 gap-4">
                            {isOnboarding && <UiBackButton />}
                            <UiNextButton label={'Submit'} />
                        </div>
                    </Form>
                )}
            </Formik>
        </div>
    );
};

export default BusinessDetailsPage;
