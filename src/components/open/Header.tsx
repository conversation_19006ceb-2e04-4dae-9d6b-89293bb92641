import React, { useEffect, useState } from 'react';
import useBoundStore from '../../store/useBoundStore.ts';
import { redirect } from 'react-router';
import Logo from '../common/Logo.tsx';

const OpenHeader: React.FC = () => {
    const { clearCart, updateGuestProperty } = useBoundStore();
    const [isScrolled, setIsScrolled] = useState(false);

    useEffect(() => {
        const handleScroll = () => {
            setIsScrolled(window.scrollY > 10);
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    return (
        <>
            <header
                className={`bg-white fixed w-full top-0 z-50 h-[var(--topbar-height)] transition-colors duration-300 ${
                    isScrolled ? 'shadow-md' : 'shadow-none'
                }`}
            >
                <div className="mx-auto h-full px-4 sm:px-6 lg:px-10 flex items-center justify-between">
                    {/* Left Section: Logo */}
                    <div className="flex-shrink-0">
                        <div
                            onClick={() => {
                                clearCart();
                                updateGuestProperty(undefined);
                                redirect('/guest');
                            }}
                            className="cursor-pointer"
                        >
                            <Logo size="lg" />
                        </div>
                    </div>
                </div>
            </header>
        </>
    );
};

export default OpenHeader;
