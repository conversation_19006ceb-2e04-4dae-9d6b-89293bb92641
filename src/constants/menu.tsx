import { BriefcaseIcon, BuildingOfficeIcon, HomeIcon, MapIcon, MapPinIcon, TagIcon } from '@heroicons/react/24/outline';
import React from 'react';

export type IMenuOption = {
    label: string;
    name: string;
    link: string;
    icon: React.ReactNode;
};

export const superAdminMenu: IMenuOption[] = [
    {
        label: 'Live Tracking',
        name: 'Live Tracking',
        link: '/admin/live-tracking',
        icon: <MapPinIcon className="h-5 w-5 mr-2" />,
    },
    {
        label: 'Merchant Management',
        name: 'Merchant Management',
        link: '/admin/merchant-management',
        icon: <BuildingOfficeIcon className="h-5 w-5 mr-2" />,
    },
    {
        label: 'Domain Values',
        name: 'Domain Values',
        link: '/admin/domain-values',
        icon: <TagIcon className="h-5 w-5 mr-2" />,
    },
    {
        label: 'Locations',
        name: 'Locations',
        link: '/admin/locations',
        icon: <MapIcon className="h-5 w-5 mr-2" />,
    },
    {
        label: 'Policy Setup',
        name: 'Policy Setup',
        link: '/admin/policy',
        icon: <MapIcon className="h-5 w-5 mr-2" />,
    },
];

export const merchantMenu: IMenuOption[] = [
    // {
    //     label: "Reservations",
    //     name: "Reservations",
    //     link: "/merchant/reservations",
    //     icon: <MapIcon className="h-5 w-5 mr-2" />
    // },
    {
        label: 'Live Tracking',
        name: 'Live Tracking',
        link: '/merchant/live-tracking',
        icon: <MapIcon className="h-5 w-5 mr-2" />,
    },
    {
        label: 'Property Information',
        name: 'Property Information',
        link: '/merchant/property-information',
        icon: <MapIcon className="h-5 w-5 mr-2" />,
    },
    {
        label: 'Amenities',
        name: 'Amenities',
        link: '/merchant/amenities',
        icon: <MapIcon className="h-5 w-5 mr-2" />,
    },
    {
        label: 'Policies',
        name: 'Policies',
        link: '/merchant/property-policies',
        icon: <MapIcon className="h-5 w-5 mr-2" />,
    },
    // {
    //     label: "Financials",
    //     name: "Financials",
    //     link: "/merchant/financials",
    //     icon: <CurrencyDollarIcon className="h-5 w-5 mr-2" />
    // },
    // {
    //     label: "Room Management",
    //     name: "Room Management",
    //     link: "/merchant/room-management",
    //     icon: <BuildingOfficeIcon className="h-5 w-5 mr-2" />
    // },
    {
        label: 'Rooms',
        name: 'Rooms',
        link: '/merchant/rooms',
        icon: <HomeIcon className="h-5 w-5 mr-2" />,
    },
    {
        label: 'Package',
        name: 'Package',
        link: '/merchant/package',
        icon: <BriefcaseIcon className="h-5 w-5 mr-2" />,
    },
    {
        label: 'Rate Card',
        name: 'Rate Card',
        link: '/merchant/rate-card',
        icon: <BriefcaseIcon className="h-5 w-5 mr-2" />,
    },
    {
        label: 'Room Availability',
        name: 'Room Availability',
        link: '/merchant/room-availability',
        icon: <BriefcaseIcon className="h-5 w-5 mr-2" />,
    },
    {
        label: 'Domain Values',
        name: 'Domain Values',
        link: '/merchant/domain-values',
        icon: <TagIcon className="h-5 w-5 mr-2" />,
    },
    // {
    //     label: "Room Availability",
    //     name: "Room Availability",
    //     link: "/merchant/room-availability",
    //     icon: <CalendarIcon className="h-5 w-5 mr-2" />
    // },
    // {
    //     label: "Rate Card",
    //     name: "Rate Card",
    //     link: "/merchant/rate-card",
    //     icon: <TagIcon className="h-5 w-5 mr-2" />
    // },
    // {
    //     label: "Gallery",
    //     name: "Gallery",
    //     link: "/merchant/gallery",
    //     icon: <PhotoIcon className="h-5 w-5 mr-2" />
    // },
];
