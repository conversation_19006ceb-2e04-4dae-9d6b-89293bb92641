class StorageService {
    private storageMode: string;

    constructor(storageMode: string = 'sessionStorage') {
        this.storageMode = storageMode;
    }

    private getStorage(): Storage | null {
        if (typeof window === 'undefined') return null;
        return window[this.storageMode as keyof Window & ('localStorage' | 'sessionStorage')];
    }

    setItem(key: string, value: unknown): void {
        const storage = this.getStorage();
        if (!storage) return;

        if (typeof value === 'object') {
            value = JSON.stringify(value);
        }
        storage.setItem(key, value as string);
    }

    getItem<T>(key: string): T | null {
        const storage = this.getStorage();
        if (!storage) return null;

        const value = storage.getItem(key);
        if (value) {
            try {
                return JSON.parse(value);
            } catch {
                return value as T;
            }
        }
        return null;
    }

    removeItem(key: string): void {
        const storage = this.getStorage();
        if (!storage) return;
        storage.removeItem(key);
    }

    clear(): void {
        const storage = this.getStorage();
        if (!storage) return;
        storage.clear();
    }

    hasItem(key: string): boolean {
        const storage = this.getStorage();
        if (!storage) return false;
        return storage.getItem(key) !== null;
    }

    switchStorage(storageMode: string): void {
        this.storageMode = storageMode;
    }
}
const storageService = new StorageService();

export default storageService;
