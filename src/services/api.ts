import axios from 'axios';
import { BASE_URL } from '../constants/env.ts';
import storageService from './storage-service.ts';

interface CurrentUser {
    token: string;
    userId: string;
}

const api = axios.create({
    baseURL: BASE_URL,
    headers: {
        'Content-Type': 'application/json',
        skip_zrok_interstitial: 'true',
    },
});

// Request interceptor for authentication
api.interceptors.request.use(config => {
    const current = storageService.getItem('currentUser') as CurrentUser | null;
    if (current) {
        config.headers = config.headers || {};
        config.headers.Authorization = `Bearer ${current.token}`;
        config.headers['x-user-id'] = current.userId;
    }

    return config;
});

// Response interceptor for error handling
api.interceptors.response.use(
    response => response,
    async error => {
        // if (error.response?.status === 401) {
        //     storageService.removeItem('currentUser');
        //     window.location.href = '/';
        // }
        return Promise.reject(error);
    }
);

export default api;
