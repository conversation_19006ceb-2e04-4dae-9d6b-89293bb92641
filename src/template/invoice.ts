import type { GroupReservation } from '../interfaces/IBooking.ts';
import { formatDate } from '../utils/helpers/dateHelpers.ts';

const formatCurrencyAsString = ({ amount }: { amount: number }): string => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD', // Adjust currency code as needed
    }).format(amount);
};

export const invoiceTemplate = (bookingData: GroupReservation) => {
    const totalPrice = bookingData.reservations.reduce((sum, res) => sum + res.price, 0).toFixed(2);
    const totalTax = bookingData.reservations.reduce((sum, res) => sum + res.tax, 0).toFixed(2);
    const grandTotal = bookingData.reservations.reduce((sum, res) => sum + res.totalAmount, 0).toFixed(2);
    const taxes = bookingData.reservations
        .flatMap(reservation => reservation.taxes)
        .map(tax => tax?.name || 'Unknown Tax')
        .join(', ');

    const imageUrl = bookingData.propertyId.customFields.businesslogo as string[];

    const totalPriceWithCurrency = formatCurrencyAsString({ amount: Number(totalPrice) });
    const totalTaxWithCurrency = formatCurrencyAsString({ amount: Number(totalTax) });
    const grandTotalWithCurrency = formatCurrencyAsString({ amount: Number(grandTotal) });

    return `
    <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Invoice</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background: #fff;
      padding: 0;
      color: #000;
      margin: 0;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      border: 1px solid #000;
      padding: 20px;
      box-sizing: border-box;
    }

    .header {
      display: flex;
      justify-content: space-between;
      border-bottom: 2px solid #000;
      padding-bottom: 10px;
      margin-bottom: 20px;
    }

    .header h1, .header h2 {
      margin: 0;
    }
    .property-address{
    width: 200px;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-bottom: 20px;
      font-size: 14px;
    }

    .info-box label {
      font-weight: bold;
      display: block;
    }

    .info-box .empty {
      border: 1px solid #000;
      height: 20px;
      margin-top: 2px;
      background: #fff;
    }

    .section {
      border: 1px solid #000;
      padding: 15px;
      margin-bottom: 20px;
      font-size: 14px;
    }

    .section label {
      font-weight: bold;
      display: block;
      margin-bottom: 4px;
    }

    .grid-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 10px;
    }

    .barcode {
      border: 1px solid #000;
      height: 80px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }

    .payment {
      border: 1px solid #000;
      padding: 15px;
      font-size: 14px;
      margin-bottom: 10px;
    }

    .payment h3 {
      margin-top: 0;
      margin-bottom: 10px;
      font-size: 16px;
    }

    .payment-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
    }

    .footer-note {
      font-size: 12px;
      color: #555;
      border-top: 1px solid #000;
      padding: 10px;
      margin-top: 15px;
    }

    hr {
      border: none;
      border-top: 1px solid #000;
      margin: 10px 0;
    }

    @media print {
      @page {
        margin: 0;
        /*size: auto;*/
      }
      body {
        padding: 10px;
        margin: 0;
        color: #000 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }

      .container {
        border: 1px solid #000 !important;
        width: 100%;
        max-width: 800px;
        margin: 0;
        padding: 15px;
      }

      .header {
        border-bottom: 2px solid #000 !important;
      }

      .info-box .empty,
      .section,
      .payment,
      .barcode,
      .footer-note {
        border: 1px solid #000 !important;
      }

      hr {
        border-top: 1px solid #000 !important;
      }

      .no-print {
        display: none;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Header -->
    <div class="header">
      <div>
        <img src="${imageUrl[0]}" alt="Logo" style="max-width: 150px;" />
      </div>
      <div>
        <h2>${bookingData.propertyId.name}</h2>
        <p class="property-address">${bookingData.propertyId.address.address1}</p>
      </div>
    </div>

    <!-- Booking Info -->
    <div class="info-grid">
      <div class="info-box">
        <label>Booking id</label>
        <div>${bookingData.reservationCode}</div>
      </div>
<!--      <div class="info-box">-->
<!--        <label>TRN</label>-->
<!--        <div>AVCS1234</div>-->
<!--      </div>-->
<!--      <div class="info-box">-->
<!--        <label>Invoice number</label>-->
<!--        <div>AVCS1234</div>-->
<!--      </div>-->
      <div class="info-box">
        <label>Invoice Date</label>
        <div >${formatDate(new Date())}</div>
      </div>
      <div class="info-box">
        <label>Place of supply</label>
        <div>${bookingData.propertyId.address.city}, ${bookingData.propertyId.address.state}, ${bookingData.propertyId.address.country}</div>
      </div>
    </div>

    <div class="section">
      <div class="grid-row">
        <div>
          <label>Customer name</label>
          <div>${bookingData.bookerDetails.firstName} ${bookingData.bookerDetails.lastName}</div>
        </div>
<!--        <div>-->
<!--          <label>Customer TRN number</label>-->
<!--          <div class="empty"></div>-->
<!--        </div>-->
      </div>
    </div>

    <!-- Transaction Info -->
    <div class="info-grid">
<!--      <div class="info-box">-->
<!--        <label>Transaction category</label>-->
<!--        <div>B2B</div>-->
<!--      </div>-->
      <div class="info-box">
        <label>Service Description</label>
        <div>Reservation service for Stay booking</div>
      </div>
    </div>

    <!-- Booking Details -->
    <div class="section">
      <div class="grid-row">
<!--        <div>-->
<!--          <label>Service provider name</label>-->
<!--          <div class="empty"></div>-->
<!--        </div>-->
        <div>
          <label>Airport location</label>
        <div>${bookingData.propertyId.address.city}, ${bookingData.propertyId.address.state}, ${bookingData.propertyId.address.country}</div>
        </div>
        <div>
          <label>Check In</label>
          <div >${formatDate(new Date(bookingData.reservations[0].startDateTime))}</div>
        </div>
        <div>
          <label>Check Out</label>
          <div>${formatDate(new Date(bookingData.reservations[0].endDateTime))}</div>
        </div>
<!--        <div class="barcode">-->
<!--          Bar code-->
<!--        </div>-->
      </div>
    </div>

    <!-- Passenger Info -->
    <div class="section">
      <label>Passenger name(s)</label>
      ${bookingData.reservations.map(reservation => reservation.guestDetails.map(guest => `<div>${guest.firstName} ${guest.lastName}</div>`))}
    </div>

    <!-- Payment Breakup -->
    <div class="payment">
      <h3>Payment breakup</h3>
      <div class="payment-row">
        <div><strong>Total Price</strong></div>
        <div>${totalPriceWithCurrency}</div>
      </div>
      <div class="payment-row">
        <div>${taxes}</div>
        <<div>${totalTaxWithCurrency}</div>
      </div>
      <hr />
      <div class="payment-row">
        <strong>Grand total</strong>
        <div>${grandTotalWithCurrency}</div>
      </div>
    </div>

    <!-- Footer Note -->
    <div class="footer-note">
      Input tax credit of Tax charged by the original service provider is available only against the invoice issued by the respective service provider. StayTransit acts only as a facilitator for these services. This is not a valid travel document.
    </div>
  </div>
</body>
</html>
`;
};
