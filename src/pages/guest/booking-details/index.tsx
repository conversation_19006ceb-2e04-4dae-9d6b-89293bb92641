import React, { useEffect, useState } from 'react';
import type { GroupReservation, Reservation } from '../../../interfaces/IBooking.ts';
import SectionTitle from '../../../components/common/SectionTitle.tsx';
import CurrencyFormat from '../../../components/common/CurrencyFormat.tsx';
import { useParams } from 'react-router';
import { useApiHook } from '../../../hooks/useApi.ts';
import { useToastHook } from '../../../hooks/useToaster.ts';
import useBoundStore from '../../../store/useBoundStore.ts';
import UiLoader from '../../../lib/UiLoader.tsx';
import CancelPopup from '../../../components/common/CancelPopup.tsx';

const GuestDetails: React.FC<{ reservation: Reservation; index: number }> = ({ reservation, index }) => {
    return (
        <div className="flex flex-col space-y-4 border-b pb-4 last:border-b-0">
            <span className="text-sm font-medium text-gray-700">Guest Details (Reservation {index + 1})</span>
            {reservation.guestDetails && reservation.guestDetails.length > 0 ? (
                reservation.guestDetails.map((guest, guestIndex) => (
                    <div key={guest._id} className="border-l-2 border-blue-500 pl-4 space-y-2 flex  justify-between">
                        <p>
                            <span className="text-gray-700 text-sm">Guest {guestIndex + 1} Name: </span>
                            <span className="font-semibold text-gray-900">
                                {guest.firstName} {guest.lastName}
                            </span>
                        </p>
                        <p>
                            <span className="text-gray-700 text-sm">Email: </span>
                            <span className="font-semibold text-gray-900">{guest.email}</span>
                        </p>
                    </div>
                ))
            ) : (
                <p className="text-gray-600 text-sm">No guest details available</p>
            )}
        </div>
    );
};

// Sub-component for Payment Details
const PaymentDetails: React.FC<{ booking: GroupReservation }> = ({ booking }) => {
    return (
        <div className="p-6 bg-gray-50 border-t">
            <SectionTitle title="Payment Details" />
            {booking.reservations.map((reservation, index) => (
                <div key={reservation._id} className="flex flex-col space-y-4 mb-6 border-b pb-4 last:border-b-0">
                    <div className="flex justify-between">
                        <span className="text-gray-700 text-sm">Amount (Reservation {index + 1})</span>
                        <span className="font-semibold text-gray-900">
                            {/*{booking.propertyId.customFields.currency} {reservation.price}*/}
                            <CurrencyFormat amount={reservation.price} />
                        </span>
                    </div>
                    <div className="flex justify-between">
                        <span className="text-gray-700 text-sm">Tax</span>
                        <span className="font-semibold text-gray-900">
                            {/*{booking.propertyId.customFields.currency} {reservation.tax}*/}
                            <CurrencyFormat amount={reservation.tax} />
                        </span>
                    </div>
                    <div className="flex justify-between border-t pt-4">
                        <span className="text-gray-700 font-semibold">Total Amount</span>
                        <span className="font-bold text-lg text-gray-900">
                            {/*{booking.propertyId.customFields.currency} {reservation.totalAmount}*/}
                            <CurrencyFormat amount={reservation.totalAmount} />
                        </span>
                    </div>
                </div>
            ))}
            <div className="flex justify-between">
                <span className="text-gray-700 text-sm">Payment Status</span>
                <span
                    className={`font-semibold ${
                        booking.paymentStatus === 'pending' ? 'text-yellow-600' : 'text-green-600'
                    }`}
                >
                    {booking.paymentStatus.charAt(0).toUpperCase() + booking.paymentStatus.slice(1)}
                </span>
            </div>
        </div>
    );
};

// Main Booking Details Component
const BookingDetailsPage = () => {
    const params = useParams();
    const { get } = useApiHook();
    const { showError } = useToastHook();
    const { loggedUser } = useBoundStore();
    const [booking, setBooking] = useState<GroupReservation | null>(null);
    const [isCancelling, setIsCancelling] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const bookingId = params.id as string;

    const fetchBooking = async () => {
        try {
            setIsLoading(true);
            const response: GroupReservation[] = await get('users/reservations', {
                headers: { Authorization: `Bearer ${loggedUser.token}` },
            });
            const foundBooking = response.find(b => b._id === bookingId);
            if (foundBooking) {
                setBooking(foundBooking);
            } else {
                showError('Booking not found');
            }
        } catch (_e) {
            showError('Failed to fetch bookings. Please try again later');
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchBooking();
    }, []);

    if (isLoading) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-gray-100">
                <UiLoader label="Loading..." />
            </div>
        );
    }

    if (!booking) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-gray-100 text-gray-700 text-lg">
                No booking data available
            </div>
        );
    }

    const isPastCheckoutTime = new Date() > new Date(booking.reservations[0].endDateTime);

    return (
        <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 mt-20 mb-5">
            <CancelPopup isOpen={isCancelling} booking={booking} onClose={() => setIsCancelling(false)} />
            <div className="w-full max-w-7xl bg-white shadow-xl rounded-2xl overflow-hidden">
                {/* Header Section */}
                <div className="theme-background p-6 text-white">
                    <SectionTitle title="Booking Details" />
                    <div className="mt-4 space-y-2">
                        <p className="text-xl font-semibold">{booking.propertyId.name}</p>
                        <p className="text-sm opacity-90">
                            {booking.propertyId.address.address1}, {booking.propertyId.address.address2},{' '}
                            {booking.propertyId.address.city}, {booking.propertyId.address.state},{' '}
                            {booking.propertyId.address.country} {booking.propertyId.address.zipcode}
                        </p>
                    </div>
                </div>

                {/* Guest Details */}
                <div className="p-6 bg-gray-50 border-t">
                    <SectionTitle title="Traveller Information" />
                    {booking.reservations.map((reservation, index) => (
                        <GuestDetails key={reservation._id} reservation={reservation} index={index} />
                    ))}
                </div>

                {/* Payment Details */}
                <PaymentDetails booking={booking} />

                {/* Business Details */}
                {/*<BusinessDetails booking={booking} />*/}

                {/* Actions */}
                {booking.paymentStatus.toLowerCase() !== 'refunded' && (
                    <div className="p-6 bg-gray-50 border-t flex flex-col items-center space-y-4">
                        <p className="text-sm text-gray-700">
                            Something not right?{' '}
                            <a
                                href="#"
                                className="theme-text hover:underline font-semibold"
                                aria-label="Chat with support"
                            >
                                Chat with us for help
                            </a>
                        </p>
                        {!isPastCheckoutTime && (
                            <div className="flex flex-col lg:flex-row justify-between gap-2">
                                <button
                                    className="theme-border text-red-500 px-8 py-3 rounded-lg "
                                    aria-label="Cancel Booking"
                                    onClick={() => setIsCancelling(true)}
                                >
                                    Edit Booking
                                </button>
                                <button
                                    className="theme-background text-white px-8 py-3 rounded-lg "
                                    aria-label="Cancel Booking"
                                    onClick={() => setIsCancelling(true)}
                                >
                                    Cancel Booking
                                </button>
                            </div>
                        )}
                        <p className="text-sm text-gray-700">
                            <a
                                href="#"
                                className="theme-text hover:underline font-semibold"
                                aria-label="Read terms and conditions"
                            >
                                Read Terms and Conditions
                            </a>
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default BookingDetailsPage;
