import { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router';
import type { ICompany } from '../../interfaces/ICompany.ts';
import { useApiHook } from '../../hooks/useApi.ts';
import useBoundStore from '../../store/useBoundStore.ts';
import { useToastHook } from '../../hooks/useToaster.ts';
import { formatApiDate } from '../../utils/helpers/dateHelpers.ts';
import UiLoader from '../../lib/UiLoader.tsx';
import HomePageBanner from '../../components/common/HomePageBanner.tsx';
import { Transition } from '@headlessui/react';
import ServiceFilter from '../../components/guest/search-filters/ServiceFilter.tsx';
import SearchForm, { type ISearchOptions } from '../../components/guest/search-filters/SearchForm.tsx';
import RoomAvailability from './RoomAvailability.tsx';
import { ArrowRightStartOnRectangleIcon } from '@heroicons/react/16/solid';
import Checkout from '../../components/common/Checkout.tsx';
import { stayBenefitsGuest } from '../../utils/constants/Features.ts';
import StayTransitBenefits from '../../components/common/StayTransitBenefits.tsx';
import SectionTitle from '../../components/common/SectionTitle.tsx';
import Testimonials from '../../components/common/Testimonials.tsx';

const GuestPage = () => {
    const navigate = useNavigate();
    const [properties, setProperties] = useState<ICompany[]>([]);
    const [isFormSubmitted, setIsFormSubmitted] = useState(false);
    const [isPageLoaded, setIsPageLoaded] = useState(false);
    const hasFetchedLocationDetails = useRef(false);
    const { get, loading } = useApiHook();
    const {
        search,
        updateSearch,
        cart,
        clearCart,
        guestSelectedProperty,
        updateGuestProperty,
        clearSearch,
        guestLocationDetails,
        updateGuestLocationDetails,
    } = useBoundStore();
    const { showError } = useToastHook();

    useEffect(() => {
        setIsPageLoaded(true);
        clearSearch();
        userCurrentLocation();
    }, []);

    const userCurrentLocation = () => {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                position => {
                    updateGuestLocationDetails({
                        countryPhoneCode: null,
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude,
                    });
                },
                _err => {
                    // showError(err.message);
                }
            );
        }
    };

    const handleSearchSubmit = (values: ISearchOptions) => {
        setIsFormSubmitted(true);
        updateSearch(values);
    };

    useEffect(() => {
        fetchProperties();
        clearCart();
    }, [search]);

    useEffect(() => {
        if (guestLocationDetails.latitude && guestLocationDetails.longitude && !hasFetchedLocationDetails.current) {
            hasFetchedLocationDetails.current = true;
            getLocationDetailsByCoordinates();
        }
    }, [guestLocationDetails.latitude]);

    const getLocationDetailsByCoordinates = async () => {
        const response = await get<{
            countryPhoneCode: string | null;
        }>(
            `/locations/google/maps?latitude=${guestLocationDetails.latitude}&longitude=${guestLocationDetails.longitude}`
        );
        if (response) {
            updateGuestLocationDetails({
                countryPhoneCode: response.countryPhoneCode || null,
                latitude: guestLocationDetails.latitude,
                longitude: guestLocationDetails.longitude,
            });
        }
    };

    const fetchProperties = async () => {
        const airport = search?.airport?.[0]?.value;
        if (!airport || !isFormSubmitted) return;

        try {
            const response = await get<ICompany[]>(
                `/properties?includesPackages=true&locationId=${airport}&startDateTime=${formatApiDate(search.checkInDate) + ' ' + search.checkInTime}&endDateTime=${formatApiDate(search.checkOutDate) + ' ' + search.checkOutTime}&noOfAdults=${search.noOfAdults}&noOfChildren=${search.noOfChildren}&active=true`
            );

            if (response?.length) {
                // Filter the response based on area selection
                const filteredProperties = response.filter(property => {
                    return (
                        (!search.area.length || search.area.includes(property.premisesType)) &&
                        property.active &&
                        property?.packages.length > 0
                    );
                });
                setProperties(filteredProperties);

                // Always select the first property if none is selected
                if (!guestSelectedProperty && filteredProperties.length > 0) {
                    updateGuestProperty(filteredProperties[0]);
                } else if (guestSelectedProperty) {
                    // If there's a previously selected property, try to find it in the new results
                    const stillExists = filteredProperties.some(p => p._id === guestSelectedProperty._id);
                    if (!stillExists && filteredProperties.length > 0) {
                        updateGuestProperty(filteredProperties[0]);
                    }
                }
                navigate('/guest#availability');
            } else {
                setProperties([]);
                updateGuestProperty(undefined);
            }
        } catch (err) {
            console.error('Error fetching properties:', err);
            showError('Failed to load properties.');
            setProperties([]);
            updateGuestProperty(undefined);
        }
    };

    return (
        <div className="min-h-screen  mt-[20] lg:mt-0 scroll-smooth">
            {/* Hero Section */}
            {loading && <UiLoader label="Loading..." />}
            <div className="relative  text-gray-900">
                <HomePageBanner />
                <Transition
                    show={isPageLoaded}
                    enter="transition ease-out duration-300"
                    enterFrom="opacity-0 translate-y-4"
                    enterTo="opacity-100 translate-y-0"
                    leave="transition ease-in duration-200"
                    leaveFrom="opacity-100 translate-y-0"
                    leaveTo="opacity-0 translate-y-4"
                >
                    <div className={'px-10 mt-5'}>
                        <div className="w-full">
                            <ServiceFilter />
                        </div>
                    </div>
                </Transition>
            </div>

            <div className="px-5 xl:px-10">
                <SearchForm initialValues={search ? search : null} onSubmit={handleSearchSubmit} />
            </div>

            <Transition
                show={Boolean(isFormSubmitted && properties.length > 0)}
                enter="transition ease-out duration-500"
                enterFrom="opacity-0 translate-y-8"
                enterTo="opacity-100 translate-y-0"
                leave="transition ease-in duration-300"
                leaveFrom="opacity-100 translate-y-0"
                leaveTo="opacity-0 translate-y-8"
            >
                <div>
                    {isFormSubmitted && properties.length > 0 && (
                        <div className="py-10" id="availability">
                            <RoomAvailability properties={properties} />
                        </div>
                    )}
                </div>
            </Transition>
            {!loading && isFormSubmitted && properties.length === 0 && (
                <div className="pt-10 pb-5">
                    <div className="container-width mx-auto">
                        <p className="text-center text-2xl font-bold">No properties found</p>
                    </div>
                </div>
            )}
            {cart.length > 0 && (
                <>
                    <a
                        href="#guest-details"
                        className="p-3 theme-background z-5 text-white rounded-full hover:bg-blue-700 fixed bottom-4 right-4 shadow-lg transition-colors duration-300 flex items-center justify-center"
                        aria-label="Show Checkout"
                    >
                        <span className="px-3 flex items-center gap-2">
                            Guest Details <ArrowRightStartOnRectangleIcon className="h-5 w-5 mt-0.5" />
                        </span>
                    </a>
                    <div id="guest-details" className="px-4 xl:px-12 py-8">
                        <Checkout />
                    </div>
                </>
            )}

            {/* {cart.length > 0 &&
        <CheckoutDrawer cart={cart} searchOptions={search} />
      } */}

            {/* Why StayTransit Section */}
            <div className={`px-5 xl:px-12 ${isFormSubmitted && properties.length > 0 ? 'mt-0' : 'mt-10'}`}>
                <StayTransitBenefits benefits={stayBenefitsGuest} />
            </div>

            {/* Testimonials */}
            <div className="px-5 xl:px-12 py-8 ">
                <div className="container-width mx-auto">
                    <div className="w-full container-width mx-auto text-start my-12 relative">
                        {/* Background quote symbol */}
                        <span className="absolute -top-88 left-20 rotate-180 text-[500px] text-indigo-300 opacity-20 leading-none pointer-events-none select-none z-0">
                            “
                        </span>
                        {/* Section title content */}
                        <div className="relative z-10">
                            <SectionTitle
                                title="What Our Guests Say?"
                                subtitle="Discover why travelers choose us for their perfect stay experience"
                            />
                        </div>
                    </div>

                    <Testimonials />
                </div>
            </div>
        </div>
    );
};

export default GuestPage;
