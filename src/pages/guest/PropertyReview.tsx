import { useEffect, useState } from 'react';
import { useLoadScript } from '@react-google-maps/api';
import { GOOGLE_MAPS_API_KEY } from '../../constants/env.ts';
import { StarIcon as SolidStar } from '@heroicons/react/20/solid';
import { StarIcon as OutlineStar } from '@heroicons/react/24/outline';

interface PlaceReview {
    author_name: string;
    author_url?: string;
    language?: string;
    rating: number;
    relative_time_description: string;
    text: string;
    time: number;
    profile_photo_url?: string;
}

function PropertyReviews({ placeId }: { placeId: string }) {
    const [reviews, setReviews] = useState<PlaceReview[]>([]);
    const [error, setError] = useState<string | null>(null);

    const { isLoaded } = useLoadScript({
        googleMapsApiKey: GOOGLE_MAPS_API_KEY || '',
        libraries: ['places'],
    });

    useEffect(() => {
        if (!isLoaded || !placeId) return;

        const service = new window.google.maps.places.PlacesService(document.createElement('div'));

        const detailsRequest: google.maps.places.PlaceDetailsRequest = {
            placeId,
            fields: ['reviews'],
        };

        service.getDetails(detailsRequest, (placeDetails, status) => {
            if (status === window.google.maps.places.PlacesServiceStatus.OK && placeDetails?.reviews) {
                setReviews(placeDetails.reviews as PlaceReview[]);
            } else {
                const errorMessage = `Failed to get place details: ${status}`;
                console.error(errorMessage);
                setError(errorMessage);
            }
        });
    }, [isLoaded, placeId]);

    if (!isLoaded) return <div className="text-center py-8 text-gray-500">Loading Google Maps...</div>;
    if (!placeId) return <div className="text-center py-8 text-gray-500">No reviews available for this location.</div>;
    if (error) return <div className="text-center py-8 text-red-500">No reviews available for this location.</div>;

    return (
        <div className=" mx-auto h-80 overflow-auto ">
            {!placeId && reviews.length === 0 ? (
                <p className=" text-center text-red-500">No reviews available for this location.</p>
            ) : (
                <div className="space-y-6">
                    {reviews.map((review, index) => (
                        <div
                            key={`${review.author_name}-${index}`}
                            className="bg-white border rounded-lg p-2 shadow-md transition hover:shadow-lg duration-200 ease-in-out"
                        >
                            <div className="flex items-start gap-4 mb-3">
                                {/*<img*/}
                                {/*    src={review.profile_photo_url || 'https://via.placeholder.com/48'}*/}
                                {/*    alt={review.author_name}*/}
                                {/*    className="w-12 h-12 rounded-full object-cover"*/}
                                {/*/>*/}
                                <div className="flex-1">
                                    <h3 className="font-semibold text-lg text-gray-800">{review.author_name}</h3>
                                    <div className="flex items-center gap-1 mt-1">
                                        {[...Array(5)].map((_, i) =>
                                            i < review.rating ? (
                                                <SolidStar key={i} className="w-5 h-5 text-yellow-400" />
                                            ) : (
                                                <OutlineStar key={i} className="w-5 h-5 text-yellow-300" />
                                            )
                                        )}
                                        <span className="text-sm text-gray-500 ml-2">
                                            {review.relative_time_description}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <p className="text-gray-700 text-base leading-relaxed">{review.text}</p>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
}

export default PropertyReviews;
