import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router';
import useBoundStore from '../../../store/useBoundStore.ts';
import type { GroupReservation } from '../../../interfaces/IBooking.ts';
import { useApiHook } from '../../../hooks/useApi.ts';
import UiLoader from '../../../lib/UiLoader.tsx';
import { formatDateTime } from '../../../utils/helpers/dateHelpers.ts';
import currencyFormat from '../../../components/common/CurrencyFormat.tsx';

function SuccessPayment() {
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();
    const paymentId = searchParams.get('paymentId');
    const [_error, setError] = useState<string | null>(null);
    const { clearCart, updateGuestProperty, clearSearch } = useBoundStore();
    const [bookingInfo, setBookingInfo] = useState<GroupReservation | null>(null);
    const { patch } = useApiHook();

    useEffect(() => {
        const updatePaymentStatus = async () => {
            try {
                const response = await patch<{
                    payment: unknown;
                    reservation: GroupReservation;
                }>(`/payments/${paymentId}`);
                if (response && response.reservation) {
                    setBookingInfo(response.reservation);
                } else {
                    setError('Failed to get booking information');
                }
            } catch (_err) {
                setError('Failed to process payment');
            }
        };

        if (paymentId) {
            updatePaymentStatus();
        } else {
            setError('Payment not found');
        }
    }, [paymentId]);

    const handleGoHome = () => {
        clearCart();
        updateGuestProperty(undefined);
        clearSearch();
        navigate('/guest', { replace: true });
    };

    if (!bookingInfo) {
        return <UiLoader label="Finalizing your booking..." />;
    }
    const { reservationCode, bookerDetails, reservations, status, propertyId, paymentStatus, createdAt } = bookingInfo;

    const totalAmount = reservations.reduce((sum, res) => sum + (res.totalAmount || 0), 0);
    const totalTax = reservations.reduce((sum, res) => sum + (res.tax || 0), 0);
    const firstReservation = reservations[0];
    const lastReservation = reservations[reservations.length - 1];
    const guestNames =
        firstReservation?.guestDetails?.map(guest => `${guest.firstName} ${guest.lastName}`).join(', ') || 'N/A';

    const packageCounts = Array.from(
        reservations
            .reduce((map, res) => {
                const id = res.packageId._id;
                map.set(id, {
                    count: (map.get(id)?.count || 0) + 1,
                    ...res,
                });
                return map;
            }, new Map())
            .values()
    );

    return (
        <div className="w-full max-w-4xl mx-auto mt-10 p-8 bg-white rounded-xl shadow-md border border-gray-200">
            <div className="text-center mb-8">
                <h2 className="text-xl font-semibold text-gray-700">Great Your Booking Is Confirmed!</h2>
                <p className="text-sm text-gray-500 mt-1">
                    You will soon receive a mail confirmation on{' '}
                    <span className="font-semibold">{bookerDetails.email}</span>
                </p>
                <p className="mt-3 text-sm text-gray-600">
                    <span className="font-semibold">ID -</span> {reservationCode}
                </p>
                <p className="text-sm text-gray-600">
                    Booked on: <span className="font-semibold">{formatDateTime(new Date(createdAt))}</span>
                </p>
            </div>

            <div className="">
                {/* Property Details */}
                <div>
                    <h3 className="theme-text font-semibold mb-2">Property Details</h3>
                    <p className={'font-medium'}>
                        <span className="text-gray-400">Name:</span> {propertyId.name}
                    </p>
                    <p className={'font-medium'}>
                        <span className="text-gray-400">Address:</span> {propertyId.address?.address1}
                    </p>
                    <p className={'font-medium capitalize'}>
                        <span className="text-gray-400">Status:</span> {status}
                    </p>
                    <p className={'font-medium capitalize'}>
                        <span className="text-gray-400">Payment:</span> {paymentStatus}
                    </p>
                </div>

                {/* Guest & Payment */}
            </div>

            {/* Payment Summary */}
            <div className="mt-6 text-sm  border-t border-dashed pt-4 flex flex-col md:flex-row justify-between">
                <div>
                    <h3 className="text-red-600 font-semibold mb-2">Guest Details</h3>
                    <p className="font-medium text-lg">{guestNames}</p>
                    <p className={'py-2 text-gray-600'}>
                        <span className="text-black font-semibold">Check in - </span>{' '}
                        {firstReservation ? formatDateTime(new Date(firstReservation.startDateTime)) : 'N/A'}
                    </p>
                    <p className={'py-2 text-gray-600'}>
                        <span className="text-black font-semibold">Check out - </span>{' '}
                        {lastReservation ? formatDateTime(new Date(lastReservation.endDateTime)) : 'N/A'}
                    </p>
                    <p className="text-black font-semibold">Room Details - </p>
                    {packageCounts.map(reservation => (
                        <div className="mt-2 text-gray-600">
                            {reservation.packageId.name}- {reservation.count} room(s)
                            <p>{`${reservation.noOfAdults} adult(s), ${reservation.noOfChildren} child(ren)`}</p>
                        </div>
                    ))}
                </div>

                <div className="mb-4 md:mb-0 w-full flex flex-col gap-2 items-center md:items-start md:w-1/3">
                    <h3 className="text-red-600 font-semibold mb-2">Payment Summary</h3>
                    <div className={'w-full flex justify-between items-center'}>
                        <p className={'text-md text-gray-600'}>Base Fee</p>
                        <p className={'text-md'}>{currencyFormat({ amount: totalAmount - totalTax })}</p>
                    </div>
                    <div className={'w-full flex justify-between items-center'}>
                        <p className={'text-md text-gray-600'}>Service Fee & Taxes:</p>
                        <p className={'text-md'}> {currencyFormat({ amount: totalTax })}</p>
                    </div>
                    <div className={'w-full flex justify-between items-center'}>
                        <p className={'text-md font-semibold'}>Grand Total:</p>
                        <p className={'text-md font-semibold'}> {currencyFormat({ amount: totalAmount })}</p>
                    </div>
                </div>
            </div>

            {/* Cancellation */}
            <div className="mt-6 text-sm text-gray-700">
                <p className="font-semibold py-2 text-md">Cancellation Policy:</p>
                <p className="text-sm text-gray-500 py-2">Allowed 24hrs before check-in.</p>
                <a href="#" className="text-red-500 text-xs underline  font-semibold">
                    Read StayTransit Terms & Conditions
                </a>
            </div>

            {/* Buttons */}
            <div className="text-center mt-4">
                <button
                    onClick={() => window.print()}
                    className="bg-red-600 text-white px-6 py-2 rounded-full font-medium hover:bg-red-700 transition mr-4"
                >
                    Print Now
                </button>
                <button
                    onClick={handleGoHome}
                    className="bg-gray-600 text-white px-6 py-2 rounded-full font-medium hover:bg-gray-700 transition"
                >
                    Go Home
                </button>
            </div>
        </div>
    );
}

export default SuccessPayment;
