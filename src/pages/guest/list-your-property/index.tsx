import LoginForm from '../../../components/guest/auth/LoginForm.tsx';
import StayTransitBenefits from '../../../components/common/StayTransitBenefits.tsx';
import { stayBenefits } from '../../../utils/constants/Features.ts';
import banner from '../../../assets/bap.png';
import Partner from '../../../components/guest/list-property/Partner.tsx';
import FAQ from '../../../components/guest/list-property/FAQ.tsx';
import SuccessStory from '../../../components/guest/list-property/SuccessStory.tsx';

function ListYourProperty() {
    return (
        <div className="mt-[var(--topbar-height)] bg-white px-3">
            <div className="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                    <div className="flex flex-col items-center justify-center p-6 sm:p-8 lg:p-12 bg-white rounded-2xl ">
                        <div className="w-full max-w-md">
                            <h2 className="text-2xl font-semibold text-gray-900 text-center">Welcome Back</h2>
                            <p className="text-lg  text-gray-600 mb-6 text-center">Sign in to manage your property</p>
                            <LoginForm redirect="onboarding" isChild={true} />
                        </div>
                    </div>

                    {/* Right Section - Banner & CTA */}
                    <div className="flex flex-col items-center justify-center p-6 sm:p-8 lg:p-12 space-y-6">
                        <img
                            className="w-full max-w-2xl rounded-2xl object-cover"
                            src={banner}
                            alt="Traveler with backpack exploring a city"
                        />
                        {/*<button*/}
                        {/*    className="bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-8 rounded-full transition-colors duration-200 shadow-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"*/}
                        {/*    aria-label="Partner with StayTransit"*/}
                        {/*>*/}
                        {/*    Partner with StayTransit*/}
                        {/*</button>*/}
                    </div>
                </div>
            </div>
            <div className={' p-3'}>
                <StayTransitBenefits benefits={stayBenefits} />
            </div>
            <div className={' p-3'}>
                <Partner />
            </div>
            <div className={'p-3'}>
                <FAQ />
            </div>
            <div className={'py-3 px-2 md:px-10'}>
                <SuccessStory />
            </div>
        </div>
    );
}

export default ListYourProperty;
