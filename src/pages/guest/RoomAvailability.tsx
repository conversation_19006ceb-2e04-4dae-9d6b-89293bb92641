import type { ICompany } from '../../interfaces/ICompany.ts';
import useBoundStore from '../../store/useBoundStore.ts';
import { useEffect, useState } from 'react';
import type { IRoomType } from '../../interfaces/IRoomType.ts';
import { useToastHook } from '../../hooks/useToaster.ts';
import { useApiHook } from '../../hooks/useApi.ts';
import { formatApiDate } from '../../utils/helpers/dateHelpers.ts';
import UiLoader from '../../lib/UiLoader.tsx';
import MinimalPropertyCard from '../../components/guest/card/MinimalPropertyCard.tsx';
import Tabs from '../../components/common/Tabs.tsx';
import RoomCard from '../../components/guest/card/RoomCard.tsx';
import MobileViewRoomCard from '../../components/common/MobileViewRoomCard.tsx';
import PackageCard from '../../components/guest/card/PackageCard.tsx';
import PropertyPolicies from '../../components/common/policies/PropertyPolicies.tsx';
import { InformationCircleIcon } from '@heroicons/react/24/outline';
import { Tooltip } from 'react-tooltip';
import PropertyReviews from './PropertyReview.tsx';

function RoomAvailability({ properties }: { properties: ICompany[] }) {
    const { guestSelectedProperty, updateGuestProperty, addToCart, cart, removeItem, clearCart, search } =
        useBoundStore();
    const [roomTypes, setRoomTypes] = useState<IRoomType[]>([]);
    const { showError } = useToastHook();
    const [activeTab, setActiveTab] = useState<'rooms' | 'policies' | 'reviews'>('rooms');
    const { get, loading } = useApiHook();

    const tabs = [
        { id: 'rooms', label: 'Rooms' },
        { id: 'policies', label: 'Policies' },
        { id: 'reviews', label: 'Reviews' },
    ];

    // const handlePropertySelect = (propertyId: string) => {
    //     const property = properties.find(p => p._id === propertyId);
    //     updateGuestProperty(property);
    //     setActiveTab('rooms');
    // };

    const fetchRoomTypes = async () => {
        if (!guestSelectedProperty?._id || !search.checkInDate || !search.checkOutDate) return;
        try {
            const response = await get<IRoomType[]>(
                `reservations/${guestSelectedProperty._id}/available?noOfAdults=${search.noOfAdults}&noOfChildren=${search.noOfChildren}&startDateTime=${formatApiDate(search.checkInDate) + ' ' + search.checkInTime}&endDateTime=${formatApiDate(search.checkOutDate) + ' ' + search.checkOutTime}`
            );
            setRoomTypes((response || []).filter(r => r.packages?.length));
        } catch (_err) {
            showError('Failed to load rooms.');
        }
    };

    // const renderHotelAccess = () => {
    //     const rawAccess = guestSelectedProperty?.customFields?.hotelAccess;
    //     const accessList = Array.isArray(rawAccess)
    //         ? rawAccess
    //         : typeof rawAccess === 'string'
    //           ? rawAccess
    //                 .split(',')
    //                 .map(s => s.trim())
    //                 .filter(Boolean)
    //           : [];

    //     if (!accessList.length) return null;

    //     return (
    //         <div className="w-full mb-4">
    //             <div className=" border border-gray-200 rounded-md px-4 py-2">
    //                 <div className="flex flex-wrap gap-3">
    //                     {accessList.map((access: string) => (
    //                         <span key={access} className="text-sm bg-gray-100 px-2 py-1 rounded">
    //                             {access}
    //                         </span>
    //                     ))}
    //                 </div>
    //             </div>
    //         </div>
    //     );
    // };

    useEffect(() => {
        if (guestSelectedProperty) {
            fetchRoomTypes();
            clearCart();
        }
    }, [guestSelectedProperty, search]);

    const premisesDescription =
        guestSelectedProperty.premisesType === 'Airside'
            ? 'Airside is the area inside the airport'
            : 'Landside is the area outside the airport';

    return (
        <>
            {guestSelectedProperty && (
                <div className="container-width mx-auto py-8 mt-0 lg:mt-[60px]">
                    {loading && <UiLoader label="Loading rooms..." />}
                    <div className="flex flex-col lg:flex-row gap-8">
                        {/* Sidebar with properties */}
                        <div className="w-full lg:w-1/4 overflow-x-auto lg:overflow-visible">
                            <div className="flex lg:flex-col gap-4 min-w-max lg:min-w-0 p-2">
                                {properties.map(property => (
                                    <div key={property._id} className="w-[280px] lg:w-full flex-shrink-0">
                                        <MinimalPropertyCard
                                            highlight={property._id === guestSelectedProperty._id}
                                            property={property}
                                            onSelect={() => updateGuestProperty(property)}
                                        />
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Room Cards */}
                        <div className="w-full lg:w-3/4">
                            <div className="flex items-center gap-4 mb-2 p-2 pl-6 rounded-t-lg border border-gray-200 bg-white ">
                                <h1 className="text-2xl font-bold text-gray-900">{guestSelectedProperty.name}</h1>
                                {/*<StatusBadge type={guestSelectedProperty.premisesType} />*/}
                                <p className={'text-sm font-semibold text-gray-400'}>
                                    {guestSelectedProperty.premisesType}
                                </p>
                                <InformationCircleIcon
                                    className="h-4 w-4 text-gray-400 cursor-help"
                                    data-tooltip-id={`${guestSelectedProperty.premisesType.toLowerCase()}-tooltip`}
                                    data-tooltip-content={premisesDescription}
                                />
                                <Tooltip
                                    id="airside-tooltip"
                                    className="max-w-xs bg-gray-800 text-gray-400 text-sm rounded-lg shadow-lg border border-gray-600 z-50"
                                    place="top"
                                    delayShow={300}
                                    delayHide={100}
                                />
                                <Tooltip
                                    id="landside-tooltip"
                                    className="max-w-xs bg-gray-800 text-gray-400 text-sm rounded-lg shadow-lg border border-gray-600 z-50"
                                    place="top"
                                    delayShow={300}
                                    delayHide={100}
                                />
                            </div>
                            {/* Tabs */}
                            <Tabs
                                tabs={tabs}
                                activeTab={activeTab}
                                onChange={tabId => setActiveTab(tabId as 'rooms' | 'policies' | 'reviews')}
                                className="mb-3 border-0"
                            />

                            {/* {guestSelectedProperty.customFields?.hotelAccess && (
                                <div className="mb-6">
                                    <div className="bg-blue-50 border rounded-lg p-4">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                            {guestSelectedProperty.customFields.hotelAccess.map((access: string) => (
                                                <div key={access} className="flex items-center gap-2 text-blue-800">
                                                    <div className="w-2 h-2 theme-background rounded-full flex-shrink-0"></div>
                                                    <span className="text-sm font-medium theme-text">{access}</span>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            )} */}
                            {/* {activeTab === 'rooms' && renderHotelAccess()} */}

                            {activeTab === 'rooms' &&
                                (roomTypes.length ? (
                                    roomTypes.map(room => (
                                        <div
                                            key={room._id}
                                            className="flex flex-col lg:flex-row gap-6 items-start p-4 border border-gray-200 rounded-lg bg-white mb-4 "
                                            style={{ minHeight: '400px' }}
                                        >
                                            {/* Desktop RoomCard */}
                                            <div className="max-w-full w-[375px] flex-shrink-0 sticky top-[55px] hidden lg:block">
                                                <RoomCard
                                                    roomName={room.name}
                                                    size={room.area}
                                                    bedType={room.bedType}
                                                    amenities={room.amenities}
                                                    images={room.imageUrls}
                                                />
                                            </div>

                                            {/* Mobile RoomCard */}
                                            <div className="w-full lg:hidden block sticky top-[55px]">
                                                <MobileViewRoomCard
                                                    roomName={room.name}
                                                    size={room.area}
                                                    bedType={room.bedType}
                                                    amenities={room.amenities}
                                                    images={room.imageUrls}
                                                />
                                            </div>

                                            {/* Packages */}
                                            <div className="w-full space-y-4">
                                                {room.packages?.map((pkg, i) => {
                                                    return (
                                                        <PackageCard
                                                            key={
                                                                pkg._id +
                                                                i +
                                                                pkg.roomTypeId._id +
                                                                Math.floor(Math.random())
                                                            }
                                                            pkg={pkg}
                                                            cart={cart}
                                                            onSelect={() => addToCart(pkg)}
                                                            onRemoveItem={() => removeItem(pkg._id)}
                                                        />
                                                    );
                                                })}
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <p>No rooms available for the selected dates.</p>
                                ))}
                            {activeTab === 'policies' && (
                                <div className="bg-white rounded-lg shadow-md p-6">
                                    <PropertyPolicies />
                                </div>
                            )}
                            {activeTab === 'reviews' && (
                                <div className="bg-white rounded-lg shadow-md p-6">
                                    <PropertyReviews placeId={guestSelectedProperty.address.placeId} />
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            )}
        </>
    );
}

export default RoomAvailability;
