import { useEffect, useState } from 'react';
import type { GroupReservation } from '../../../../interfaces/IBooking.ts';
import { useNavigate, useParams, useSearchParams } from 'react-router';
import { useApiHook } from '../../../../hooks/useApi.ts';
import ConfirmationDialog from '../../../../components/common/ConfirmationDialog.tsx';
import CancelPopup from '../../../../components/common/CancelPopup.tsx';

function CancelPage() {
    const [isCancelling, setIsCancelling] = useState<boolean>(false);
    const [booking, setBooking] = useState<GroupReservation | null>(null);
    const [_selectedReason, setSelectedReason] = useState<string | null>(null);
    const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState<boolean>(false);
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const { get } = useApiHook();
    const { bookingId } = useParams();
    const redirectTo = searchParams.get('type');

    console.warn(redirectTo);

    const handleCancelPopupClose = () => {
        setIsCancelling(false);
        setIsConfirmationDialogOpen(true);
    };
    useEffect(() => {
        const fetchReservation = async () => {
            if (!bookingId) return;
            try {
                const response = await get<GroupReservation>(`/reservations/${bookingId}`);
                setBooking(response);
            } catch (error) {
                console.error('Error fetching reservation', error);
            }
        };

        fetchReservation();
    }, [bookingId]);

    const handleReasonClick = (reason: string) => {
        setSelectedReason(reason);
        setIsCancelling(true);
    };

    const reasons = [
        'Found a better price',
        "Don't Need this stay option",
        'Facing an issue at the property',
        'Plan Change / Flight Details',
    ];
    if (!bookingId && !booking) {
        return <div>No booking Id</div>;
    }
    return (
        <div className="flex items-center justify-center min-h-screen bg-gray-100 px-4">
            {setIsConfirmationDialogOpen && (
                <ConfirmationDialog
                    onConfirm={() => {
                        navigate('/guest');
                    }}
                    onClose={() => setIsConfirmationDialogOpen(false)}
                    isOpen={isConfirmationDialogOpen}
                    description="You have Cancle the booking details. Please check your email."
                    title="Canceled"
                    confirmText="Ok"
                    cancelText={'Close'}
                />
            )}
            <div className="w-full theme-border max-w-lg bg-white p-10 rounded-xl shadow-xl space-y-6">
                <h1 className="text-xl font-semibold text-center text-gray-800">
                    Please Select the Reason for Cancellation
                </h1>

                <div className="bg-gray-50 p-4 rounded-lg border">
                    <div className="flex justify-between font-semibold items-center mb-4 px-3">
                        <p>Need to Modify this booking?</p>
                        <button
                            onClick={() => navigate(`/reservation/${bookingId}/update`)}
                            className="theme-text font-semibold"
                        >
                            Click here
                        </button>
                    </div>

                    <div className="space-y-2">
                        {reasons.map((reason, index) => (
                            <span
                                key={index}
                                onClick={() => handleReasonClick(reason)}
                                className="block theme-border w-full px-3 py-2 rounded-md transition hover:bg-indigo-50 hover:font-semibold hover:theme-text cursor-pointer"
                            >
                                {reason}
                            </span>
                        ))}
                    </div>
                </div>
            </div>

            {/* Popup always mounted, controlled by `isOpen` */}
            <CancelPopup isOpen={isCancelling} booking={booking!} onClose={handleCancelPopupClose} isMail={true} />
        </div>
    );
}

export default CancelPage;
