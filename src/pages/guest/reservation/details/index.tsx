import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Formik, Form, FieldArray } from 'formik';
import * as Yup from 'yup';
import 'react-datepicker/dist/react-datepicker.css';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { useLoadScript } from '@react-google-maps/api';
import { useToastHook } from '../../../../hooks/useToaster.ts';
import type { GroupReservation } from '../../../../interfaces/IBooking.ts';
import { useApiHook } from '../../../../hooks/useApi.ts';
import { GOOGLE_MAPS_API_KEY } from '../../../../constants/env.ts';
import { countryCodesOptions } from '../../../../utils/helpers/countryCodes.ts';
import { useParams } from 'react-router';
import UiLoader from '../../../../lib/UiLoader.tsx';
import UiInput from '../../../../lib/UiInput.tsx';
import { allowOnlyAlphabetsAndNumbers, allowOnlyNumbers } from '../../../../utils/helpers/inputValidation.ts';
import UiSelect from '../../../../lib/UiSelect.tsx';
import UiButton from '../../../../lib/UiButton.tsx';

interface Guest {
    firstName: string;
    lastName: string;
    email: string;
    phone: {
        countryCode: string;
        phoneNumber: string;
    };
}

interface FormValues {
    guestDetails: Guest[];
}

export interface GuestDetailsRef {
    triggerSubmit: () => Promise<FormValues | null>;
    isValid: boolean;
    errors: string[];
}

const BookerDetailsPage = forwardRef<GuestDetailsRef>((_, ref) => {
    const { showSuccess, showError } = useToastHook();
    const [formErrors, setFormErrors] = useState<string[]>([]);
    const [booking, setBooking] = useState<GroupReservation | null>(null);
    const { get, patch, loading } = useApiHook();

    const { isLoaded, loadError } = useLoadScript({
        googleMapsApiKey: GOOGLE_MAPS_API_KEY || '',
        libraries: ['places'],
    });

    const validationSchema = Yup.object().shape({
        guestDetails: Yup.array().of(
            Yup.object().shape({
                firstName: Yup.string().required('First name is required'),
                lastName: Yup.string().required('Last name is required'),
                email: Yup.string()
                    .email('Invalid email address')
                    .test(
                        'email-required-for-first-guest',
                        'Email is required for the primary guest',
                        function (value) {
                            const { path } = this;
                            const pathMatch = path.match(/guestDetails\[(\d+)\]\.email/);
                            if (pathMatch) {
                                const guestIndex = parseInt(pathMatch[1]);
                                if (guestIndex === 0) {
                                    return Boolean(value && value.length > 0);
                                }
                            }
                            return true;
                        }
                    ),
                phone: Yup.object().shape({
                    countryCode: Yup.string().required('Country code is required'),
                    phoneNumber: Yup.string()
                        .required('Phone Number is Required')
                        .test('min-length-by-country', function (value) {
                            const { parent } = this;
                            const selectedCode = countryCodesOptions.find(code => code.value === parent.countryCode);
                            const minLength = selectedCode?.phoneLength || 10;
                            if (value && value.length !== minLength) {
                                return this.createError({
                                    message: `Phone number must be exactly ${minLength} digits`,
                                });
                            }
                            return true;
                        }),
                }),
            })
        ),
    });

    const { bookingId } = useParams();
    const initialValues: FormValues = {
        guestDetails:
            booking?.reservations
                ?.map(reservation =>
                    reservation.guestDetails.map(guest => ({
                        firstName: guest.firstName || '',
                        lastName: guest.lastName || '',
                        email: guest.email || '',
                        phone: {
                            countryCode: guest.phone?.countryCode || '',
                            phoneNumber: guest.phone?.phoneNumber || '',
                        },
                    }))
                )
                .flat() || [],
    };

    useEffect(() => {
        const fetchReservation = async () => {
            if (!bookingId) return;
            try {
                const response = await get<GroupReservation>(`/reservations/${bookingId}`);
                setBooking(response);
            } catch (error) {
                console.error('Error fetching reservation', error);
            }
        };

        fetchReservation();
    }, [bookingId]);

    const handleSubmit = async (values: FormValues) => {
        if (!bookingId || !booking?.reservations) return;
        try {
            const response = await patch<GroupReservation>(`/reservations/${bookingId}`, values);
            showSuccess('Updated Guest Data Successfully');
            setBooking(response);
        } catch (err) {
            console.error('Error updating reservation', err);
            showError('Failed to update guest data');
        }
    };

    if (loading) {
        return <UiLoader label="Loading..." />;
    }

    return (
        <div className="bg-white rounded-xl mt-12 p-12">
            <Formik
                initialValues={initialValues}
                validationSchema={validationSchema}
                enableReinitialize
                onSubmit={values => handleSubmit(values)}
            >
                {({ values, isValid, validateForm, setFieldValue, setTouched }) => {
                    // console.log(errors)
                    // eslint-disable-next-line react-hooks/rules-of-hooks
                    useImperativeHandle(ref, () => ({
                        triggerSubmit: async () => {
                            const touchedFields = {
                                guestDetails: values.guestDetails.map(() => ({
                                    firstName: true,
                                    lastName: true,
                                    email: true,
                                    phone: {
                                        countryCode: true,
                                        phoneNumber: true,
                                    },
                                })),
                            };
                            setTouched(touchedFields);
                            const formErrors = await validateForm();

                            const errorMessages: string[] = [];
                            Object.keys(formErrors).forEach(key => {
                                // @ts-expect-error type fix
                                const error = formErrors[key];
                                if (typeof error === 'string') {
                                    errorMessages.push(error);
                                } else if (Array.isArray(error)) {
                                    error.forEach(item => {
                                        if (typeof item === 'string') {
                                            errorMessages.push(item);
                                        } else if (item && typeof item === 'object') {
                                            Object.values(item).forEach(val => {
                                                if (typeof val === 'string') {
                                                    errorMessages.push(val);
                                                }
                                            });
                                        }
                                    });
                                }
                            });

                            setFormErrors(errorMessages);
                            return Object.keys(formErrors).length === 0 ? values : null;
                        },
                        isValid,
                        errors: formErrors,
                    }));

                    return (
                        <Form className="space-y-4">
                            {loadError && <div>Error loading map</div>}
                            {!isLoaded && <UiLoader label="Loading..." />}
                            <div>
                                <h3 className="text-lg font-semibold text-gray-900 mb-3">Guest Information</h3>
                                <FieldArray name="guestDetails">
                                    {({ push, remove }) => (
                                        <div className="space-y-4">
                                            {values.guestDetails.map((_, index) => (
                                                <div key={index} className="rounded-lg p-4 relative">
                                                    <>
                                                        <h3 className="text-sm mb-3 font-bold">Guest {index + 1}</h3>
                                                        {index > 0 && (
                                                            <button
                                                                type="button"
                                                                onClick={() => remove(index)}
                                                                className="absolute top-3 right-3 text-gray-400 hover:text-red-500"
                                                            >
                                                                <XMarkIcon className="h-4 w-4" />
                                                            </button>
                                                        )}
                                                    </>
                                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
                                                        <UiInput
                                                            label="First Name"
                                                            name={`guestDetails[${index}].firstName`}
                                                            placeholder="John"
                                                            onKeyDown={allowOnlyAlphabetsAndNumbers}
                                                            required
                                                        />
                                                        <UiInput
                                                            label="Last Name"
                                                            name={`guestDetails[${index}].lastName`}
                                                            placeholder="Doe"
                                                            onKeyDown={allowOnlyAlphabetsAndNumbers}
                                                            required
                                                        />
                                                    </div>
                                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                                        <UiInput
                                                            label="Email"
                                                            name={`guestDetails[${index}].email`}
                                                            onKeyDown={() => {}}
                                                            placeholder="<EMAIL>"
                                                            required={index === 0}
                                                        />
                                                        <div className="flex flex-col w-full">
                                                            <label className="mr-2">
                                                                Phone {index === 0 ? '*' : ''}
                                                            </label>
                                                            <div className="flex items-start">
                                                                <UiSelect
                                                                    name={`guestDetails[${index}].phone.countryCode`}
                                                                    options={countryCodesOptions.map(code => ({
                                                                        label: code.label,
                                                                        value: code.value,
                                                                    }))}
                                                                    onChange={value => {
                                                                        setFieldValue(
                                                                            `guestDetails[${index}].phone.countryCode`,
                                                                            value[0] || ''
                                                                        );
                                                                    }}
                                                                    containerClassName="min-w-[150px] rounded-md"
                                                                    inputClassName={'h-10'}
                                                                />
                                                                <UiInput
                                                                    label=""
                                                                    name={`guestDetails[${index}].phone.phoneNumber`}
                                                                    placeholder="Enter phone number"
                                                                    onKeyDown={allowOnlyNumbers}
                                                                    required={true}
                                                                    widthClassname={
                                                                        'w-full rounded-l-none border-l-none'
                                                                    }
                                                                />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                            <button
                                                type="button"
                                                onClick={() =>
                                                    push({
                                                        firstName: '',
                                                        lastName: '',
                                                        email: '',
                                                        phone: {
                                                            countryCode: '',
                                                            phoneNumber: '',
                                                        },
                                                    })
                                                }
                                                className="ml-auto px-4 py-2 border theme-border rounded-full hover:bg-blue-50 font-medium flex items-center gap-2"
                                            >
                                                <span className="theme-text">+ Add Another Guest</span>
                                            </button>
                                        </div>
                                    )}
                                </FieldArray>
                            </div>

                            <UiButton
                                type="submit"
                                className="w-full text-white font-medium py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center"
                            >
                                Submit
                            </UiButton>
                        </Form>
                    );
                }}
            </Formik>
        </div>
    );
});

export default BookerDetailsPage;
