import BookingIdPage from './BookingIdForm';
import EmailFormPage from './EmailForm';

const ReservationVerify: React.FC = () => {
    return (
        <>
            <div className="min-h-screen flex items-center justify-center px-4 py-10 bg-gray-50">
                <div className="w-full max-w-md space-y-6">
                    <EmailFormPage />

                    <div className="flex items-center justify-center">
                        <p className="text-sm text-gray-500 font-medium">OR</p>
                    </div>

                    <BookingIdPage />
                </div>
            </div>
        </>
    );
};

export default ReservationVerify;
