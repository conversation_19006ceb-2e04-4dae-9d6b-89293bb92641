import { Formik, Form, type FormikHelpers } from 'formik';
import * as Yup from 'yup';
import { useToastHook } from '../../../../hooks/useToaster.ts';
import { useApiHook } from '../../../../hooks/useApi.ts';
import { useNavigate } from 'react-router';
import useBoundStore from '../../../../store/useBoundStore.ts';
import type { ILoginResponse } from '../../../../interfaces/ILoginResponse.ts';
import storageService from '../../../../services/storage-service.ts';
import UiInput from '../../../../lib/UiInput.tsx';
import UiButton from '../../../../lib/UiButton.tsx';

interface FormValues {
    email: string;
    password: string;
}

const initialValues: FormValues = {
    email: '',
    password: '',
};

const validationSchema = Yup.object({
    email: Yup.string().email('Invalid email address').required('Email is required'),
    password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
});

const EmailFormPage: React.FC = () => {
    const { showSuccess, showError } = useToastHook();
    const { post } = useApiHook();
    const navigate = useNavigate();
    const { updateLoggedUser } = useBoundStore();

    const handleSubmit = async (values: FormValues, { setSubmitting }: FormikHelpers<FormValues>) => {
        try {
            const response = await post<ILoginResponse>('/authentication/login', values);

            if (response) {
                const { token, user } = response;

                storageService.setItem('currentUser', response);
                updateLoggedUser({ token, user });

                showSuccess('Login successful');
                navigate('/guest/profile');
            } else {
                showError('Login failed. Please check your credentials.');
            }
        } catch (_err) {
            showError('Something went wrong');
        } finally {
            setSubmitting(false);
        }
    };

    return (
        <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={handleSubmit}>
            {({ isSubmitting }) => (
                <Form className="theme-border rounded-xl bg-white p-8 shadow-md space-y-5">
                    <UiInput label="Email" name="email" type="email" placeholder="Enter your email address" />
                    <UiInput label="Password" name="password" type="password" placeholder="••••••••" />
                    <UiButton
                        type="submit"
                        disabled={isSubmitting}
                        className="w-full text-white font-medium py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center"
                    >
                        {isSubmitting ? 'Logging in...' : 'Submit'}
                    </UiButton>
                </Form>
            )}
        </Formik>
    );
};

export default EmailFormPage;
