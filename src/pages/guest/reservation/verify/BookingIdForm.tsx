import { Formik, Form, type FormikHelpers } from 'formik';
import * as Yup from 'yup';
import { ArrowRightCircleIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router';
import { useApiHook } from '../../../../hooks/useApi.ts';
import { useToastHook } from '../../../../hooks/useToaster.ts';
import UiInput from '../../../../lib/UiInput.tsx';
import UiButton from '../../../../lib/UiButton.tsx';
import type { GroupReservation } from '../../../../interfaces/IBooking.ts';
import UiLoader from '../../../../lib/UiLoader.tsx';

interface FormValues {
    bookingId: string;
    otp: string;
}

const initialValues: FormValues = {
    bookingId: '',
    otp: '',
};

const BookingIdPage: React.FC = () => {
    const [enterOtp, setEnterOtp] = useState(false);
    const { showSuccess, showError } = useToastHook();
    const navigate = useNavigate();
    const [isFormSubmitting, setIsFormSubmitting] = useState(false);
    const { post } = useApiHook();
    const [params] = useSearchParams();
    const redirectTo = params.get('type');

    const getValidationSchema = (requireOtp: boolean) =>
        Yup.object({
            bookingId: Yup.string().required('Booking ID is required'),
            otp: requireOtp ? Yup.string().required('OTP is required') : Yup.string().notRequired(),
        });

    // First step: Send OTP using bookingId
    const handleSendOtp = async (bookingId: string) => {
        if (!bookingId) {
            showError('Please enter a valid Booking ID.');
            return;
        }

        try {
            setIsFormSubmitting(true);
            await post('reservations/cancel/get-otp', {
                reservationCode: bookingId.trim(),
            });
            showSuccess('OTP sent to your registered contact.');
            setEnterOtp(true);
        } catch (_err) {
            showError('Failed to send OTP.');
        } finally {
            setIsFormSubmitting(false);
        }
    };

    // Second step: Submit OTP for verification
    const handleSubmit = async (values: FormValues, { setSubmitting }: FormikHelpers<FormValues>) => {
        try {
            if (!values.otp) {
                showError('Please enter the OTP sent to your booking ID.');
                return;
            }

            const response = await post<GroupReservation>('/reservations/cancel/verify-otp', {
                reservationCode: values.bookingId.trim(),
                otp: values.otp.trim(),
            });
            showSuccess('OTP verified successfully.');
            navigate(`/reservation/${response._id}/${redirectTo}`);
        } catch (_err) {
            showError('Invalid Booking ID or OTP.');
        } finally {
            setSubmitting(false);
        }
    };

    return (
        <Formik initialValues={initialValues} validationSchema={getValidationSchema(enterOtp)} onSubmit={handleSubmit}>
            {({ isSubmitting, values }) => (
                <Form className="theme-border rounded-xl bg-white p-8 shadow-md space-y-5">
                    {/* Booking ID input and OTP trigger */}
                    <div className="relative w-full">
                        <UiInput label="Booking ID" name="bookingId" type="text" placeholder="Enter your booking ID" />
                        {isFormSubmitting ? (
                            <UiLoader label={'Submitting...'} />
                        ) : (
                            <ArrowRightCircleIcon
                                className="w-9 h-9 absolute top-[25px] right-3 cursor-pointer"
                                onClick={() => handleSendOtp(values.bookingId)}
                            />
                        )}
                    </div>

                    {/* Show OTP field only after user clicks icon */}
                    {enterOtp && (
                        <>
                            <UiInput name="otp" label="OTP" type="text" placeholder="Enter OTP" />

                            <UiButton
                                type="submit"
                                disabled={isSubmitting}
                                className="w-full text-white font-medium py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center"
                            >
                                Submit
                            </UiButton>
                        </>
                    )}
                </Form>
            )}
        </Formik>
    );
};

export default BookingIdPage;
