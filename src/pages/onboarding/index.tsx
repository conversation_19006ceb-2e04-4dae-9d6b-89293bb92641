import { useEffect, useMemo, useState } from 'react';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';

import { BuildingOffice2Icon, ArrowRightIcon } from '@heroicons/react/24/outline';
import { useNavigate } from 'react-router';
import { useApiHook } from '../../hooks/useApi.ts';
import useBoundStore from '../../store/useBoundStore.ts';
import type { ILocation } from '../../interfaces/ILocation.ts';
import type { IDomainValue } from '../../interfaces/IDomainValue.ts';
import { useToastHook } from '../../hooks/useToaster.ts';
import storageService from '../../services/storage-service.ts';
import type { ICompany } from '../../interfaces/ICompany.ts';
import { domainValue } from '../../utils/constants/domainValues.ts';
import { useLoadScript } from '@react-google-maps/api';
import { GOOGLE_MAPS_API_KEY } from '../../constants/env.ts';
import UiLoader from '../../lib/UiLoader.tsx';
import UiSelect from '../../lib/UiSelect.tsx';
import UiMapInput from '../../lib/UiMapInput.tsx';
import UiInput from '../../lib/UiInput.tsx';
import { allowOnlyNumbers } from '../../utils/helpers/inputValidation.ts';
import UiDatePicker from '../../lib/UiDatepicker.tsx';
import GoogleMapUi from '../../lib/LocationPicker.tsx';
import ConfirmationDialog from '../../components/common/ConfirmationDialog.tsx';

function ServiceDetails() {
    const navigate = useNavigate();
    const { loading, error, post, get } = useApiHook();
    const { showError } = useToastHook();
    const { loggedUser } = useBoundStore();
    const [locations, setLocations] = useState<ILocation[]>([]);
    const [isSuccessDialogOpen, setIsSuccessDialogOpen] = useState(false);
    const [serviceTypes, setServiceTypes] = useState<IDomainValue[]>([]);

    useEffect(() => {
        const fetchLocations = async () => {
            const response = await get<ILocation[]>('/locations');
            setLocations(response);
        };
        fetchLocations();
        fetchServiceTypes();
    }, []);

    const locationOptions = useMemo(
        () =>
            locations.map(location => ({
                value: location._id || '',
                label: `${location.name} (${location.code})`,
            })),
        [locations]
    );

    const initialValues = {
        address: {
            address1: '',
            address2: '',
            city: '',
            state: '',
            country: '',
            zipcode: '',
            placeId: '',
            latitude: '',
            longitude: '',
            locationId: '',
        },
        name: '',
        built: '',
        premisesType: '',
        bookingStarts: new Date(),
        serviceType: '',
        // channelManager: 'No',
    };

    const validationSchema = Yup.object({
        address: Yup.object({
            address1: Yup.string().required('Address is required'),
            address2: Yup.string(),
            city: Yup.string(),
            state: Yup.string().required('State is required'),
            country: Yup.string().required('Country is required'),
            zipcode: Yup.string(),
            placeId: Yup.string(),
            latitude: Yup.string(),
            longitude: Yup.string(),
            locationId: Yup.string().required('Near by airport is required'),
        }),
        premisesType: Yup.string().required('Premises type is required'),
        name: Yup.string().required('Property name is required'),
        built: Yup.string().required('Business built is required'),
        serviceType: Yup.string().required('Service Type is required'),
        bookingStarts: Yup.date().required('Booking start date is required'),
    });

    const handleSubmit = async (values: typeof initialValues) => {
        storageService.setItem('newPropertyData', values);
        const user = { ...loggedUser.user };
        const { id: _id, username: _un, ...userWithoutId } = user;
        const body = {
            name: values.name,
            premisesType: values.premisesType,
            // @ts-expect-error needs to fix types
            address: { ...values.address, placeId: values.placeId, city: values.address.city || values.address.state },
            poc: {
                email: userWithoutId.email,
                firstName: userWithoutId.firstName,
                lastName: userWithoutId.lastName,
                phone: userWithoutId.phone,
            },
            phone: loggedUser.user?.phone,
            customFields: {
                built: values.built,
                bookingStarts: values.bookingStarts,
                serviceType: values.serviceType,
                // channelManager: values.channelManager
            },
        };

        try {
            const response = await post<ICompany>('/properties', body);
            if (response) {
                // router.push(`/onboarding/service-list?propertyId=${response._id}`);
                setIsSuccessDialogOpen(true);
            }
        } catch (_err) {
            if (error) {
                showError(error);
            }
        }
    };

    const fetchServiceTypes = async () => {
        const response = await get<IDomainValue[]>(`/domain-values/categoryId/${domainValue.ServicesCategories}`);
        setServiceTypes(response);
    };

    const { isLoaded, loadError } = useLoadScript({
        googleMapsApiKey: GOOGLE_MAPS_API_KEY || '',
        libraries: ['places'],
    });

    if (loadError) return <div>Error loading map</div>;
    if (!isLoaded) return <UiLoader label="Loading..." />;
    if (!loggedUser.token) return <div>Redirecting...</div>;

    return (
        <div className="min-h-screen bg-gradient-to-br from-indigo-50 to-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            {loading && <UiLoader label="Loading..." />}
            <div className="max-w-7xl mx-auto">
                <div className="text-center mb-10">
                    <div className="mx-auto h-16 w-16 bg-indigo-100 rounded-full flex items-center justify-center mb-4">
                        <BuildingOffice2Icon className="h-8 w-8 text-indigo-600" />
                    </div>
                    <h2 className="text-3xl font-bold text-gray-900 mb-2">Property Details</h2>
                    <p className="text-gray-600">Tell us about your property to get started</p>
                </div>

                <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={handleSubmit}>
                    {({ errors, touched, setFieldValue, isSubmitting, values }) => {
                        return (
                            <Form>
                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-10">
                                    {/* FORM COLUMN */}
                                    <div className="bg-white shadow-xl rounded-2xl p-8 sm:p-10 space-y-6">
                                        <div>
                                            {locations.length > 0 && (
                                                <div className="mb-6">
                                                    <UiSelect
                                                        name={'address.locationId'}
                                                        label={'Nearby Airport'}
                                                        options={locationOptions}
                                                        onChange={value =>
                                                            setFieldValue('address.locationId', value[0])
                                                        }
                                                        required
                                                    />
                                                </div>
                                            )}
                                            <h3 className="text-lg font-medium text-gray-900 mb-4">
                                                Location Information
                                            </h3>
                                            <UiMapInput
                                                defaultValue={values.address.address1}
                                                required
                                                label="Property Address"
                                                placeholder="Search for an address"
                                                onLocationSelect={location => {
                                                    if (!location) return;
                                                    setFieldValue('address.address1', location.address || '');
                                                    setFieldValue(
                                                        'address.address2',
                                                        location.address?.split(' ')[1] || ''
                                                    );
                                                    setFieldValue('address.city', location.city);
                                                    setFieldValue('address.state', location.state || '');
                                                    setFieldValue('address.country', location.country || '');
                                                    setFieldValue('address.zipcode', location.zipcode || '');
                                                    setFieldValue('placeId', location.placeId || '');
                                                    setFieldValue('address.latitude', location.latitude || '');
                                                    setFieldValue('address.longitude', location.longitude || '');
                                                }}
                                            />
                                            {errors.address?.address1 && touched.address?.address1 && (
                                                <p className="mt-1 text-sm text-red-600">{errors.address.address1}</p>
                                            )}
                                        </div>

                                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                                            <UiInput
                                                label="Property Name"
                                                name="name"
                                                placeholder="e.g. Grand Hotel"
                                                type="text"
                                                required
                                            />
                                            <UiInput
                                                label="Build Year"
                                                name="built"
                                                placeholder="e.g. 2020"
                                                type="text"
                                                onKeyDown={allowOnlyNumbers}
                                                required
                                            />
                                        </div>
                                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                                            <UiSelect
                                                name={'serviceType'}
                                                label={'Service Type'}
                                                options={serviceTypes.map(service => ({
                                                    value: service._id,
                                                    label: service.name,
                                                }))}
                                                onChange={value => setFieldValue('serviceType', value[0])}
                                                required
                                            />
                                            <UiDatePicker
                                                name="bookingStarts"
                                                label="Accepting booking from"
                                                placeholder="Select Accepting booking from"
                                                minDate={new Date()}
                                                required
                                            />
                                        </div>

                                        {/* <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Do you work with a Channel Manager?
                    </label>
                    <div className="flex items-center space-x-6">
                      {['Yes', 'No'].map((option) => (
                        <label key={option} className="flex items-center space-x-2">
                          <Field
                            type="radio"
                            name="channelManager"
                            value={option}
                            className="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"
                          />
                          <span className="text-sm text-gray-700">{option}</span>
                        </label>
                      ))}
                    </div>
                    {errors.channelManager && touched.channelManager && (
                      <p className="mt-1 text-sm text-red-600">{errors.channelManager}</p>
                    )}
                  </div> */}

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-3">
                                                Property Type <span className={'text-red-500'}>*</span>
                                            </label>
                                            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                                {['Airside', 'Landside'].map(type => (
                                                    <label
                                                        key={type}
                                                        className={`relative rounded-xl border p-4 cursor-pointer transition-colors ${
                                                            values.premisesType === type
                                                                ? 'border-indigo-500 bg-indigo-50'
                                                                : errors.premisesType && touched.premisesType
                                                                  ? 'border-red-300'
                                                                  : 'border-gray-200 hover:border-indigo-300'
                                                        }`}
                                                    >
                                                        <Field
                                                            type="radio"
                                                            name="premisesType"
                                                            value={type}
                                                            className="absolute opacity-0"
                                                        />
                                                        <div className="flex items-center">
                                                            <div
                                                                className={`h-5 w-5 rounded-full border flex items-center justify-center mr-3 ${
                                                                    values.premisesType === type
                                                                        ? 'border-indigo-500 bg-indigo-500'
                                                                        : errors.premisesType && touched.premisesType
                                                                          ? 'border-red-500'
                                                                          : 'border-gray-300'
                                                                }`}
                                                            >
                                                                {values.premisesType === type && (
                                                                    <svg
                                                                        className="h-2.5 w-2.5 text-white"
                                                                        viewBox="0 0 20 20"
                                                                        fill="currentColor"
                                                                    >
                                                                        <path
                                                                            fillRule="evenodd"
                                                                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                                            clipRule="evenodd"
                                                                        />
                                                                    </svg>
                                                                )}
                                                            </div>
                                                            <div>
                                                                <span className="block text-sm font-medium text-gray-900">
                                                                    {type}
                                                                </span>
                                                                <span className="block text-sm text-gray-500">
                                                                    {type === 'Landside'
                                                                        ? 'Standard hotel location'
                                                                        : 'Airport terminal location'}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </label>
                                                ))}
                                            </div>
                                            {errors.premisesType && touched.premisesType && (
                                                <p className="mt-2 text-sm text-red-600">{errors.premisesType}</p>
                                            )}
                                        </div>

                                        {/*{errors && <p>{errors}</p>}*/}

                                        <div className="pt-4">
                                            <button
                                                type="submit"
                                                disabled={isSubmitting}
                                                className="w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white theme-background transition-colors"
                                            >
                                                Submit Request
                                                <ArrowRightIcon className="ml-2 h-4 w-4" />
                                            </button>
                                        </div>
                                    </div>

                                    {/* MAP COLUMN */}
                                    <div className="bg-white shadow-xl rounded-2xl p-6">
                                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                                            Pick a Location on the Map
                                        </h3>
                                        <GoogleMapUi
                                            placeId={values.address.placeId}
                                            width="100%"
                                            height="400px"
                                            onPointSelect={location => {
                                                if (!location) return;
                                                setFieldValue('address.address1', location.address || '');
                                                setFieldValue(
                                                    'address.address2',
                                                    location.address?.split(' ')[1] || ''
                                                );
                                                setFieldValue('address.city', location.city || '');
                                                setFieldValue('address.state', location.state || '');
                                                setFieldValue('address.country', location.country || '');
                                                setFieldValue('address.zipcode', location.zipcode || '');
                                                setFieldValue('address.placeId', location.placeId || '');
                                                setFieldValue('address.latitude', location.lat || '');
                                                setFieldValue('address.longitude', location.lng || '');
                                            }}
                                        />
                                    </div>
                                </div>
                            </Form>
                        );
                    }}
                </Formik>
                <ConfirmationDialog
                    isOpen={isSuccessDialogOpen}
                    onClose={() => {
                        setIsSuccessDialogOpen(false);
                        navigate('/guest');
                    }}
                    onConfirm={() => {
                        setIsSuccessDialogOpen(false);
                        navigate('/guest');
                    }}
                    title="Thank you for sharing your basic details"
                    description="Our team will review the information and get in touch with you shortly to continue the onboarding process."
                    confirmText="Okay"
                    cancelText=""
                    confirmButtonClassName="bg-green-600 hover:bg-green-700"
                    cancelButtonClassName="hidden"
                />
            </div>
        </div>
    );
}

export default ServiceDetails;
