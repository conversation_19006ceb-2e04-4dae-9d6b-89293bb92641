import { useNavigate } from 'react-router';
import { ExclamationTriangleIcon, HomeIcon, ArrowUturnLeftIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

const ErrorPage = () => {
    const navigate = useNavigate();

    const handleRefresh = () => {
        navigate(0);
    };

    const handleGoBack = () => {
        navigate(-1);
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center px-4 py-12">
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-blue-200 rounded-full opacity-20 animate-pulse [animation-duration:2s] [animation-timing-function:ease-in-out]"></div>
                <div className="absolute top-3/4 right-1/4 w-24 h-24 bg-purple-200 rounded-full opacity-20 animate-bounce [animation-duration:1.5s] [animation-timing-function:ease-in-out]"></div>
                <div className="absolute bottom-1/4 left-1/3 w-16 h-16 bg-pink-200 rounded-full opacity-30 animate-pulse [animation-duration:2s] [animation-timing-function:ease-in-out] delay-1000"></div>
            </div>

            <div className="relative max-w-lg w-full bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/20 p-8 text-center space-y-8 animate-fade-in">
                <div className="relative mx-auto w-20 h-20">
                    <div className="absolute inset-0 bg-gradient-to-r from-red-500 via-pink-500 to-orange-500 rounded-full animate-spin [animation-duration:4s] opacity-20"></div>
                    <div className="relative w-full h-full bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg">
                        <ExclamationTriangleIcon className="h-10 w-10 text-white animate-bounce [animation-duration:1.5s] [animation-timing-function:ease-in-out]" />
                    </div>
                </div>
                <div className="space-y-3">
                    <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                        Oops! Something Went Wrong
                    </h1>
                    <p className="text-gray-600 leading-relaxed text-lg">
                        Don't worry, these things happen! Let's get you back to where you need to be.
                    </p>
                </div>
                <div className="flex flex-col gap-4 justify-center">
                    <button
                        onClick={() => navigate('/')}
                        className="group flex items-center justify-center gap-3 theme-background text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95"
                    >
                        <HomeIcon className="h-5 w-5 group-hover:animate-bounce [animation-duration:1.5s] [animation-timing-function:ease-in-out]" />
                        Take Me Home
                    </button>

                    <div className="flex gap-3">
                        <button
                            onClick={handleRefresh}
                            className="group flex-1 flex items-center justify-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 active:scale-95 border border-gray-200 hover:border-gray-300"
                        >
                            <ArrowPathIcon className="h-5 w-5 group-hover:rotate-180 transition-transform duration-300" />
                            Refresh
                        </button>
                        <button
                            onClick={handleGoBack}
                            className="group flex-1 flex items-center justify-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 active:scale-95 border border-gray-200 hover:border-gray-300"
                        >
                            <ArrowUturnLeftIcon className="h-5 w-5 group-hover:-translate-x-1 transition-transform duration-200" />
                            Go Back
                        </button>
                    </div>
                </div>

                {/* Additional Info with better styling */}
                <div className="pt-4 border-t border-gray-100">
                    <div className="inline-flex items-center gap-2 text-sm text-gray-500 bg-gray-50 px-4 py-2 rounded-full">
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse [animation-duration:2s] [animation-timing-function:ease-in-out]"></div>
                        Our team has been notified and is working on a fix
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ErrorPage;
