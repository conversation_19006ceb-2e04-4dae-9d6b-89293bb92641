import { Link, useNavigate } from 'react-router';
import { HomeIcon, ArrowLeftIcon, MagnifyingGlassIcon } from '@heroicons/react/24/solid';

const NotFound = () => {
    const navigate = useNavigate();
    const handleGoBack = () => {
        navigate(-1);
    };

    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-red-50 via-white to-red-100 px-4 relative overflow-hidden">
            {/* Animated background elements */}
            <div className="absolute inset-0 overflow-hidden">
                <div className="absolute -top-40 -right-40 w-80 h-80 bg-red-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
                <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-red-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse animation-delay-2000"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-rose-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse animation-delay-4000"></div>
            </div>

            {/* Main content */}
            <div className="relative z-10 text-center max-w-2xl mx-auto">
                {/* 404 Number with gradient and animation */}
                <div className="relative mb-8">
                    <h1 className="text-9xl md:text-[12rem] font-black bg-gradient-to-r from-red-700  to-red-500 bg-clip-text text-transparent animate-bounce">
                        404
                    </h1>
                    <div className="absolute inset-0 text-9xl md:text-[12rem] font-black text-red-200 -z-10 transform translate-x-2 translate-y-2">
                        404
                    </div>
                </div>

                {/* Error message */}
                <div className="mb-8 space-y-4">
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Oops! Page Not Found</h2>
                    <p className="text-lg text-gray-600 leading-relaxed max-w-md mx-auto">
                        The page you're looking for seems to have wandered off into the digital void. Don't worry, it
                        happens to the best of us!
                    </p>
                </div>

                {/* Action buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                    <Link
                        to="/"
                        className="group flex items-center gap-2 bg-gradient-to-r from-red-600 to-rose-600 hover:from-red-700 hover:to-rose-700 text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
                    >
                        <HomeIcon className="w-5 h-5 group-hover:animate-pulse" />
                        Go Home
                    </Link>

                    <button
                        onClick={handleGoBack}
                        className="group flex items-center gap-2 bg-white hover:bg-gray-50 text-gray-700 px-6 py-3 rounded-full font-semibold border-2 border-gray-200 hover:border-gray-300 transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
                    >
                        <ArrowLeftIcon className="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-300" />
                        Go Back
                    </button>
                </div>

                {/* Additional help section */}
                <div className="mt-12 p-6 bg-white/70 backdrop-blur-sm rounded-2xl border border-white/20 shadow-lg">
                    <div className="flex items-center justify-center gap-2 mb-3">
                        <MagnifyingGlassIcon className="w-5 h-5 text-red-600" />
                        <h3 className="text-lg font-semibold text-gray-800">Need Help?</h3>
                    </div>
                    <p className="text-gray-600 text-sm leading-relaxed">
                        Try checking the URL for typos, or use the navigation menu to find what you're looking for.
                    </p>
                </div>
            </div>

            {/* Floating particles animation */}
            <div className="absolute inset-0 pointer-events-none">
                <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-red-400 rounded-full animate-ping animation-delay-1000"></div>
                <div className="absolute top-3/4 right-1/4 w-2 h-2 bg-rose-400 rounded-full animate-ping animation-delay-3000"></div>
                <div className="absolute top-1/2 left-3/4 w-2 h-2 bg-red-300 rounded-full animate-ping animation-delay-5000"></div>
            </div>
        </div>
    );
};

export default NotFound;
