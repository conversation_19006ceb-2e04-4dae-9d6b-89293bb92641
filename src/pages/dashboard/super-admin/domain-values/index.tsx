import { useState } from 'react';
import type { IDomainValue } from '../../../../interfaces/IDomainValue.ts';
import DomainValues from '../../common/domain-values';

function SuperAdminDomainValues() {
    const [selectedDomainValue, setSelectedDomainValue] = useState<IDomainValue | null>(null);

    const fetchMetaUrl = '/domain-values?level=SYSTEM_META';
    const fetchDataUrl = selectedDomainValue
        ? `/domain-values?level=SYSTEM_DATA&propertyId=Elaachi&categoryId=${selectedDomainValue._id}`
        : '/domain-values?level=SYSTEM_DATA&propertyId=Elaachi';
    const deleteUrlPrefix = '/domain-values';
    const propertyId = 'Elaachi';

    return (
        <div className="min-h-screen bg-gray-100">
            <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <DomainValues
                    propertyId={propertyId}
                    fetchMetaUrl={fetchMetaUrl}
                    fetchDataUrl={fetchDataUrl}
                    deleteUrlPrefix={deleteUrlPrefix}
                    selectedDomainValue={selectedDomainValue}
                    setSelectedDomainValue={setSelectedDomainValue}
                />
            </div>
        </div>
    );
}

export default SuperAdminDomainValues;
