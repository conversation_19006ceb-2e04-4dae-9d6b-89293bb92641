import { useEffect, useMemo, useState } from 'react';
import useBoundStore from '../../../../store/useBoundStore.ts';
import type { GroupReservation } from '../../../../interfaces/IBooking.ts';
import { useApiHook } from '../../../../hooks/useApi.ts';
import type { ColumnDef } from '@tanstack/react-table';
import { formatDate, formatDateTime } from '../../../../utils/helpers/dateHelpers.ts';
import CurrencyFormat from '../../../../components/common/CurrencyFormat.tsx';
import UiTable from '../../../../lib/UiTable.tsx';
import UiPopup from '../../../../lib/UiPopup.tsx';
import UiLoader from '../../../../lib/UiLoader.tsx';
import { MASTER_COMPANY_ID } from '../../../../constants/env.ts';
import { formatWithCurrency } from '../../../../utils/helpers/currencyHelpers.ts';

function LiveTracking() {
    const { selectedProperty } = useBoundStore();
    const [popupOpen, setPopupOpen] = useState(false);
    const [selectedReservation, setSelectedReservation] = useState<GroupReservation | null>(null);
    const [reservations, setReservations] = useState<GroupReservation[]>([]);
    const { get, loading } = useApiHook();
    // TODO: Remove this
    const companyKey = MASTER_COMPANY_ID;

    useEffect(() => {
        const fetchReservations = async () => {
            const response = await get<GroupReservation[]>(`reservations/property/${companyKey}`);
            if (response) {
                setReservations(response);
            }
        };
        fetchReservations();
    }, [selectedProperty]);

    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case 'confirmed':
                return 'bg-green-100 text-green-800';
            case 'blocked':
                return 'bg-yellow-100 text-yellow-800';
            case 'pending':
                return 'bg-blue-100 text-blue-800';
            case 'cancelled':
                return 'bg-red-100 text-red-800';
            case 'completed':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };
    // const getPaymentStatusColor = (status: string) => {
    //     switch (status.toLowerCase()) {
    //         case "paid":
    //             return "bg-green-100 text-green-800";
    //         case "pending":
    //             return "bg-yellow-100 text-yellow-800";
    //         case "unpaid":
    //             return "bg-red-100 text-red-800";
    //         case "cancelled":
    //             return "bg-gray-100 text-gray-800";
    //         case "refunded":
    //             return "bg-blue-100 text-blue-800";
    //         default:
    //             return "bg-gray-100 text-gray-800";
    //     }
    // };

    const columns = useMemo<ColumnDef<GroupReservation>[]>(
        () => [
            {
                accessorKey: 'createdAt',
                header: 'Booking Datetime',
                cell: ({ row }) => (
                    <div className="font-medium text-gray-900">{formatDate(new Date(row.original.createdAt))}</div>
                ),
            },
            {
                accessorKey: 'propertyId.name',
                header: 'Property Name',
                cell: ({ row }) => <div className="font-medium text-gray-900">{row.original.propertyId.name}</div>,
            },
            {
                accessorKey: 'propertyId.address.locationId.name',
                header: 'Airport',
                cell: ({ row }) => (
                    <div className="font-medium text-gray-900">{row.original.propertyId.address.locationId.name}</div>
                ),
            },
            {
                accessorKey: 'reservations',
                header: 'Rooms & Guests',
                cell: ({ row }) => {
                    const reservations = row.original.reservations;
                    const noOfRooms = reservations.length;
                    const firstReservation = reservations[0] || {};
                    const totalGuests = (firstReservation.noOfAdults || 0) + (firstReservation.noOfChildren || 0);

                    return (
                        <div className="space-y-1">
                            <div className="text-sm font-medium text-gray-900">
                                {noOfRooms} Room{noOfRooms > 1 ? 's' : ''}
                            </div>
                            <div className="text-sm text-gray-500">
                                {totalGuests} Guest{totalGuests > 1 ? 's' : ''}
                            </div>
                        </div>
                    );
                },
            },

            {
                accessorKey: 'reservations',
                header: 'Check-in/Check-out',
                cell: ({ row }) => {
                    const firstReservation = row.original.reservations[0];
                    const lastReservation = row.original.reservations[row.original.reservations.length - 1];
                    return (
                        <div className="space-y-1">
                            <div className="text-sm">
                                <span className="font-medium text-gray-900">In:</span>{' '}
                                {formatDateTime(new Date(firstReservation.startDateTime))}
                            </div>
                            <div className="text-sm">
                                <span className="font-medium text-gray-900">Out:</span>{' '}
                                {formatDateTime(new Date(lastReservation.endDateTime))}
                            </div>
                        </div>
                    );
                },
            },
            {
                accessorKey: 'totalAmount',
                header: 'Total Amount',
                cell: ({ row }) => {
                    const totalAmount = row.original.reservations[0]?.totalAmount;
                    return (
                        <div className="font-medium text-gray-900">
                            <CurrencyFormat amount={totalAmount} />
                        </div>
                    );
                },
            },
            {
                accessorKey: 'status',
                header: 'Booking Status',
                cell: ({ row }) => (
                    <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                            row.original.status
                        )}`}
                    >
                        {row.original.status.charAt(0).toUpperCase() + row.original.status.slice(1)}
                    </span>
                ),
            },

            {
                id: 'actions',
                header: 'Actions',
                cell: ({ row }) => (
                    <div className="flex items-center space-x-2">
                        <button
                            onClick={() => handleViewDetails(row.original)}
                            className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                        >
                            View
                        </button>
                    </div>
                ),
            },
        ],
        []
    );

    const handleViewDetails = (reservation: GroupReservation) => {
        // TODO: Implement view details modal/page
        console.error('View details for:', reservation.groupReservationId);
        setSelectedReservation(reservation);
        setPopupOpen(true);
    };

    // const handleConfirm = (reservation: GroupReservation) => {
    //     // TODO: Implement confirm reservation
    //     console.error("Confirm reservation:", reservation.groupReservationId);
    // };
    //
    // const handleCancel = (reservation: GroupReservation) => {
    //     // TODO: Implement cancel reservation
    //     console.error("Cancel reservation:", reservation.groupReservationId);
    // };

    if (loading) {
        return <UiLoader label="Loading reservations..." />;
    }

    return (
        <div className="p-4">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">Reservations</h1>
                    <p className="text-gray-600">{selectedProperty.name}</p>
                </div>
                <div className="text-sm text-gray-500">
                    {reservations.length} reservation
                    {reservations.length !== 1 ? 's' : ''}
                </div>
            </div>

            <UiTable
                columns={columns}
                data={reservations}
                enableSorting={true}
                enableFiltering={false}
                enablePagination={true}
                enableGlobalFilter={true}
                defaultPageSize={10}
                className="bg-white rounded-lg shadow"
            />
            <UiPopup
                isOpen={popupOpen}
                onClose={() => setPopupOpen(false)}
                title="Reservation Details"
                width="w-full max-w-xl"
                height="max-h-[90vh]"
            >
                {selectedReservation ? (
                    <div
                        className="overflow-y-auto px-6 py-6 text-sm text-gray-700"
                        style={{ maxHeight: 'calc(90vh - 64px)', minHeight: '200px' }}
                    >
                        <div className="mb-6">
                            <h3 className="text-base font-semibold text-gray-900 mb-2">Property Details</h3>
                            <ul className="space-y-1">
                                <li>
                                    <span className="font-medium">Property Name:</span>{' '}
                                    {selectedReservation?.propertyId?.name}
                                </li>
                                <li>
                                    <span className="font-medium">Property Address:</span>{' '}
                                    {selectedReservation.propertyId.address.address1}
                                </li>
                                <li>
                                    <span className="font-medium">Point of Contact (Name):</span>{' '}
                                    {selectedReservation.propertyId.poc?.firstName}
                                </li>
                                <li>
                                    <span className="font-medium">Point of Contact (Phone Number):</span>{' '}
                                    {selectedReservation?.propertyId?.phone
                                        ? `${selectedReservation.propertyId.phone.countryCode}${selectedReservation.propertyId.phone.phoneNumber}`
                                        : 'N/A'}
                                </li>
                                {/*<li>*/}
                                {/*    <span className="font-medium">Property Address:</span>{' '}*/}
                                {/*    {selectedReservation.propertyId.address.address1}*/}
                                {/*</li>*/}
                            </ul>
                        </div>
                        {/* Booker Info */}
                        <div className="mb-6">
                            <h3 className="text-base font-semibold text-gray-900 mb-2">Primary Guest Details</h3>
                            <ul className="space-y-1">
                                <li>
                                    <span className="font-medium">Name:</span>{' '}
                                    {selectedReservation.bookerDetails.firstName}{' '}
                                    {selectedReservation.bookerDetails.lastName}
                                </li>
                                <li>
                                    <span className="font-medium">Email:</span>{' '}
                                    {selectedReservation.bookerDetails.email}
                                </li>
                                <li>
                                    <span className="font-medium">Phone Number:</span>{' '}
                                    {selectedReservation?.bookerDetails?.phone
                                        ? `${selectedReservation.bookerDetails.phone.countryCode}${selectedReservation.bookerDetails.phone.phoneNumber}`
                                        : 'N/A'}
                                </li>
                                <li>
                                    <span className="font-medium">Number of Rooms:</span>{' '}
                                    {selectedReservation.reservations.length}
                                </li>
                                <li>
                                    <span className="font-medium">Number of Guests:</span>{' '}
                                    {selectedReservation?.reservations
                                        ? selectedReservation.reservations.reduce(
                                              (total, res) => total + (res.noOfAdults || 0) + (res.noOfChildren || 0),
                                              0
                                          )
                                        : 'N/A'}
                                </li>

                                <li>
                                    <span className="font-medium">Check-in Date & Time:</span>{' '}
                                    {selectedReservation?.reservations?.[0]?.startDateTime
                                        ? formatDateTime(new Date(selectedReservation.reservations[0].startDateTime))
                                        : 'N/A'}
                                </li>
                                <li>
                                    <span className="font-medium">Check-out Date & Time:</span>{' '}
                                    {selectedReservation?.reservations?.[0]?.endDateTime
                                        ? formatDateTime(new Date(selectedReservation.reservations[0].endDateTime))
                                        : 'N/A'}
                                </li>
                            </ul>
                        </div>

                        {/* Booking Info */}
                        <div className="mb-6">
                            <h3 className="text-base font-semibold text-gray-900 mb-2">Booking Summary</h3>
                            <ul className="space-y-1">
                                <li>
                                    <span className="font-medium">Booking ID:</span>{' '}
                                    {selectedReservation.reservationCode}
                                </li>
                                <li>
                                    <span className="font-medium">Booking Date & Time:</span>{' '}
                                    {formatDateTime(new Date(selectedReservation.createdAt))}
                                </li>
                                <li>
                                    <span className="font-medium">Room Type:</span>{' '}
                                    {selectedReservation.reservations[0]?.roomTypeId.name}
                                </li>

                                <li>
                                    <span className="font-medium">Tax Amount: </span>{' '}
                                    {selectedReservation?.reservations
                                        ? selectedReservation.reservations.reduce(
                                              (total, res) => total + (res.tax || 0),
                                              0
                                          )
                                            ? formatWithCurrency(
                                                  selectedReservation.reservations.reduce(
                                                      (total, res) => total + (res.tax || 0),
                                                      0
                                                  )
                                              )
                                            : 'N/A'
                                        : 'N/A'}
                                </li>

                                <li>
                                    <span className="font-medium">Total Price: </span>{' '}
                                    {selectedReservation?.reservations
                                        ? selectedReservation.reservations
                                              .reduce((total, res) => total + (res.totalAmount || 0), 0)
                                              .toFixed(2)
                                            ? formatWithCurrency(
                                                  selectedReservation.reservations.reduce(
                                                      (total, res) => total + (res.totalAmount || 0),
                                                      0
                                                  )
                                              )
                                            : 'N/A'
                                        : 'N/A'}
                                </li>
                                <li>
                                    <span className="font-medium">Booking Status:</span>{' '}
                                    {selectedReservation.paymentStatus}
                                </li>
                            </ul>
                        </div>
                    </div>
                ) : (
                    <div className="p-4 text-sm text-gray-500">No reservation selected.</div>
                )}
            </UiPopup>
        </div>
    );
}

export default LiveTracking;
