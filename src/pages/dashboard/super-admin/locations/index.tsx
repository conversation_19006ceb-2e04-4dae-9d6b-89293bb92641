import { useEffect, useState } from 'react';
import { PlusIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import LocationModal, { type ILocationForm } from './LocationModal';
import { useApiHook } from '../../../../hooks/useApi.ts';
import UiTable from '../../../../lib/UiTable.tsx';
import UiLoader from '../../../../lib/UiLoader.tsx';

function Locations() {
    const { get, delete: deleteApi } = useApiHook();
    const [locations, setLocations] = useState<ILocationForm[]>([]);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [editingLocation, setEditingLocation] = useState<ILocationForm | null>(null);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        fetchLocations();
    }, []);

    const fetchLocations = async () => {
        setLoading(true);
        try {
            const response = await get<ILocationForm[]>('/locations');
            setLocations(response);
        } catch (error) {
            console.error('Error fetching locations:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleCreate = () => {
        setEditingLocation(null);
        setIsModalOpen(true);
    };

    const handleEdit = (location: ILocationForm) => {
        setEditingLocation(location);
        setIsModalOpen(true);
    };

    const handleDelete = async (id: string) => {
        if (window.confirm('Are you sure you want to delete this location?')) {
            try {
                await deleteApi(`/locations/${id}`);
                fetchLocations();
            } catch (error) {
                console.error('Error deleting location:', error);
            }
        }
    };

    const handleModalClose = () => {
        setIsModalOpen(false);
        setEditingLocation(null);
    };

    const handleModalSuccess = () => {
        fetchLocations();
        handleModalClose();
    };

    const columns = [
        {
            header: 'Name',
            accessorKey: 'name',
            enableFiltering: false,
        },
        {
            header: 'City',
            accessorKey: 'city',
            enableFiltering: false,
        },
        {
            header: 'Country',
            accessorKey: 'country',
            enableFiltering: false,
        },
        {
            header: 'Latitude',
            accessorKey: 'latitude',
            enableFiltering: false,
        },
        {
            header: 'Longitude',
            accessorKey: 'longitude',
            enableFiltering: false,
        },
        {
            header: 'Actions',
            id: 'actions',
            enableFiltering: false,
            cell: ({ row }: { row: { original: ILocationForm } }) => (
                <div className="flex space-x-2">
                    <button onClick={() => handleEdit(row.original)} className="text-indigo-600 hover:text-indigo-900">
                        <PencilIcon className="h-4 w-4" />
                    </button>
                    <button onClick={() => handleDelete(row.original._id!)} className="text-red-600 hover:text-red-900">
                        <TrashIcon className="h-4 w-4" />
                    </button>
                </div>
            ),
        },
    ];

    if (loading) {
        return <UiLoader label={'Loading locations...'} />;
    }

    return (
        <div className="w-full p-6 bg-white rounded-lg shadow-sm">
            <div className="w-full flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold text-gray-900">Locations</h1>
                <button
                    onClick={handleCreate}
                    className="flex items-center px-4 py-2 theme-background text-white rounded-md hover:bg-red-700 transition-colors"
                >
                    <PlusIcon className="h-5 w-5 mr-2" />
                </button>
            </div>
            <UiTable
                columns={columns}
                data={locations}
                enableSorting={true}
                enableFiltering={false}
                enablePagination={true}
                enableGlobalFilter={false}
            />
            {locations.length === 0 && !loading && (
                <div className="text-center py-8 text-gray-500">No locations found.</div>
            )}
            {loading && (
                <div className="flex items-center justify-center h-64">
                    <div className="text-lg text-gray-600">Loading locations...</div>
                </div>
            )}
            {/* Modal */}
            <LocationModal
                isOpen={isModalOpen}
                onClose={handleModalClose}
                onSuccess={handleModalSuccess}
                location={editingLocation}
            />
        </div>
    );
}

export default Locations;
