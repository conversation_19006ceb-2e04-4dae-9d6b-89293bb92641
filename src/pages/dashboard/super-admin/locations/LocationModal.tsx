import React, { useState } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { useLoadScript } from '@react-google-maps/api';
import { useApiHook } from '../../../../hooks/useApi';
import UiMapInput from '../../../../lib/UiMapInput.tsx';
import UiInput from '../../../../lib/UiInput.tsx';
import { allowOnlyAlphabets, allowOnlyNumbers } from '../../../../utils/helpers/inputValidation.ts';
import { GOOGLE_MAPS_API_KEY } from '../../../../constants/env.ts';

export interface ILocationForm {
    _id?: string;
    name: string;
    latitude: number | '';
    longitude: number | '';
    country: string;
    code: string;
    city: string;
    description: string;
}

interface LocationModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSuccess: () => void;
    location: ILocationForm | null;
}

const validationSchema = Yup.object({
    name: Yup.string().required('Name is required'),
    latitude: Yup.number().typeError('Latitude must be a number').required('Latitude is required'),
    longitude: Yup.number().typeError('Longitude must be a number').required('Longitude is required'),
    country: Yup.string().required('Country is required'),
    code: Yup.string().required('Label is required'),
    city: Yup.string().required('City is required'),
    description: Yup.string(),
});

const LocationModal: React.FC<LocationModalProps> = ({ isOpen, onClose, onSuccess, location }) => {
    const { post, put } = useApiHook();
    const [isSubmitting, setIsSubmitting] = useState(false);

    const { isLoaded } = useLoadScript({
        googleMapsApiKey: GOOGLE_MAPS_API_KEY || '',
        libraries: ['places'],
    });

    const initialValues: ILocationForm = {
        name: location?.name || '',
        latitude: location?.latitude ?? '',
        longitude: location?.longitude ?? '',
        country: location?.country || '',
        code: location?.code || '',
        city: location?.city || '',
        description: location?.description || '',
        _id: location?._id,
    };

    const handleSubmit = async (values: ILocationForm) => {
        setIsSubmitting(true);
        try {
            if (location && location._id) {
                await put(`/locations/${location._id}`, values);
            } else {
                await post('/locations', values);
            }
            onSuccess();
        } catch (error) {
            console.error('Error saving location:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center">
            <div className="relative w-full max-w-2xl mx-auto p-6 rounded-2xl shadow-xl theme-background theme-border border-2">
                <div className="flex justify-between items-center mb-6">
                    <h3 className="text-lg font-bold theme-text">{location ? 'Edit Location' : 'Create Location'}</h3>
                    <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
                        <XMarkIcon className="h-6 w-6" />
                    </button>
                </div>
                <Formik
                    initialValues={initialValues}
                    validationSchema={validationSchema}
                    onSubmit={handleSubmit}
                    enableReinitialize
                >
                    {({ setFieldValue }) => (
                        <Form className="space-y-6">
                            {isLoaded && (
                                <UiMapInput
                                    onLocationSelect={location => {
                                        setFieldValue('name', location.address);
                                        setFieldValue('latitude', location.latitude);
                                        setFieldValue('longitude', location.longitude);
                                        setFieldValue('country', location.country);
                                        setFieldValue('code', location.address);
                                        setFieldValue('city', location.city);
                                    }}
                                />
                            )}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <UiInput label="Name" name="name" placeholder="Enter name" required />
                                <UiInput label="Label" name="code" placeholder="Enter label" required />
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <UiInput
                                    label="Latitude"
                                    name="latitude"
                                    type="number"
                                    placeholder="Enter latitude"
                                    onKeyDown={allowOnlyNumbers}
                                    required
                                />
                                <UiInput
                                    label="Longitude"
                                    name="longitude"
                                    type="number"
                                    onKeyDown={allowOnlyNumbers}
                                    placeholder="Enter longitude"
                                    required
                                />
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <UiInput
                                    label="Country"
                                    name="country"
                                    placeholder="Enter country"
                                    onKeyDown={allowOnlyAlphabets}
                                    required
                                />
                                <UiInput
                                    label="City"
                                    name="city"
                                    placeholder="Enter city"
                                    onKeyDown={allowOnlyAlphabets}
                                    required
                                />
                            </div>

                            <UiInput
                                label="Description"
                                name="description"
                                placeholder="Enter description"
                                type="text"
                                bottomSpacing={false}
                            />

                            <div className="flex justify-end space-x-3 pt-4">
                                <button
                                    type="button"
                                    onClick={onClose}
                                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                                >
                                    Cancel
                                </button>
                                <button
                                    type="submit"
                                    disabled={isSubmitting}
                                    className="px-4 py-2 text-sm font-semibold theme-background  rounded-md shadow hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                                >
                                    {isSubmitting ? 'Saving...' : location ? 'Update' : 'Create'}
                                </button>
                            </div>
                        </Form>
                    )}
                </Formik>
            </div>
        </div>
    );
};

export default LocationModal;
