import { useEffect, useState } from 'react';
import { PencilIcon, CheckCircleIcon, XCircleIcon, CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';
import type { ICompany } from '../../../../interfaces/ICompany';
import { useApiHook } from '../../../../hooks/useApi.ts';
import { useNavigate } from 'react-router';
import ConfirmationDialog from '../../../../components/common/ConfirmationDialog.tsx';
import UiTable from '../../../../lib/UiTable.tsx';
import UiLoader from '../../../../lib/UiLoader.tsx';

function MerchantManagement() {
    const [merchants, setMerchants] = useState<ICompany[]>([]);
    const { get, loading, patch, delete: deleteApi } = useApiHook();
    const navigate = useNavigate();
    const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState(false);
    const [selectedProperty, setSelectedProperty] = useState<ICompany | null>(null);
    const [actionType, setActionType] = useState<'approve' | 'reject' | 'activate' | 'deactivate' | 'delete'>(
        'approve'
    );

    useEffect(() => {
        const fetchMerchants = async () => {
            try {
                const response = await get<ICompany[]>('/properties');
                setMerchants(response || []);
            } catch (err) {
                console.error('Error fetching merchants:', err);
            }
        };
        fetchMerchants();
    }, []);

    // const handleAddProperty = () => {
    //     navigate('/onboarding');
    // };
    //
    // const handleViewProperty = (propertyId: string) => {
    //     navigate(`/dashboard/property/${propertyId}`);
    // };

    const handleEdit = (property: ICompany) => {
        // Navigate to edit page or open edit modal
        navigate(`/onboarding/${property._id}/property-information`);
    };

    const updateStatus = async (id: string, status: string, active?: boolean) => {
        try {
            const updateData = active !== undefined ? { status, active } : { status };
            await patch(`/properties/${id}/status`, updateData);
            // Refresh the list
            const response = await get<ICompany[]>('/properties');
            setMerchants(response || []);
        } catch (error) {
            console.error('Error updating property status:', error);
        }
    };

    const handleDelete = async (id: string) => {
        try {
            await deleteApi(`/properties/${id}`);
            // Refresh the list
            const response = await get<ICompany[]>('/properties');
            setMerchants(response || []);
        } catch (error) {
            console.error('Error deleting property:', error);
        }
    };

    const openConfirmationDialog = (
        property: ICompany,
        action: 'approve' | 'reject' | 'activate' | 'deactivate' | 'delete'
    ) => {
        setSelectedProperty(property);
        setActionType(action);
        setIsConfirmationDialogOpen(true);
    };

    // Define columns for the properties table
    const columns = [
        {
            accessorKey: 'name',
            header: 'Property Name',
            enableSorting: true,
        },
        {
            accessorKey: 'premisesType',
            header: 'Location Type',
            enableSorting: true,
        },
        {
            accessorKey: 'address.city',
            header: 'City',
            enableSorting: true,
        },
        {
            accessorKey: 'address.country',
            header: 'Country',
            enableSorting: true,
        },
        {
            accessorKey: 'status',
            header: 'Approval Status',
            enableSorting: true,
            cell: ({ row }: { row: { original: ICompany } }) => {
                const status = row.original.status || 'Pending';
                return (
                    <span
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                            status === 'Approved'
                                ? 'bg-green-100 text-green-800'
                                : status === 'Rejected'
                                  ? 'bg-red-100 text-red-800'
                                  : 'bg-yellow-100 text-yellow-800'
                        }`}
                    >
                        {status}
                    </span>
                );
            },
        },
        {
            accessorKey: 'active',
            header: 'Active Status',
            enableSorting: true,
            cell: ({ row }: { row: { original: ICompany } }) => {
                const isActive = row.original.active;
                const status = row.original.status || 'Pending';

                // Only show active status for approved properties
                if (status !== 'Approved') {
                    return <span className="text-gray-400 text-xs">N/A</span>;
                }

                return (
                    <span
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                            isActive ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                        }`}
                    >
                        {isActive ? 'Active' : 'Inactive'}
                    </span>
                );
            },
        },
        {
            accessorKey: 'actions',
            header: 'Actions',
            enableSorting: false,
            enableFiltering: false,
            cell: ({ row }: { row: { original: ICompany } }) => {
                const property = row.original;
                const status = property.status || 'Pending';

                return (
                    <div className="flex space-x-2">
                        <button
                            onClick={() => handleEdit(property)}
                            className="text-indigo-600 hover:text-indigo-900"
                            title="Edit"
                        >
                            <PencilIcon className="h-4 w-4" />
                        </button>

                        {/* Show approve/reject buttons for pending status only */}
                        {status === 'Pending' && (
                            <>
                                {property.customFields?.businessDetails && (
                                    <button
                                        onClick={() => openConfirmationDialog(property, 'approve')}
                                        className="text-green-600 hover:text-green-900"
                                        title="Approve"
                                    >
                                        <CheckIcon className="h-4 w-4" />
                                    </button>
                                )}
                                <button
                                    onClick={() => openConfirmationDialog(property, 'reject')}
                                    className="text-red-600 hover:text-red-900"
                                    title="Reject"
                                >
                                    <XMarkIcon className="h-4 w-4" />
                                </button>
                            </>
                        )}

                        {/* Show activate/deactivate buttons for approved status only */}
                        {status === 'Approved' && (
                            <>
                                {property.active ? (
                                    <button
                                        onClick={() => openConfirmationDialog(property, 'deactivate')}
                                        className="text-orange-600 hover:text-orange-900"
                                        title="Deactivate"
                                    >
                                        <XCircleIcon className="h-4 w-4" />
                                    </button>
                                ) : (
                                    <button
                                        onClick={() => openConfirmationDialog(property, 'activate')}
                                        className="text-green-600 hover:text-green-900"
                                        title="Activate"
                                    >
                                        <CheckCircleIcon className="h-4 w-4" />
                                    </button>
                                )}
                            </>
                        )}

                        {/* No actions for rejected status - one-time decision */}
                    </div>
                );
            },
        },
    ];

    const handleConfirmAction = async () => {
        if (selectedProperty) {
            try {
                if (actionType === 'approve') {
                    await updateStatus(selectedProperty._id, 'Approved', false); // Approved but not active by default
                } else if (actionType === 'reject') {
                    await updateStatus(selectedProperty._id, 'Rejected');
                } else if (actionType === 'activate') {
                    await updateStatus(selectedProperty._id, 'Approved', true);
                } else if (actionType === 'deactivate') {
                    await updateStatus(selectedProperty._id, 'Approved', false);
                } else if (actionType === 'delete') {
                    await handleDelete(selectedProperty._id);
                }
                setIsConfirmationDialogOpen(false);
                setSelectedProperty(null);
            } catch (error) {
                console.error('Error confirming action:', error);
            }
        }
    };

    const getConfirmationDialogProps = () => {
        if (selectedProperty) {
            switch (actionType) {
                case 'approve':
                    return {
                        title: 'Approve Property',
                        description: `Are you sure you want to approve "${selectedProperty.name}"? This is a one-time action and cannot be undone. The property will be approved for listing.`,
                        confirmText: 'Approve',
                        cancelText: 'Cancel',
                        confirmButtonClassName: 'bg-green-600 hover:bg-green-700 focus:ring-green-500',
                    };
                case 'reject':
                    return {
                        title: 'Reject Property',
                        description: `Are you sure you want to reject "${selectedProperty.name}"? This is a one-time action and cannot be undone. The property application will be permanently rejected.`,
                        confirmText: 'Reject',
                        cancelText: 'Cancel',
                        confirmButtonClassName: 'bg-red-600 hover:bg-red-700 focus:ring-red-500',
                    };
                case 'activate':
                    return {
                        title: 'Activate Property',
                        description: `Are you sure you want to activate "${selectedProperty.name}"? This will make the property visible to users.`,
                        confirmText: 'Activate',
                        cancelText: 'Cancel',
                        confirmButtonClassName: 'bg-green-600 hover:bg-green-700 focus:ring-green-500',
                    };
                case 'deactivate':
                    return {
                        title: 'Deactivate Property',
                        description: `Are you sure you want to deactivate "${selectedProperty.name}"? This will hide the property from users.`,
                        confirmText: 'Deactivate',
                        cancelText: 'Cancel',
                        confirmButtonClassName: 'bg-orange-600 hover:bg-orange-700 focus:ring-orange-500',
                    };
                case 'delete':
                    return {
                        title: 'Delete Property',
                        description: `Are you sure you want to delete "${selectedProperty.name}"? This action cannot be undone and will permanently remove all associated data.`,
                        confirmText: 'Delete',
                        cancelText: 'Cancel',
                        confirmButtonClassName: 'bg-red-600 hover:bg-red-700 focus:ring-red-500',
                    };
                default:
                    return {};
            }
        }
        return {};
    };

    if (loading) {
        return <UiLoader label="Loading merchants..." />;
    }

    return (
        <>
            <ConfirmationDialog
                isOpen={isConfirmationDialogOpen}
                onClose={() => {
                    setIsConfirmationDialogOpen(false);
                    setSelectedProperty(null);
                }}
                onConfirm={handleConfirmAction}
                isLoading={loading}
                {...getConfirmationDialogProps()}
            />
            <div className="p-6">
                <UiTable
                    columns={columns}
                    data={merchants}
                    enableSorting={true}
                    enableFiltering={false}
                    enablePagination={true}
                    defaultPageSize={10}
                    pageSizeOptions={[5, 10, 20, 50]}
                    className=""
                />
            </div>
        </>
    );
}

export default MerchantManagement;
