import { useEffect, useState } from 'react';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import { PlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import { useApiHook } from '../../../../../hooks/useApi.ts';
import { useToastHook } from '../../../../../hooks/useToaster.ts';
import { useNavigate, useSearchParams } from 'react-router';
import type { IDomainValue } from '../../../../../interfaces/IDomainValue.ts';
import { domainValue } from '../../../../../utils/constants/domainValues.ts';
import UiSelect from '../../../../../lib/UiSelect.tsx';
import UiInput from '../../../../../lib/UiInput.tsx';
import UiButton from '../../../../../lib/UiButton.tsx';
import useBoundStore from '../../../../../store/useBoundStore.ts';

interface PolicyOption {
    value: string;
    description: string;
}

interface PolicyFormValues {
    name: string;
    description: string;
    category: string;
    serviceType: string;
    // order: number;
    input_type: 'text' | 'radio' | 'checkbox' | 'select' | 'custom_rule';
    options: PolicyOption[];
    is_required: boolean;
    validation_rules: {
        min?: number;
        max?: number;
        pattern?: string;
    };
    config: {
        rules?: Array<{
            refund_percent: number;
            before_hours: number;
            after_hours?: number;
        }>;
    };
}

const PolicySetup = () => {
    const { post, get, put } = useApiHook();
    const { showSuccess, showError } = useToastHook();
    const { selectedProperty } = useBoundStore();
    const propertyId = selectedProperty._id;
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const policyId = searchParams.get('id');

    const [policyCategories, setPolicyCategories] = useState<IDomainValue[]>([]);
    const [serviceTypes, setServiceTypes] = useState<IDomainValue[]>([]);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isEditMode, setIsEditMode] = useState(false);
    const [initialFormValues, setInitialFormValues] = useState<PolicyFormValues | null>(null);

    const validationSchema = Yup.object().shape({
        name: Yup.string().required('Policy name is required').max(100, 'Name must be less than 100 characters'),
        serviceType: Yup.string().required('Service Type is required'),
        description: Yup.string().max(500, 'Description must be less than 500 characters'),
        category: Yup.string()
            .required('Policy Category is required')
            .max(100, 'Category must be less than 100 characters'),
        input_type: Yup.string()
            .oneOf(['text', 'radio', 'checkbox', 'select', 'custom_rule'], 'Invalid input type')
            .required('Input type is required'),
        options: Yup.array().when('input_type', {
            is: (type: string) => ['radio', 'checkbox', 'select'].includes(type),
            then: schema =>
                schema.min(1, 'At least one option is required').of(
                    Yup.object().shape({
                        value: Yup.string().required('Option value cannot be empty'),
                        description: Yup.string().max(200, 'Description too long'),
                    })
                ),
            otherwise: schema => schema.notRequired(),
        }),
        is_required: Yup.boolean().required(),
        validation_rules: Yup.object().shape({
            min: Yup.number().min(0, 'Minimum must be 0 or greater'),
            max: Yup.number().min(0, 'Maximum must be 0 or greater'),
            pattern: Yup.string(),
        }),
        config: Yup.object().when('input_type', {
            is: 'custom_rule',
            then: schema =>
                schema.shape({
                    rules: Yup.array()
                        .min(1, 'At least one rule is required')
                        .of(
                            Yup.object().shape({
                                refund_percent: Yup.number().min(0).max(100).required('Refund percentage is required'),
                                before_hours: Yup.number().min(0).required('Before hours is required'),
                                after_hours: Yup.number().min(0),
                            })
                        ),
                }),
            otherwise: schema => schema.notRequired(),
        }),
    });

    const initialValues: PolicyFormValues = {
        name: '',
        description: '',
        category: '',
        serviceType: '',
        // order: 0,
        input_type: 'text',
        options: [{ value: '', description: '' }],
        is_required: false,
        validation_rules: {},
        config: {
            rules: [
                {
                    refund_percent: 100,
                    before_hours: 48,
                },
            ],
        },
    };

    useEffect(() => {
        if (policyId) {
            setIsEditMode(true);
            fetchPolicyById(policyId);
        }
    }, [policyId]);

    useEffect(() => {
        fetchPolicyCategories();
        fetchServiceTypes();
    }, []);

    const fetchPolicyCategories = async () => {
        const response = await get<IDomainValue[]>(`/domain-values/categoryId/${domainValue.PolicyCategories}`);
        setPolicyCategories(response);
    };

    const fetchServiceTypes = async () => {
        const response = await get<IDomainValue[]>(`/domain-values/categoryId/${domainValue.ServicesCategories}`);
        setServiceTypes(response);
    };

    const fetchPolicyById = async (id: string) => {
        try {
            const response = await get(`/policy/${id}`);
            // @ts-expect-error type mismatch, but we handle it later
            if (response?.policy) {
                // @ts-expect-error type mismatch, but we handle it later
                const policy = response.policy;
                // Backward compatibility: convert string[] to PolicyOption[]
                let options: PolicyOption[] = [{ value: '', description: '' }];
                if (Array.isArray(policy.options) && policy.options.length > 0) {
                    if (typeof policy.options[0] === 'string') {
                        options = policy.options.map((v: string) => ({ value: v, description: '' }));
                    } else {
                        options = policy.options;
                    }
                }
                setInitialFormValues({
                    name: policy.name || '',
                    description: policy.description || '',
                    category: policy.category || '',
                    serviceType: policy.serviceType || '',
                    // order: policy.order || 0,
                    input_type: policy.input_type || 'text',
                    options,
                    is_required: policy.is_required || false,
                    validation_rules: policy.validation_rules || {},
                    config: policy.config || {
                        rules: [
                            {
                                refund_percent: 100,
                                before_hours: 48,
                            },
                        ],
                    },
                });
            }
        } catch (_error) {
            showError('Failed to fetch policy details');
            navigate(-1);
        }
    };

    const handleSubmit = async (values: PolicyFormValues) => {
        setIsSubmitting(true);
        try {
            const policyData = {
                ...values,
                // order: Number(values.order) || 0,
                options: values.options
                    .filter((option: PolicyOption) => option.value.trim() !== '')
                    .map((option: PolicyOption) => ({
                        value: option.value.trim(),
                        description: option.description.trim(),
                    })),
                propertyId: propertyId,
            };

            if (isEditMode && policyId) {
                await put(`/policy/${policyId}`, policyData);
                showSuccess('Policy updated successfully');
                navigate(-1);
            } else {
                await post('/policy', policyData);
                showSuccess('Policy created successfully');
                navigate(-1);
            }
        } catch (_error) {
            showError(isEditMode ? 'Failed to update policy' : 'Failed to create policy');
        } finally {
            setIsSubmitting(false);
        }
    };

    const inputTypeOptions = [
        { label: 'Text Input', value: 'text' },
        { label: 'Radio Buttons', value: 'radio' },
        { label: 'Checkbox', value: 'checkbox' },
        // { label: 'Dropdown Select', value: 'select' },
        // { label: 'Custom Rule', value: 'custom_rule' },
    ];

    return (
        <div className="w-full p-6 bg-white rounded-lg shadow-sm">
            <div className="flex justify-between items-center mb-6">
                <div className="flex items-center gap-4">
                    <h1 className="text-2xl font-bold text-gray-900">{isEditMode ? 'Edit Policy' : 'Policy Setup'}</h1>
                </div>
            </div>

            {/* Policy Creation/Edit Form */}
            <div className="mb-8 p-6 bg-gray-50 rounded-lg">
                <h2 className="text-lg font-semibold mb-4">{isEditMode ? 'Edit Policy' : 'Create New Policy'}</h2>
                <Formik
                    initialValues={initialFormValues || initialValues}
                    validationSchema={validationSchema}
                    onSubmit={handleSubmit}
                    enableReinitialize={true}
                >
                    {({ values, setFieldValue }) => (
                        <Form className="space-y-6">
                            {/* Basic Information */}
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <UiSelect
                                    label="Service Type"
                                    name="serviceType"
                                    required
                                    options={serviceTypes.map(category => ({
                                        value: category._id,
                                        label: category.name,
                                    }))}
                                />
                                <UiSelect
                                    label="Category"
                                    name="category"
                                    required
                                    options={policyCategories.map(category => ({
                                        value: category._id,
                                        label: category.name,
                                    }))}
                                />
                                <UiInput
                                    label="Policy Name"
                                    name="name"
                                    placeholder="e.g., Cancellation Policy"
                                    required
                                />
                                {/* <UiInput
                                    label="Order"
                                    name="order"
                                    placeholder="Order of the policy"
                                     onKeyDown={allowOnlyNumbers}
                                /> */}
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <UiSelect
                                    label="Input Type"
                                    name="input_type"
                                    options={inputTypeOptions}
                                    onChange={value => {
                                        setFieldValue('input_type', value[0] || 'text');
                                        // Reset options when changing input type
                                        if (!['radio', 'checkbox', 'select'].includes(value[0])) {
                                            setFieldValue('options', [{ value: '', description: '' }]);
                                        }
                                    }}
                                    required
                                />
                                <div className="flex items-center space-x-2">
                                    <input
                                        type="checkbox"
                                        id="is_required"
                                        checked={values.is_required}
                                        onChange={e => setFieldValue('is_required', e.target.checked)}
                                        className="h-4 w-4 text-blue-600"
                                    />
                                    <label htmlFor="is_required" className="text-sm font-medium text-gray-700">
                                        Required Field
                                    </label>
                                </div>
                            </div>

                            {values.input_type === 'text' && (
                                <UiInput
                                    label="Description"
                                    name="description"
                                    placeholder="Brief description of the policy"
                                />
                            )}

                            {/* Options for Radio/Checkbox/Select */}
                            {['radio', 'checkbox', 'select'].includes(values.input_type) && (
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Options</label>
                                    {values.options.map((_option, index) => (
                                        <div key={index} className="flex items-center gap-2 mb-2">
                                            <UiInput
                                                label=""
                                                name={`options.${index}.value`}
                                                placeholder={`Option Value ${index + 1}`}
                                                widthClassname="flex-1"
                                            />
                                            <UiInput
                                                label=""
                                                name={`options.${index}.description`}
                                                placeholder={`Description (optional)`}
                                                widthClassname="flex-1"
                                            />
                                            {values.options.length > 1 && (
                                                <button
                                                    type="button"
                                                    onClick={() => {
                                                        const newOptions = values.options.filter((_, i) => i !== index);
                                                        setFieldValue('options', newOptions);
                                                    }}
                                                    className="p-2 text-red-600 hover:text-red-800"
                                                >
                                                    <TrashIcon className="h-4 w-4" />
                                                </button>
                                            )}
                                        </div>
                                    ))}
                                    <button
                                        type="button"
                                        onClick={() =>
                                            setFieldValue('options', [
                                                ...values.options,
                                                {
                                                    value: '',
                                                    description: '',
                                                },
                                            ])
                                        }
                                        className="flex items-center gap-2 text-blue-600 hover:text-blue-800 text-sm"
                                    >
                                        <PlusIcon className="h-4 w-4" />
                                        Add Option
                                    </button>
                                </div>
                            )}

                            {/* Custom Rules Configuration */}
                            {values.input_type === 'custom_rule' && (
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Cancellation Rules
                                    </label>
                                    {values.config.rules?.map((_rule, index) => (
                                        <div
                                            key={index}
                                            className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-white rounded border mb-4"
                                        >
                                            <UiInput
                                                label="Refund %"
                                                name={`config.rules.${index}.refund_percent`}
                                                type="number"
                                                required
                                            />
                                            <UiInput
                                                label="Before Hours"
                                                name={`config.rules.${index}.before_hours`}
                                                type="number"
                                                required
                                            />
                                            <UiInput
                                                label="After Hours (Optional)"
                                                name={`config.rules.${index}.after_hours`}
                                                type="number"
                                            />
                                            {values.config.rules && values.config.rules.length > 1 && (
                                                <button
                                                    type="button"
                                                    onClick={() => {
                                                        const newRules = values.config.rules?.filter(
                                                            (_, i) => i !== index
                                                        );
                                                        setFieldValue('config.rules', newRules);
                                                    }}
                                                    className="p-2 text-red-600 hover:text-red-800"
                                                >
                                                    <TrashIcon className="h-4 w-4" />
                                                </button>
                                            )}
                                        </div>
                                    ))}
                                    <button
                                        type="button"
                                        onClick={() => {
                                            const newRules = [
                                                ...(values.config.rules || []),
                                                {
                                                    refund_percent: 0,
                                                    before_hours: 0,
                                                },
                                            ];
                                            setFieldValue('config.rules', newRules);
                                        }}
                                        className="flex items-center gap-2 text-blue-600 hover:text-blue-800 text-sm"
                                    >
                                        <PlusIcon className="h-4 w-4" />
                                        Add Rule
                                    </button>
                                </div>
                            )}

                            {/* Validation Rules */}
                            <div>
                                <h3 className="text-md font-medium text-gray-700 mb-2">Validation Rules (Optional)</h3>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <UiInput label="Minimum Value" name="validation_rules.min" type="number" />
                                    <UiInput label="Maximum Value" name="validation_rules.max" type="number" />
                                    <UiInput
                                        label="Pattern (Regex)"
                                        name="validation_rules.pattern"
                                        placeholder="e.g., ^[A-Za-z]+$"
                                    />
                                </div>
                            </div>

                            {/* Submit Buttons */}
                            <div className="flex gap-4">
                                <UiButton type="submit" disabled={isSubmitting} className="flex items-center gap-2">
                                    {isSubmitting
                                        ? isEditMode
                                            ? 'Updating...'
                                            : 'Creating...'
                                        : isEditMode
                                          ? 'Update Policy'
                                          : 'Create Policy'}
                                </UiButton>
                                <UiButton
                                    type="button"
                                    isTheme={false}
                                    onClick={() => navigate(-1)}
                                    className="flex items-center gap-2 bg-gray-600"
                                >
                                    Back to Policies
                                </UiButton>
                            </div>
                        </Form>
                    )}
                </Formik>
            </div>
        </div>
    );
};

export default PolicySetup;
