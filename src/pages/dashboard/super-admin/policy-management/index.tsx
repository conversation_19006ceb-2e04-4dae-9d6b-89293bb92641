import { useEffect, useState } from 'react';
import { PlusIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import { useApiHook } from '../../../../hooks/useApi.ts';
import { useToastHook } from '../../../../hooks/useToaster.ts';
import { useNavigate } from 'react-router';
import ConfirmationDialog from '../../../../components/common/ConfirmationDialog.tsx';
import UiButton from '../../../../lib/UiButton.tsx';
import UiTable from '../../../../lib/UiTable.tsx';
import UiLoader from '../../../../lib/UiLoader.tsx';

interface Policy {
    _id: string;
    name: string;
    description?: string;
    input_type: 'text' | 'radio' | 'checkbox' | 'select' | 'custom_rule';
    is_required: boolean;
    options?: string[];
    createdAt: string;
    updatedAt: string;
}

function PolicyList() {
    const [policies, setPolicies] = useState<Policy[]>([]);
    const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState(false);
    const [selectedPolicy, setSelectedPolicy] = useState<Policy | null>(null);
    const { get, delete: deleteApi, loading } = useApiHook();
    const { showSuccess, showError } = useToastHook();
    const navigate = useNavigate();

    useEffect(() => {
        fetchPolicies();
    }, []);

    const fetchPolicies = async () => {
        try {
            const response = await get<{ policies: Policy[] }>('/policy');
            if (response?.policies) {
                setPolicies(response.policies);
            } else if (Array.isArray(response)) {
                setPolicies(response);
            }
        } catch (_error) {
            showError('Failed to fetch policies');
        }
    };

    const handleEdit = (policy: Policy) => {
        navigate(`policy-setup?id=${policy._id}`);
    };

    const handleDelete = async (policy: Policy) => {
        setSelectedPolicy(policy);
        setIsConfirmationDialogOpen(true);
    };

    const confirmDelete = async () => {
        if (selectedPolicy) {
            try {
                await deleteApi(`/policy/${selectedPolicy._id}`);
                showSuccess('Policy deleted successfully');
                fetchPolicies();
            } catch (_error) {
                showError('Failed to delete policy');
            } finally {
                setIsConfirmationDialogOpen(false);
                setSelectedPolicy(null);
            }
        }
    };

    const handleCreateNew = () => {
        navigate('policy-setup');
    };

    // Define columns for the policies table
    const columns = [
        {
            accessorKey: 'name',
            header: 'Policy Name',
            enableSorting: true,
            enableFiltering: false,
        },
        {
            accessorKey: 'category.name',
            header: 'Category',
            enableSorting: true,
            enableFiltering: false,
        },
        // {
        //     accessorKey: 'description',
        //     header: 'Description',
        //     enableSorting: true,
        //     enableFiltering: false,
        //     cell: ({ row }: { row: { original: Policy } }) => {
        //         const description = row.original.description;
        //         return description ? (
        //             <span className="text-sm text-gray-600">
        //                 {description.length > 50 ? `${description.substring(0, 50)}...` : description}
        //             </span>
        //         ) : (
        //             <span className="text-gray-400 text-sm">No description</span>
        //         );
        //     },
        // },
        {
            accessorKey: 'input_type',
            header: 'Input Type',
            enableSorting: true,
            enableFiltering: false,
            cell: ({ row }: { row: { original: Policy } }) => {
                const inputType = row.original.input_type;
                const typeLabels: Record<string, string> = {
                    text: 'Text Input',
                    radio: 'Radio Buttons',
                    checkbox: 'Checkbox',
                    select: 'Dropdown Select',
                    custom_rule: 'Custom Rule',
                };
                return (
                    <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        {typeLabels[inputType] || inputType}
                    </span>
                );
            },
        },
        {
            accessorKey: 'is_required',
            header: 'Required',
            enableSorting: true,
            enableFiltering: false,
            cell: ({ row }: { row: { original: Policy } }) => {
                const isRequired = row.original.is_required;
                return (
                    <span
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                            isRequired ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}
                    >
                        {isRequired ? 'Yes' : 'No'}
                    </span>
                );
            },
        },
        // {
        //     accessorKey: 'options',
        //     header: 'Options',
        //     enableSorting: false,
        //     enableFiltering: false,
        //     cell: ({ row }: { row: { original: Policy } }) => {
        //         const options = row.original.options;
        //         const inputType = row.original.input_type;

        //         if (!['radio', 'checkbox', 'select'].includes(inputType)) {
        //             return <span className="text-gray-400 text-sm">N/A</span>;
        //         }

        //         if (!options || options.length === 0) {
        //             return <span className="text-red-400 text-sm">No options</span>;
        //         }

        //         return (
        //             <span className="text-sm text-gray-600">
        //                 {options.length} option{options.length !== 1 ? 's' : ''}
        //             </span>
        //         );
        //     },
        // },
        // {
        //     accessorKey: 'createdAt',
        //     header: 'Created',
        //     enableSorting: true,
        //     enableFiltering: false,
        //     cell: ({ row }: { row: { original: Policy } }) => {
        //         const date = new Date(row.original.createdAt);
        //         return <span className="text-sm text-gray-600">{date.toLocaleDateString()}</span>;
        //     },
        // },
        {
            accessorKey: 'actions',
            header: 'Actions',
            enableSorting: false,
            enableFiltering: false,
            enableColumnActions: false,
            cell: ({ row }: { row: { original: Policy } }) => {
                const policy = row.original;
                return (
                    <div className="flex space-x-2">
                        <button
                            onClick={() => handleEdit(policy)}
                            className="text-indigo-600 hover:text-indigo-900"
                            title="Edit Policy"
                        >
                            <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                            onClick={() => handleDelete(policy)}
                            className="text-red-600 hover:text-red-900"
                            title="Delete Policy"
                        >
                            <TrashIcon className="h-4 w-4" />
                        </button>
                    </div>
                );
            },
        },
    ];

    if (loading) {
        return <UiLoader label="Loading policies..." />;
    }

    return (
        <>
            <ConfirmationDialog
                isOpen={isConfirmationDialogOpen}
                onClose={() => {
                    setIsConfirmationDialogOpen(false);
                    setSelectedPolicy(null);
                }}
                onConfirm={confirmDelete}
                isLoading={loading}
                title="Delete Policy"
                description={
                    selectedPolicy
                        ? `Are you sure you want to delete "${selectedPolicy.name}"? This action cannot be undone.`
                        : ''
                }
                confirmText="Delete"
                cancelText="Cancel"
                confirmButtonClassName="bg-red-600 hover:bg-red-700 focus:ring-red-500"
            />

            <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-bold text-gray-900">Policy Management</h1>
                    <UiButton onClick={handleCreateNew} className="flex items-center gap-2">
                        <PlusIcon className="h-5 w-5" />
                    </UiButton>
                </div>

                <UiTable
                    columns={columns}
                    data={policies}
                    enableSorting={true}
                    enableFiltering={false}
                    enablePagination={true}
                    defaultPageSize={10}
                    pageSizeOptions={[5, 10, 20, 50]}
                    className=""
                />
            </div>
        </>
    );
}

export default PolicyList;
