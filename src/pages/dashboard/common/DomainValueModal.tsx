import React, { useEffect, useState, useMemo } from 'react';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { useApiHook } from '../../../hooks/useApi';
import { useToastHook } from '../../../hooks/useToaster.ts';
import type { IDomainValue } from '../../../interfaces/IDomainValue.ts';
import UiLoader from '../../../lib/UiLoader.tsx';
import UiSelect from '../../../lib/UiSelect.tsx';

interface DomainValueModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSuccess: () => void;
    domainValue: IDomainValue | null;
    type: string;
    categoryId?: string;
    propertyId: string | null;
    parentId?: string | null;
}

const DomainValueModal: React.FC<DomainValueModalProps> = ({
    isOpen,
    onClose,
    onSuccess,
    domainValue,
    categoryId,
    parentId,
    propertyId,
}) => {
    const { post, put, get, error, loading } = useApiHook();
    const { showError } = useToastHook();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [parentDomainValues, setParentDomainValues] = useState<IDomainValue[] | []>([]);

    const initialValues = {
        propertyId: propertyId || 'Elaachi',
        category: domainValue?.category || '',
        level: domainValue?.level || (propertyId !== 'Elaachi' ? 'PROPERTY_DATA' : 'SYSTEM_DATA'),
        code: domainValue?.code || '',
        name: domainValue?.name || '',
        description: domainValue?.description || '',
        displayOrder: domainValue?.displayOrder || 1,
        parentId: domainValue?.parentId || '',
        value: domainValue?.value || '',
    };

    const validationSchema = useMemo(() => {
        const baseSchema = {
            code: Yup.string().required('Code is required'),
            name: Yup.string().required('Name is required'),
            description: Yup.string(),
            category: Yup.string(),
            displayOrder: Yup.number(),
            level: Yup.string().required('Level is required'),
        };

        if (parentId) {
            return Yup.object({
                ...baseSchema,
                parentId: Yup.string().required('Parent is required'),
            });
        }

        return Yup.object(baseSchema);
    }, [parentId]);

    const handleSubmit = async (values: typeof initialValues) => {
        setIsSubmitting(true);
        try {
            const payload = {
                ...values,
                categoryId: domainValue?.categoryId || categoryId || '',
            };

            if (domainValue) {
                await put(`/domain-values/${domainValue._id}`, payload);
            } else {
                await post('/domain-values', payload);
            }

            onSuccess();
        } catch (err) {
            const e = err as { response?: { data?: { error: string } } };
            showError(
                error || e.response?.data?.error || 'An error occurred create domain value. Please try again later.'
            );
        } finally {
            setIsSubmitting(false);
        }
    };

    useEffect(() => {
        if (parentId && isOpen) {
            const fetchParentDomainValue = async () => {
                try {
                    const response = await get<IDomainValue[]>(`/domain-values?categoryId=${parentId}`);
                    setParentDomainValues(response);
                } catch (err) {
                    console.error('Error fetching parent domain values:', err);
                    setParentDomainValues([]);
                }
            };
            fetchParentDomainValue();
        } else {
            setParentDomainValues([]);
        }
    }, [parentId, isOpen, get]);

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-[#0005] overflow-y-auto h-full w-full z-50">
            {loading && <UiLoader label={'Saving...'} />}
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium text-gray-900 theme-text">
                        {domainValue ? 'Edit Domain Value' : 'Create Domain Value'}
                    </h3>
                    <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
                        <XMarkIcon className="h-6 w-6" />
                    </button>
                </div>

                <Formik
                    initialValues={initialValues}
                    validationSchema={validationSchema}
                    onSubmit={handleSubmit}
                    enableReinitialize
                >
                    {({ errors, touched }) => (
                        <Form className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Code *</label>
                                <Field
                                    name="code"
                                    type="text"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                    placeholder="Enter code"
                                />
                                {errors.code && touched.code && (
                                    <p className="mt-1 text-sm text-red-600">{errors.code}</p>
                                )}
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Name *</label>
                                <Field
                                    name="name"
                                    type="text"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                    placeholder="Enter name"
                                />
                                {errors.name && touched.name && (
                                    <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                                )}
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                <Field
                                    name="description"
                                    as="textarea"
                                    rows={3}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                    placeholder="Enter description"
                                />
                                {errors.description && touched.description && (
                                    <p className="mt-1 text-sm text-red-600">{errors.description}</p>
                                )}
                            </div>
                            {/*
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Category
                                </label>
                                <Field
                                    name="category"
                                    type="text"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                    placeholder="Enter category"
                                />
                                {errors.category && touched.category && (
                                    <p className="mt-1 text-sm text-red-600">{errors.category}</p>
                                )}
                            </div> */}

                            {parentDomainValues.length > 0 && (
                                <UiSelect
                                    name="parentId"
                                    label="Parent"
                                    required
                                    options={parentDomainValues.map(value => ({
                                        label: value.name,
                                        value: value._id,
                                    }))}
                                />
                            )}

                            {/* <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Display Order *
                                </label>
                                <Field
                                    name="displayOrder"
                                    type="number"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                    placeholder="Enter display order"
                                />
                                {errors.displayOrder && touched.displayOrder && (
                                    <p className="mt-1 text-sm text-red-600">{errors.displayOrder}</p>
                                )}
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Level *
                                </label>
                                <Field
                                    name="level"
                                    type="text"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                    placeholder="Enter level"
                                />
                                {errors.level && touched.level && (
                                    <p className="mt-1 text-sm text-red-600">{errors.level}</p>
                                )}
                            </div> */}

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Value</label>
                                <Field
                                    name="value"
                                    type="text"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                    placeholder="Enter value"
                                />
                            </div>

                            <div className="flex justify-end space-x-3 pt-4">
                                <button
                                    type="button"
                                    onClick={onClose}
                                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                                >
                                    Cancel
                                </button>
                                <button
                                    type="submit"
                                    disabled={isSubmitting || loading}
                                    className="px-4 py-2 text-sm font-medium text-white theme-background border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                                >
                                    {isSubmitting || loading ? 'Saving...' : domainValue ? 'Update' : 'Create'}
                                </button>
                            </div>
                        </Form>
                    )}
                </Formik>
            </div>
        </div>
    );
};

export default DomainValueModal;
