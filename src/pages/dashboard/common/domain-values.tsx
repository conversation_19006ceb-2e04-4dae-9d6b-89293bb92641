import { useCallback, useEffect, useState } from 'react';
import { PencilIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import DomainValueModal from './DomainValueModal';
import { useApiHook } from '../../../hooks/useApi.ts';
import { useToastHook } from '../../../hooks/useToaster.ts';
import type { IDomainValue } from '../../../interfaces/IDomainValue.ts';
import UiLoader from '../../../lib/UiLoader.tsx';

interface DomainValuesProps {
    propertyId: string | null;
    fetchMetaUrl: string;
    fetchDataUrl: string;
    deleteUrlPrefix: string;
    selectedDomainValue: IDomainValue | null;
    setSelectedDomainValue: (value: IDomainValue | null) => void;
}

function DomainValues({
    propertyId,
    fetchMetaUrl,
    fetchDataUrl,
    deleteUrlPrefix,
    selectedDomainValue,
    setSelectedDomainValue,
}: DomainValuesProps) {
    const { get, delete: deleteApi, loading } = useApiHook();
    const { showError, showSuccess } = useToastHook();

    const [metaList, setMetaList] = useState<IDomainValue[]>([]);
    const [dataList, setDataList] = useState<IDomainValue[]>([]);
    const [selectedCategory, setSelectedCategory] = useState<string>('');
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [editingDomainValue, setEditingDomainValue] = useState<IDomainValue | null>(null);

    useEffect(() => {
        setTimeout(() => {
            fetchMetaList();
        }, 1000);
    }, [fetchMetaUrl]);

    useEffect(() => {
        if (selectedDomainValue) {
            fetchDataList();
        } else {
            setDataList([]);
        }
    }, [selectedDomainValue, fetchDataUrl]);

    useEffect(() => {
        if (selectedCategory && metaList.length > 0) {
            const categoryDomainValues = metaList.filter(item => item.category === selectedCategory);
            if (categoryDomainValues.length > 0) {
                setSelectedDomainValue(categoryDomainValues[0]);
            } else {
                setSelectedDomainValue(null);
            }
        }
    }, [selectedCategory, metaList]);

    const fetchMetaList = useCallback(async () => {
        const response = await get<IDomainValue[]>(fetchMetaUrl);
        setMetaList(response || []);
        if (response && response.length > 0) {
            setSelectedCategory(response[0].category);
            if (!selectedDomainValue) {
                setSelectedDomainValue(response[0]);
            }
        }
    }, [fetchMetaUrl, get, showError]);

    const fetchDataList = useCallback(async () => {
        if (!selectedDomainValue) return;
        const response = await get<IDomainValue[]>(fetchDataUrl);
        setDataList(response || []);
    }, [fetchDataUrl, selectedDomainValue, get, showError]);

    const handleCreate = () => {
        setEditingDomainValue(null);
        setIsModalOpen(true);
    };

    const handleEdit = (domainValue: IDomainValue) => {
        setEditingDomainValue(domainValue);
        setIsModalOpen(true);
    };

    const handleDelete = async (id: string) => {
        if (window.confirm('Are you sure you want to delete this domain value?')) {
            try {
                await deleteApi(`${deleteUrlPrefix}/${id}`);
                showSuccess('Domain value deleted successfully');
                fetchDataList();
            } catch (error) {
                console.error('Error deleting domain value:', error);
                showError('Failed to delete domain value');
            }
        }
    };

    const handleModalClose = () => {
        setIsModalOpen(false);
        setEditingDomainValue(null);
    };

    const handleModalSuccess = () => {
        fetchDataList();
        handleModalClose();
        showSuccess('Domain value saved successfully');
    };

    if (loading) {
        return <UiLoader label={'Loading domain values...'} />;
    }

    return (
        <div className="w-full p-6 bg-white rounded-lg shadow-sm">
            <div className="w-full flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold text-gray-900">Domain Values</h1>
                <button
                    onClick={handleCreate}
                    className="flex items-center px-4 py-2 theme-background text-white rounded-md hover:bg-red-700 transition-colors"
                >
                    <PlusIcon className="h-5 w-5" />
                </button>
            </div>

            {/* Category Tabs */}
            {metaList.length > 0 && (
                <div className="w-full border-b border-gray-200 mb-6">
                    <nav className="-mb-px flex space-x-8">
                        {Array.from(new Set(metaList.map(item => item.category))).map(category => (
                            <button
                                key={category}
                                onClick={() => setSelectedCategory(category)}
                                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                    selectedCategory === category
                                        ? 'border-red-500 theme-text'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                {category}
                            </button>
                        ))}
                    </nav>
                </div>
            )}

            {/* Domain Values Tabs */}
            {selectedCategory && (
                <div className="w-full border-b border-gray-200 mb-6">
                    <nav className="-mb-px flex space-x-8">
                        {metaList
                            .filter(item => item.category === selectedCategory)
                            .map(domainValue => (
                                <button
                                    key={domainValue._id}
                                    onClick={() => setSelectedDomainValue(domainValue)}
                                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                        selectedDomainValue === domainValue
                                            ? 'border-red-500 theme-text'
                                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                    }`}
                                >
                                    {domainValue.name}
                                </button>
                            ))}
                    </nav>
                </div>
            )}

            {/* Domain Values Table */}
            {selectedDomainValue && (
                <div className="overflow-x-auto">
                    <div className="flex justify-between items-center mb-4">
                        <h2 className="text-lg font-semibold text-gray-900">{selectedDomainValue.name} Values</h2>
                    </div>

                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Name
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {dataList
                                .filter(item => item.categoryId === selectedDomainValue._id)
                                .map(domainValue => (
                                    <tr key={domainValue._id} className="hover:bg-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {domainValue.name}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div className="flex space-x-2">
                                                <button
                                                    onClick={() => handleEdit(domainValue)}
                                                    className="text-indigo-600 hover:text-indigo-900"
                                                    title="Edit"
                                                >
                                                    <PencilIcon className="h-4 w-4" />
                                                </button>
                                                <button
                                                    onClick={() => handleDelete(domainValue._id)}
                                                    className="text-red-600 hover:text-red-900"
                                                    title="Delete"
                                                >
                                                    <TrashIcon className="h-4 w-4" />
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                        </tbody>
                    </table>
                </div>
            )}

            {selectedDomainValue && dataList.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                    No domain values found for {selectedDomainValue.name}
                </div>
            )}

            {metaList.length === 0 && <div className="text-center py-8 text-gray-500">No domain categories found</div>}

            <DomainValueModal
                isOpen={isModalOpen}
                onClose={handleModalClose}
                onSuccess={handleModalSuccess}
                domainValue={editingDomainValue}
                type={selectedDomainValue?.name || ''}
                categoryId={selectedDomainValue?._id}
                parentId={selectedDomainValue?.parentId || null}
                propertyId={propertyId}
            />
        </div>
    );
}

export default DomainValues;
