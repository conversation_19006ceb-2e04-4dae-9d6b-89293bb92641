import { useEffect, useState } from 'react';
import DatePicker from 'react-datepicker';
import useBoundStore from '../../../../store/useBoundStore.ts';
import { useApiHook } from '../../../../hooks/useApi.ts';
import { useToastHook } from '../../../../hooks/useToaster.ts';
import type { IPackage } from '../../../../interfaces/IPackage.ts';
import type { IRateCard, RateCardData } from '../../../../interfaces/IRateCard.ts';
import UiLoader from '../../../../lib/UiLoader.tsx';
import UiButton from '../../../../lib/UiButton.tsx';

const getDateArray = (start: Date, end: Date) => {
    const arr = [];
    const dt = new Date(start);
    while (dt <= end) {
        arr.push(new Date(dt));
        dt.setDate(dt.getDate() + 1);
    }
    return arr;
};

function RateCard() {
    const { selectedProperty } = useBoundStore();
    const { get, post } = useApiHook();
    const { showSuccess, showError } = useToastHook();
    const [packages, setPackages] = useState<IPackage[]>([]);
    const [loading, setLoading] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
        new Date(),
        new Date(Date.now() + 6 * 24 * 60 * 60 * 1000),
    ]);
    const [dates, setDates] = useState<Date[]>(
        getDateArray(new Date(), new Date(Date.now() + 6 * 24 * 60 * 60 * 1000))
    );
    const [rates, setRates] = useState<{ [packageId: string]: { [date: string]: string } }>({});
    const [existingRateCards, setExistingRateCards] = useState<IRateCard[]>([]);

    // Fetch packages and rates
    useEffect(() => {
        if (!selectedProperty?._id) return;
        setLoading(true);
        get<IPackage[]>(`properties/${selectedProperty._id}/packages`).then(pkgs => {
            setPackages(pkgs || []);
            setLoading(false);
        });
    }, [selectedProperty]);

    // Fetch rates when date range or packages change
    useEffect(() => {
        if (!selectedProperty?._id || packages.length === 0) return;

        const [start, end] = dateRange;
        if (start && end) {
            setLoading(true);
            setDates(getDateArray(start!, end!));
            get<IRateCard[]>(
                `properties/${selectedProperty._id}/rate-cards?start=${start.toISOString().slice(0, 10)}&end=${end.toISOString().slice(0, 10)}`
            ).then(rateCards => {
                setExistingRateCards(rateCards || []);
                setLoading(false);
            });
        }
    }, [dateRange, packages, selectedProperty]);

    useEffect(() => {
        if (packages.length === 0 || dates.length === 0 || loading) return;

        const matrix: { [packageId: string]: { [date: string]: string } } = {};

        packages.forEach(pkg => {
            matrix[pkg._id] = {};
            dates.forEach(date => {
                const dateStr = date.toISOString().slice(0, 10);
                const existingRateCard = existingRateCards?.find(rc => {
                    return rc.packageId._id === pkg._id && rc.date?.slice(0, 10) === dateStr;
                });

                matrix[pkg._id][dateStr] = existingRateCard ? String(existingRateCard.price) : String(pkg.price || 0);
            });
        });

        setRates(matrix);
    }, [existingRateCards, packages, dates, loading]);

    // Handle price change
    const handlePriceChange = (pkgId: string, dateStr: string, value: string) => {
        setRates(prev => ({
            ...prev,
            [pkgId]: {
                ...prev[pkgId],
                [dateStr]: value.replace(/[^0-9.]/g, ''),
            },
        }));
    };

    // Handle submit - bulk upsert all rate cards
    const handleSubmit = async () => {
        setSubmitting(true);
        try {
            const rateCards: RateCardData[] = [];
            Object.entries(rates).forEach(([pkgId, dateMap]) => {
                Object.entries(dateMap).forEach(([date, price]) => {
                    if (price && price !== '0') {
                        rateCards.push({
                            code: `${pkgId}-${date}`,
                            name: packages.find(p => p._id === pkgId)?.name || '',
                            description: '',
                            propertyId: selectedProperty._id,
                            date,
                            packageId: pkgId,
                            price: Number(price),
                        } as RateCardData);
                    }
                });
            });
            // Use new API: POST /rate-cards/{propertyId} { rateCards: [...] }
            await post(`properties/${selectedProperty._id}/rate-cards`, { rateCards });
            showSuccess('Rate cards updated successfully!');
            // Refresh the data
            const [start, end] = dateRange;
            const updatedRateCards = await get<IRateCard[]>(
                `properties/${selectedProperty._id}/rate-cards?start=${start?.toISOString().slice(0, 10)}&end=${end?.toISOString().slice(0, 10)}`
            );
            setExistingRateCards(updatedRateCards || []);
        } catch (_e) {
            showError('Failed to update rate cards');
        } finally {
            setSubmitting(false);
        }
    };

    if (loading) {
        return <UiLoader label="Loading Rate Cards..." />;
    }

    return (
        <div className="p-6 bg-white rounded-lg">
            <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold">Package's Rate Card</h2>
                <div>
                    <DatePicker
                        selectsRange
                        startDate={dateRange[0]}
                        endDate={dateRange[1]}
                        onChange={(update: [Date | null, Date | null] | null) => {
                            if (update) {
                                setDateRange(update);
                            }
                        }}
                        dateFormat="dd/MM/yyyy"
                        className="border px-2 py-1 rounded"
                    />
                    <span className="text-sm text-gray-500"></span>
                </div>
            </div>
            {loading ? (
                <UiLoader label="Loading..." />
            ) : (
                <form
                    onSubmit={e => {
                        e.preventDefault();
                        handleSubmit();
                    }}
                >
                    <div className="overflow-x-auto">
                        <table className="min-w-full border border-gray-200">
                            <thead>
                                <tr>
                                    <th className="bg-gray-50 px-4 py-2 text-left">Packages</th>
                                    {dates.map(date => (
                                        <th key={date.toISOString()} className="bg-gray-50 px-4 py-2 text-center">
                                            {date.toLocaleDateString()}
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody>
                                {packages.map(pkg => (
                                    <tr key={pkg._id}>
                                        <td className="font-medium px-4 py-2 whitespace-nowrap">{pkg.name}</td>
                                        {dates.map(date => {
                                            const dateStr = date.toISOString().slice(0, 10);
                                            // const existingRateCard = existingRateCards.find(rc =>
                                            //     rc.packageId._id === pkg._id && rc.date?.slice(0, 10) === dateStr
                                            // );
                                            // const isCustomRate = existingRateCard !== undefined;

                                            return (
                                                <td key={dateStr} className="px-2 py-1">
                                                    <input
                                                        type="number"
                                                        className={`w-full border rounded px-2 py-1 text-sm `}
                                                        value={rates[pkg._id]?.[dateStr] || ''}
                                                        onChange={e =>
                                                            handlePriceChange(pkg._id, dateStr, e.target.value)
                                                        }
                                                        min={0}
                                                        placeholder={String(pkg.price || 0)}
                                                    />
                                                    {/*{isCustomRate && (*/}
                                                    {/*    <div className="text-xs text-blue-600 mt-1">Custom rate</div>*/}
                                                    {/*)}*/}
                                                </td>
                                            );
                                        })}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                    <div className="flex justify-end mt-4">
                        <UiButton type="submit" disabled={submitting} className="bg-purple-500 text-white px-6">
                            {submitting ? 'Saving...' : 'Submit'}
                        </UiButton>
                    </div>
                </form>
            )}
        </div>
    );
}

export default RateCard;
