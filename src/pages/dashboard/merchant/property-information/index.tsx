import React, { useEffect, useState } from 'react';
import { Field, type FieldProps, Form, Formik } from 'formik';
import * as Yup from 'yup';
import { useNavigate, useParams } from 'react-router';
import { useApiHook } from '../../../../hooks/useApi.ts';
import type { ICompany } from '../../../../interfaces/ICompany.ts';
import { useToastHook } from '../../../../hooks/useToaster.ts';
import type { IDomainValue } from '../../../../interfaces/IDomainValue.ts';
import { domainValue } from '../../../../utils/constants/domainValues.ts';
import SectionTitle from '../../../../components/common/SectionTitle.tsx';
import StatusBadge from '../../../../components/common/StatusBadge.tsx';
import { formatDate, formatDateTime } from '../../../../utils/helpers/dateHelpers.ts';
import UiRadioGroupField from '../../../../lib/UiRadioGroupField.tsx';
import UiInput from '../../../../lib/UiInput.tsx';
import UiSelect from '../../../../lib/UiSelect.tsx';
import TimePicker from '../../../../lib/UiTimepicker.tsx';
import UiFileUpload from '../../../../lib/UiFileUpload.tsx';
import UiNextButton from '../../../../lib/UiNextButton.tsx';
import UiLoader from '../../../../lib/UiLoader.tsx';

const validationSchema = Yup.object({
    propertyChain: Yup.boolean().required('Required'),
    restriction: Yup.boolean().required('Required'),
    noOfBookingPerPerson: Yup.number().when('restriction', {
        is: true,
        then: schema => schema.min(1, 'Must be >= 1').required('Required'),
        otherwise: schema => schema.notRequired(),
    }),
    staylimit: Yup.number().min(0, 'Must be >= 0').required('Required'),
    deskOpens: Yup.string().when('open24hrs', {
        is: true,
        then: schema => schema.notRequired(),
        otherwise: schema => schema.required('Required'),
    }),
    deskCloses: Yup.string().when('open24hrs', {
        is: true,
        then: schema => schema.notRequired(),
        otherwise: schema => schema.required('Required'),
    }),
    propertyRooms: Yup.number().min(0, 'Must be >= 0').required('Required'),
    sharedUnits: Yup.number()
        .min(0, 'Must be >= 0')
        .required('Required')
        .test(
            'sum-check',
            'Sum of Shared Units and Private Units must not exceed the rooms in Property',
            function (value) {
                const { privateUnits, propertyRooms } = this.parent;
                if (
                    typeof value === 'number' &&
                    typeof privateUnits === 'number' &&
                    typeof propertyRooms === 'number'
                ) {
                    return privateUnits + value <= propertyRooms;
                }
                return true;
            }
        ),
    privateUnits: Yup.number()
        .min(0, 'Must be >= 0')
        .required('Required')
        .test(
            'sum-check',
            'Sum of Shared Units and Private Units must not exceed the rooms in Property',
            function (value) {
                const { sharedUnits, propertyRooms } = this.parent;
                if (typeof value === 'number' && typeof sharedUnits === 'number' && typeof propertyRooms === 'number') {
                    return sharedUnits + value <= propertyRooms;
                }
                return true;
            }
        ),
    exclusiveUse: Yup.boolean().required('Required'),
    sameAddress: Yup.boolean().required('Required'),
    aboutProperty: Yup.string().required('Required'),
    currency: Yup.string().required('Required'),
    accommodationType: Yup.string().when('isLandside', {
        is: true,
        then: schema => schema.required('Required'),
        otherwise: schema => schema.notRequired(),
    }),
    hourlyBookings: Yup.boolean().when('isLandside', {
        is: true,
        then: schema => schema.required('Required'),
        otherwise: schema => schema.notRequired(),
    }),
    open24hrs: Yup.boolean().required('Required'),
    // hotelAccess: Yup.mixed().test('is-string-or-array', 'Hotel access must be selected', value => {
    //     return typeof value === 'string' || Array.isArray(value);
    // }),
    // .required('Hotel access is required'),
    bookingIdPrefix: Yup.string().max(4, 'Maximum 4 characters are allowed').required('Booking ID prefix is required'),
    mailEvents: Yup.array(),
});

const mailEvents = [
    { name: 'Confirm Reservation', value: 'Confirm Reservation' },
    { name: 'Cancel Reservation', value: 'Cancel Reservation' },
    { name: 'Update Reservation', value: 'Update Reservation' },
];

const airSideHotelAccessOptions = [
    {
        value: 'Either the arrival or departure flight is international',
        label: 'Either the arrival or departure flight is international',
    },
    {
        value: 'Both arrival and departure flights are international',
        label: 'Both arrival and departure flights are international',
    },
];

const landSideHotelAccessOptions = [
    {
        value: 'Either the arrival or departure flight is international',
        label: 'Either the arrival or departure flight is international',
    },
    {
        value: 'Both arrival and departure flights are international',
        label: 'Both arrival and departure flights are international',
    },
    { value: 'Non-traveling guests are also welcome', label: 'Non-traveling guests are also welcome' },
];

const PropertyInformation = () => {
    const navigate = useNavigate();
    const { propertyId } = useParams();
    const { error, patch, get, loading } = useApiHook();
    const [property, setProperty] = useState<ICompany | null>(null);
    const { showError, showSuccess } = useToastHook();
    const [serviceTypes, setServiceTypes] = useState<IDomainValue[]>([]);
    const [serviceSubTypes, setServiceSubTypes] = useState<IDomainValue[]>([]);
    const [currencyList, setCurrencyList] = useState<IDomainValue[]>([]);

    const isOnboarding = location.pathname.includes('onboarding');

    const premisesType = property?.customFields?.InPremises === 'No' ? 'Landside' : 'Airside';
    const validAccessOptions =
        premisesType === 'Landside'
            ? landSideHotelAccessOptions.map(opt => opt.value)
            : airSideHotelAccessOptions.map(opt => opt.value);

    const businesslogo = (property?.customFields?.propertyDetails?.businesslogo as string[]) || [];

    const initialValues = {
        propertyChain: property?.customFields?.propertyDetails?.propertyChain ?? false,
        restriction: property?.customFields?.propertyDetails?.restriction ?? false,
        noOfBookingPerPerson: property?.customFields?.propertyDetails?.noOfBookingPerPerson ?? '',
        propertyRooms: property?.customFields?.propertyDetails?.propertyRooms ?? '',
        staylimit: property?.customFields?.propertyDetails?.staylimit ?? '',
        maxstaylimit: property?.customFields?.propertyDetails?.maxstaylimit ?? '',
        bookingIdPrefix: property?.customFields?.propertyDetails?.bookingIdPrefix ?? '',
        deskOpens: property?.customFields?.propertyDetails?.deskOpens ?? '',
        deskCloses: property?.customFields?.propertyDetails?.deskCloses ?? '',
        sharedUnits: property?.customFields?.propertyDetails?.sharedUnits ?? '',
        privateUnits: property?.customFields?.propertyDetails?.privateUnits ?? '',
        exclusiveUse: property?.customFields?.propertyDetails?.exclusiveUse ?? false,
        sameAddress: property?.customFields?.propertyDetails?.sameAddress ?? true,
        businesslogo,
        aboutProperty: property?.customFields?.propertyDetails?.aboutProperty ?? '',
        accommodationType:
            property?.premisesType === 'Landside'
                ? (property?.customFields?.propertyDetails?.accommodationType ?? '')
                : 'Hotel',
        hourlyBookings:
            property?.premisesType === 'Landside'
                ? (property?.customFields?.propertyDetails?.hourlyBookings ?? false)
                : true,
        open24hrs: property?.customFields?.propertyDetails?.open24hrs ?? false,
        isLandside: property?.premisesType === 'Landside',
        currency: property?.customFields?.propertyDetails?.currency ?? '',
        hotelAccess:
            premisesType === 'Landside'
                ? Array.isArray(property?.customFields?.propertyDetails?.hotelAccess)
                    ? property?.customFields?.propertyDetails?.hotelAccess.filter((val: string) =>
                          validAccessOptions.includes(val)
                      )
                    : []
                : typeof property?.customFields?.propertyDetails?.hotelAccess === 'string'
                  ? property?.customFields?.propertyDetails?.hotelAccess
                  : '',
        mailEvents: property?.customFields?.propertyDetails?.mailEvents ?? [],
    };

    const parseBooleans = (vals: typeof initialValues) => ({
        ...vals,
        propertyChain: typeof vals.propertyChain === 'boolean' ? vals.propertyChain : vals.propertyChain === 'true',
        restriction: typeof vals.restriction === 'boolean' ? vals.restriction : vals.restriction === 'true',
        exclusiveUse: typeof vals.exclusiveUse === 'boolean' ? vals.exclusiveUse : vals.exclusiveUse === 'true',
        sameAddress: typeof vals.sameAddress === 'boolean' ? vals.sameAddress : vals.sameAddress === 'true',
        hourlyBookings: typeof vals.hourlyBookings === 'boolean' ? vals.hourlyBookings : vals.hourlyBookings === 'true',
        open24hrs: !!vals.open24hrs,
    });
    const fetchProperty = async () => {
        const response = await get<ICompany>(`/properties/${propertyId}`);
        setProperty(response);
    };

    const fetchCurrency = async () => {
        const response = await get<IDomainValue[]>(`/domain-values/categoryId/${domainValue.Currency}`);
        setCurrencyList(response);
    };

    const fetchServiceTypes = async () => {
        const response = await get<IDomainValue[]>(`/domain-values/categoryId/${domainValue.ServicesCategories}`);
        setServiceTypes(response);
    };

    const fetchServiceSubTypes = async () => {
        const response = await get<IDomainValue[]>(`/domain-values/categoryId/${domainValue.ServicesSubCategories}`);
        setServiceSubTypes(response);
    };

    useEffect(() => {
        fetchProperty();
        fetchCurrency();
        fetchServiceTypes();
        fetchServiceSubTypes();
    }, []);

    if (loading) {
        return <UiLoader label="Loading property..." />;
    }
    return (
        <div className="mx-auto p-4 md:p-8 bg-white rounded-2xl shadow-xl my-8 max-w-5xl">
            <SectionTitle
                title="Property Information"
                subtitle="Please provide details about your property to get started."
            />

            <Formik
                enableReinitialize
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={async (values, formikHelpers) => {
                    const { setSubmitting } = formikHelpers;

                    try {
                        const booleanSafeValues = parseBooleans(values);

                        const response = await patch(`/properties/${propertyId}/customFields`, {
                            customFields: {
                                ...property?.customFields,
                                propertyDetails: {
                                    ...booleanSafeValues,
                                    hotelAccess:
                                        premisesType === 'Landside'
                                            ? Array.isArray(values.hotelAccess)
                                                ? values.hotelAccess
                                                : [values.hotelAccess]
                                            : values.hotelAccess,
                                },
                            },
                        });

                        if (response) {
                            showSuccess('Property information saved successfully!');
                            if (isOnboarding) {
                                navigate(`/onboarding/${propertyId}/amenities`);
                            }
                        }
                    } catch (_err) {
                        showError(error || 'Failed to update property info.');
                    } finally {
                        setSubmitting(false);
                    }
                }}
            >
                {formikProps => {
                    const { setFieldValue, values, validateForm, setTouched, handleSubmit } = formikProps;

                    const handleCustomSubmit = async (e: React.FormEvent) => {
                        e.preventDefault();

                        const errors = await validateForm();
                        if (Object.keys(errors).length > 0) {
                            setTouched(Object.fromEntries(Object.keys(values).map(k => [k, true])), true);
                            showError('Please fill all required fields');
                            return;
                        }

                        handleSubmit(); // runs your actual onSubmit logic
                    };
                    // TODO: Fix this useEffect to handle open24hrs logic correctly
                    // eslint-disable-next-line react-hooks/rules-of-hooks
                    useEffect(() => {
                        if (values.open24hrs) {
                            setFieldValue('deskOpens', '00:00');
                            setFieldValue('deskCloses', '00:00');
                        }
                    }, [values.open24hrs]);

                    return (
                        <Form onSubmit={handleCustomSubmit} className="space-y-8">
                            {/* Property Display */}
                            <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-800 mb-4">Property Details</h3>

                                {/* Property Name and Type */}
                                <div className="mb-4">
                                    <span className="font-semibold text-gray-800 mr-2">{property?.name}</span>
                                    {property?.premisesType && <StatusBadge type={property.premisesType} />}
                                </div>

                                {/* Address */}
                                <div className="mb-4">
                                    <p className="text-sm text-gray-600">
                                        <span className="font-medium text-gray-700">Location:</span>{' '}
                                        {property?.address.locationId.code}
                                    </p>
                                    <p className="text-sm text-gray-600">
                                        <span className="font-medium text-gray-700">Address:</span>{' '}
                                        {property?.address?.address1}
                                    </p>
                                </div>

                                {/* Contact and Property Information */}
                                <div className="space-y-3">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <h4 className="text-sm font-medium text-gray-700 mb-2">
                                                Contact Information
                                            </h4>
                                            <ul className="space-y-2">
                                                <li className="flex items-center text-sm">
                                                    <span className=" font-medium text-gray-700 w-16">Name:</span>
                                                    <span className="text-sm text-gray-700">
                                                        {property?.poc?.firstName} {property?.poc?.lastName}
                                                    </span>
                                                </li>
                                                <li className="flex items-center text-sm">
                                                    <span className=" font-medium text-gray-700 w-16">Email:</span>
                                                    <span className="text-sm text-gray-700">
                                                        {property?.poc?.email}
                                                    </span>
                                                </li>
                                                <li className="flex items-center text-sm">
                                                    <span className=" font-medium text-gray-700 w-16">Phone:</span>
                                                    <span className="text-sm text-gray-700">
                                                        {property?.poc?.phone?.phoneNumber}
                                                    </span>
                                                </li>
                                            </ul>
                                        </div>

                                        <div>
                                            <h4 className="text-sm font-medium text-gray-700 mb-2">
                                                Property Information
                                            </h4>
                                            <ul className="space-y-2">
                                                <li className="flex items-center text-sm">
                                                    <span className=" font-medium text-gray-700 w-20">
                                                        Property build year:
                                                    </span>
                                                    <span className="text-sm text-gray-700">
                                                        {property?.customFields?.built as string}
                                                    </span>
                                                </li>
                                                <li className="flex items-center text-sm">
                                                    <span className=" font-medium text-gray-700 w-20">
                                                        Accepting booking from:
                                                    </span>
                                                    <span className="text-sm text-gray-700">
                                                        {formatDate(
                                                            (property?.customFields?.bookingStarts as Date) ||
                                                                new Date()
                                                        )}
                                                    </span>
                                                </li>
                                                <li className="flex items-center text-sm">
                                                    <span className=" font-medium text-gray-700 w-20">Service:</span>
                                                    <span className="text-sm text-gray-700">
                                                        {
                                                            serviceTypes.find(
                                                                service =>
                                                                    service._id === property?.customFields?.serviceType
                                                            )?.name
                                                        }
                                                    </span>
                                                </li>
                                                <li className="flex items-center text-sm">
                                                    <span className=" font-medium text-gray-700 w-20">Applied on:</span>
                                                    <span className="text-sm text-gray-700">
                                                        {formatDateTime(property?.createdAt || new Date())}
                                                    </span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <UiRadioGroupField
                                    name="propertyChain"
                                    label="Do you have a property chain?"
                                    required
                                    options={[
                                        { value: true, label: 'Yes' },
                                        { value: false, label: 'No' },
                                    ]}
                                />

                                <UiRadioGroupField
                                    name="restriction"
                                    label="Is there any restriction on the number of bookings per person?"
                                    required
                                    options={[
                                        { value: true, label: 'Yes' },
                                        { value: false, label: 'No' },
                                    ]}
                                />

                                {values.restriction && (
                                    <UiInput
                                        required
                                        label="Enter the maximum number of booking per person"
                                        name="noOfBookingPerPerson"
                                        type="number"
                                        placeholder="Enter maximum bookings allowed per person"
                                    />
                                )}

                                {values.isLandside && (
                                    <>
                                        <UiSelect
                                            name="accommodationType"
                                            label="What accommodation type best describes this property?"
                                            required
                                            options={serviceSubTypes.map(service => ({
                                                value: service._id,
                                                label: service.name,
                                            }))}
                                        />
                                        <UiRadioGroupField
                                            name="hourlyBookings"
                                            label="Are hourly bookings available?"
                                            required
                                            options={[
                                                { value: true, label: 'Yes' },
                                                { value: false, label: 'No' },
                                            ]}
                                        />
                                    </>
                                )}

                                <UiSelect
                                    name="currency"
                                    label="Currency"
                                    required
                                    options={currencyList.map(currency => ({
                                        value: currency._id,
                                        label: currency.name,
                                    }))}
                                />
                                <UiInput
                                    required
                                    label="How many rooms or units does the property have?"
                                    name="propertyRooms"
                                    type="number"
                                />
                                <div className="col-span-2">
                                    <h3 className="text-lg">How many units have private vs shared bathroom?</h3>
                                </div>
                                <UiInput required label="Shared Units" name="sharedUnits" type="number" />
                                <UiInput required label="Private Units" name="privateUnits" type="number" />
                                <UiInput required label="Minimum stay limit (hrs)" name="staylimit" type="number" />
                                <UiInput required label="Maximum stay limit (hrs)" name="maxstaylimit" type="number" />

                                <div className="col-span-2">
                                    <h3 className="text-lg">What is Front desk Schedule?</h3>
                                </div>
                                <TimePicker name="deskOpens" label="Desk Opens" disabled={!!values.open24hrs} />
                                <TimePicker name="deskCloses" label="Desk Closes" disabled={!!values.open24hrs} />
                                <div className="flex items-center gap-2">
                                    <Field
                                        type="checkbox"
                                        name="open24hrs"
                                        id="open24hrs"
                                        className="w-5 h-5 text-indigo-600 border-gray-300 rounded"
                                    />
                                    <label htmlFor="open24hrs" className="text-sm text-gray-700">
                                        Open 24 Hours
                                    </label>
                                </div>

                                <div></div>

                                <UiRadioGroupField
                                    name="exclusiveUse"
                                    label="Is your property for exclusive use?"
                                    required
                                    options={[
                                        { value: true, label: 'Yes' },
                                        { value: false, label: 'No' },
                                    ]}
                                />

                                <UiRadioGroupField
                                    name="sameAddress"
                                    label="Are all units at the same address?"
                                    required
                                    options={[
                                        { value: true, label: 'Yes' },
                                        { value: false, label: 'No' },
                                    ]}
                                />

                                {/* <div className="col-span-2">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <UiSelect
                                            name="hotelAccess"
                                            label="Who Can Access This Hotel? *"
                                            options={
                                                property?.premisesType === 'Landside'
                                                    ? landSideHotelAccessOptions
                                                    : airSideHotelAccessOptions
                                            }
                                            multiple={property?.premisesType === 'Landside'}
                                        />
                                    </div>
                                </div> */}
                                <UiInput required label="Booking id prefix" name="bookingIdPrefix" />
                            </div>
                            <UiSelect
                                name="mailEvents"
                                label="Reservation Notification Preferences (via Email)"
                                multiple
                                options={mailEvents.map(event => ({ value: event.value, label: event.name }))}
                            />

                            <div className="">
                                <Field name="businesslogo">
                                    {({ field, form }: FieldProps) => (
                                        <UiFileUpload
                                            name={field.name}
                                            onFileUpload={urls => form.setFieldValue(field.name, urls)}
                                            label="Business Logo"
                                            value={field.value}
                                            allowedTypes="images"
                                            multiple={false}
                                        />
                                    )}
                                </Field>
                            </div>

                            <UiInput
                                required
                                label="About your Property"
                                name="aboutProperty"
                                placeholder="Describe your property, facilities, and features..."
                            />

                            <UiNextButton label="Next" />
                        </Form>
                    );
                }}
            </Formik>
        </div>
    );
};

export default PropertyInformation;
