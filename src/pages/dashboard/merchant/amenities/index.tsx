import { useEffect, useState } from 'react';
import { Field, Form, Formik } from 'formik';
import { useLocation, useNavigate, useParams } from 'react-router';
import { useApiHook } from '../../../../hooks/useApi.ts';
import type { ICompany } from '../../../../interfaces/ICompany.ts';
import type { IDomainValue } from '../../../../interfaces/IDomainValue.ts';
import { useToastHook } from '../../../../hooks/useToaster.ts';
import { domainValue } from '../../../../utils/constants/domainValues.ts';
import UiBackButton from '../../../../lib/UiBackButton.tsx';
import UiNextButton from '../../../../lib/UiNextButton.tsx';
import UiLoader from '../../../../lib/UiLoader.tsx';

interface CustomFields {
    amenities?: string[];

    [key: string]: unknown;
}

function PropertyAmenities() {
    const { propertyId } = useParams();
    const { patch, get, loading } = useApiHook();
    const [property, setProperty] = useState<ICompany | null>(null);
    const [amenities, setAmenities] = useState<IDomainValue[]>([]);
    const { showSuccess, showError } = useToastHook();
    const [amenitiesCategories, setAmenitiesCategories] = useState<Map<string, IDomainValue>>(new Map());
    const navigate = useNavigate();
    const location = useLocation();
    const isOnboarding = location.pathname.includes('onboarding');

    const fetchAmenities = async () => {
        const response = await get<IDomainValue[]>(`/domain-values/categoryId/${domainValue.AmenitiesSubCategory}`);
        setAmenities(response);
    };

    const fetchAmenitiesCategories = async () => {
        const response = await get<IDomainValue[]>(`/domain-values/categoryId/${domainValue.AmenitiesCategory}`);
        const categories = new Map<string, IDomainValue>();
        response.forEach(category => {
            categories.set(category._id, category);
        });
        setAmenitiesCategories(categories);
    };

    const fetchProperty = async () => {
        const response = await get<ICompany>(`/properties/${propertyId}`);
        setProperty(response);
    };

    useEffect(() => {
        fetchProperty();
        fetchAmenities();
        fetchAmenitiesCategories();
    }, []);

    if (loading) {
        return <UiLoader label={'Loading Amenities...'} />;
    }

    // Group amenities by category for display
    const groupedAmenities = amenities.reduce(
        (acc, amenity) => {
            const parentId = amenity.parentId!;
            if (!acc[parentId]) {
                acc[parentId] = [];
            }
            acc[parentId].push(amenity);
            return acc;
        },
        {} as Record<string, IDomainValue[]>
    );

    // Get initial values from property's customFields if they exist
    const getInitialValues = () => {
        const initialValues: Record<string, string[]> = {};

        // Initialize all parentId groups with empty arrays
        amenities.forEach(amenity => {
            const parentId = amenity.parentId!;
            if (!initialValues[parentId]) {
                initialValues[parentId] = [];
            }
        });

        const customFields = property?.customFields as CustomFields | undefined;
        if (customFields?.amenities) {
            // Convert flat array of IDs back to parentId-based structure for form
            amenities.forEach(amenity => {
                if (customFields.amenities?.includes(amenity._id)) {
                    const parentId = amenity.parentId!;
                    if (!initialValues[parentId]) {
                        initialValues[parentId] = [];
                    }
                    initialValues[parentId].push(amenity._id);
                }
            });
        }

        return initialValues;
    };

    return (
        <Formik
            initialValues={getInitialValues()}
            enableReinitialize={true}
            onSubmit={async values => {
                try {
                    // Flatten the category-based structure to a single array of IDs
                    const selectedAmenityIds = Object.values(values).flat();

                    const res = await patch(`/properties/${propertyId}/customFields`, {
                        customFields: {
                            ...property?.customFields,
                            amenities: selectedAmenityIds,
                        },
                    });
                    if (res) {
                        showSuccess('Amenities saved successfully!');
                    }
                    if (isOnboarding) {
                        navigate(`/onboarding/${propertyId}/room-types`);
                    }
                } catch (error) {
                    showError('Failed to update amenities info.');
                    console.error('Error updating amenities:', error);
                }
            }}
        >
            {() => (
                <Form className="p-6 bg-gray-50 rounded-lg shadow-md">
                    {Object.entries(groupedAmenities).map(([parentId, items]) => (
                        <div key={parentId} className="mb-6">
                            <h3 className="text-lg font-semibold text-gray-700 mb-4 border-b pb-2">
                                {amenitiesCategories.get(parentId)?.name}
                            </h3>
                            <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                                {items.map(item => (
                                    <div key={item._id} className="flex items-center">
                                        <label className="flex items-center space-x-2">
                                            <Field
                                                type="checkbox"
                                                name={parentId}
                                                value={item._id}
                                                className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                            />
                                            <span className="text-gray-600">{item.name}</span>
                                        </label>
                                    </div>
                                ))}
                            </div>
                        </div>
                    ))}

                    <div className="flex justify-between mt-6 gap-4">
                        {isOnboarding && <UiBackButton />}
                        <UiNextButton label={isOnboarding ? 'Next' : 'Submit'} />
                    </div>
                </Form>
            )}
        </Formik>
    );
}

export default PropertyAmenities;
