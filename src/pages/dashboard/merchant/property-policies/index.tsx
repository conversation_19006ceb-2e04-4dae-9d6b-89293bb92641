import { useEffect, useState } from 'react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import type { IDomainValue } from '../../../../interfaces/IDomainValue.ts';
import { useNavigate, useParams } from 'react-router';
import { useApiHook } from '../../../../hooks/useApi.ts';
import { useToastHook } from '../../../../hooks/useToaster.ts';
import type { ICompany } from '../../../../interfaces/ICompany.ts';
import UiInput from '../../../../lib/UiInput.tsx';
import UiSelect from '../../../../lib/UiSelect.tsx';
import type { IOptions } from '../../../../interfaces/IOptions.ts';
import UiLoader from '../../../../lib/UiLoader.tsx';
import SectionTitle from '../../../../components/common/SectionTitle.tsx';
import UiNextButton from '../../../../lib/UiNextButton.tsx';
import UiBackButton from '../../../../lib/UiBackButton.tsx';
import CancellationRules from '../../../../components/common/policies/CancellationRules.tsx';
import CustomRules from '../../../../components/common/policies/CustomRules.tsx';

interface FormValues {
    [key: string]: string | string[];
}

interface Policy {
    _id: string;
    name: string;
    category: IDomainValue;
    description?: string;
    input_type: 'text' | 'radio' | 'checkbox' | 'select' | 'custom_rule';
    is_required: boolean;
    options?: string[];
    validation_rules?: {
        min?: number;
        max?: number;
        pattern?: string;
    };
    config?: {
        rules?: Array<{
            refund_percent: number;
            before_hours: number;
            after_hours?: number;
        }>;
    };
}

const createValidationSchema = (policies: Policy[]) => {
    // @ts-nocheck
    const validationObject: Record<string, Yup.Schema<unknown>> = {};

    policies.forEach(policy => {
        const fieldName = policy._id;

        if (policy.is_required) {
            switch (policy.input_type) {
                case 'text':
                    validationObject[fieldName] = Yup.string().required(`${policy.name} is required`);
                    break;
                case 'radio':
                case 'select':
                    validationObject[fieldName] = Yup.string().required(`Please select ${policy.name}`);
                    break;
                case 'checkbox':
                    validationObject[fieldName] = Yup.array().min(
                        1,
                        `Please select at least one option for ${policy.name}`
                    );
                    break;
                case 'custom_rule':
                    validationObject[fieldName] = Yup.string().required(`${policy.name} is required`);
                    break;
            }
        } else {
            switch (policy.input_type) {
                case 'text':
                    validationObject[fieldName] = Yup.string();
                    break;
                case 'radio':
                case 'select':
                    validationObject[fieldName] = Yup.string();
                    break;
                case 'checkbox':
                    validationObject[fieldName] = Yup.array();
                    break;
                case 'custom_rule':
                    validationObject[fieldName] = Yup.string();
                    break;
            }
        }

        // Add validation rules if they exist
        if (
            policy.input_type === 'text' &&
            (policy.validation_rules?.min !== undefined || policy.validation_rules?.max !== undefined)
        ) {
            let schema = Yup.number().typeError(`${policy.name} must be a number`);
            if (policy.is_required) {
                schema = schema.required(`${policy.name} is required`);
            }
            if (policy.validation_rules.min !== undefined) {
                schema = schema.min(
                    policy.validation_rules.min,
                    `${policy.name} must be at least ${policy.validation_rules.min}`
                );
            }
            if (policy.validation_rules.max !== undefined) {
                schema = schema.max(
                    policy.validation_rules.max,
                    `${policy.name} must be at most ${policy.validation_rules.max}`
                );
            }

            validationObject[fieldName] = schema;
        } else {
            // TODO:TypeScript: Type fix for validationObject[fieldName]
            if (policy.validation_rules?.min !== undefined) {
                // @ts-expect-error assuming validationObject[fieldName] is a Yup schema
                validationObject[fieldName] = validationObject[fieldName]?.min(
                    policy.validation_rules.min,
                    `${policy.name} must be at least ${policy.validation_rules.min}`
                );
            }
            if (policy.validation_rules?.max !== undefined) {
                // @ts-expect-error assuming validationObject[fieldName] is a Yup schema
                validationObject[fieldName] = validationObject[fieldName]?.max(
                    policy.validation_rules.max,
                    `${policy.name} must be at most ${policy.validation_rules.max}`
                );
            }
            if (policy.validation_rules?.pattern) {
                // @ts-expect-error assuming validationObject[fieldName] is a Yup schema
                validationObject[fieldName] = validationObject[fieldName]?.matches(
                    new RegExp(policy.validation_rules.pattern),
                    `${policy.name} format is invalid`
                );
            }
        }
    });

    validationObject['cancellationPolicy'] = Yup.array().of(
        Yup.object().shape({
            hours: Yup.number().required('Hours are required').min(0),
            refund_percent: Yup.number()
                .required('Refund % is required')
                .min(0, 'Minimum is 0%')
                .max(100, 'Maximum is 100%'),
            description: Yup.string().required('Description is required'),
        })
    );

    validationObject['customPolicy'] = Yup.array().of(
        Yup.object().shape({
            title: Yup.string().required('Title is required'),
        })
    );

    return Yup.object(validationObject);
};

const Policies = () => {
    const { propertyId } = useParams();
    const [formError, setFormError] = useState<string | null>(null);
    const { loading, error, patch, get } = useApiHook();
    const { showSuccess, showError } = useToastHook();
    const [property, setProperty] = useState<ICompany | null>(null);
    const [policies, setPolicies] = useState<Policy[]>([]);
    const [validationSchema, setValidationSchema] = useState(Yup.object({}));
    const navigate = useNavigate();
    const isOnboarding = location.pathname.includes('onboarding');

    const fetchProperty = async () => {
        const response = await get<ICompany>(`/properties/${propertyId}`);
        setProperty(response);
    };

    const fetchPolicies = async () => {
        try {
            const response = await get<{ policies: Policy[] }>('/policy');
            if (response?.policies) {
                setPolicies(response.policies);
                setValidationSchema(createValidationSchema(response.policies));
            } else if (Array.isArray(response)) {
                setPolicies(response);
                setValidationSchema(createValidationSchema(response));
            }
        } catch (_error) {
            showError('Failed to fetch policies');
            setPolicies([]);
        }
    };

    useEffect(() => {
        if (propertyId) {
            fetchProperty();
            fetchPolicies();
        }
    }, [propertyId]);

    if (!propertyId) {
        return (
            <div className="mt-2 p-6 bg-white rounded-lg text-red-600">
                No property selected. Please select a property first.
            </div>
        );
    }

    const createInitialValues = () => {
        const values: FormValues = {};

        policies.forEach(policy => {
            const fieldName = policy._id;

            switch (policy.input_type) {
                case 'text':
                    // @ts-expect-error type mismatch, but we handle it later
                    values[fieldName] = property?.customFields?.policies?.[fieldName] || '';
                    break;
                case 'radio':
                case 'select':
                    // @ts-expect-error type mismatch, but we handle it later
                    values[fieldName] = property?.customFields?.policies?.[fieldName] || '';
                    break;
                case 'checkbox': {
                    // @ts-expect-error type mismatch, but we handle it later
                    const existingValue = property?.customFields?.policies?.[fieldName];
                    values[fieldName] = Array.isArray(existingValue) ? existingValue : [];
                    break;
                }
                case 'custom_rule':
                    // @ts-expect-error type mismatch, but we handle it later
                    values[fieldName] = property?.customFields?.policies?.[fieldName] || '';
                    break;
            }
        });
        const cancellation = property?.customFields?.cancellationPolicy;
        values['cancellationPolicy'] = Array.isArray(cancellation) ? cancellation : [];
        const custom = property?.customFields?.customPolicy;
        values['customPolicy'] = Array.isArray(custom) ? custom : [];

        return values;
    };

    const initialValues = createInitialValues();
    const handleSubmit = async (values: typeof initialValues) => {
        try {
            const { cancellationPolicy, customPolicy, ...policyValues } = values;
            setFormError(null);
            await patch(`/properties/${propertyId}/customFields`, {
                customFields: {
                    ...property?.customFields,
                    policies: policyValues,
                    cancellationPolicy,
                    customPolicy,
                },
            });
            showSuccess('Policies updated successfully!');
            if (isOnboarding) {
                navigate(`/onboarding/${propertyId}/business-details`);
            }
        } catch (_error) {
            showError('Failed to update policies');
        }
    };

    const renderPolicyField = (policy: Policy, fieldName: string) => {
        // Helper to normalize options to { value, label, key } objects
        const normalizeOptions = (options: IOptions[] | undefined) =>
            options?.map((option, idx) => {
                if (typeof option === 'object' && option !== null) {
                    return {
                        value: option.value ?? option._id ?? idx,
                        label: option.description ?? option.value ?? option._id ?? String(idx),
                        key: option._id ?? option.value ?? idx,
                    };
                } else {
                    return {
                        value: option,
                        label: option,
                        key: option,
                    };
                }
            }) || [];
        const policyOptions = policy.options as IOptions[] | undefined;

        switch (policy.input_type) {
            case 'text':
                return (
                    <UiInput
                        name={fieldName}
                        placeholder={`Enter ${policy.name.toLowerCase()}`}
                        required={policy.is_required}
                    />
                );

            case 'radio': {
                const options = normalizeOptions(policyOptions);
                return (
                    <div>
                        <div className={`flex flex-row gap-6`}>
                            {options.map(opt => (
                                <label key={opt.key} className="inline-flex items-center">
                                    <Field
                                        type="radio"
                                        name={fieldName}
                                        value={opt.value}
                                        required={policy.is_required}
                                        className="w-5 h-5 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                                    />
                                    <span className="ml-2 text-sm text-gray-700">{opt.value}</span>
                                    <ErrorMessage
                                        name={fieldName}
                                        component="div"
                                        className="text-red-600 text-sm mt-1"
                                    />
                                </label>
                            ))}
                        </div>
                    </div>
                );
            }

            case 'select': {
                const options = normalizeOptions(policyOptions);
                return (
                    <UiSelect
                        name={fieldName}
                        options={options.map(opt => ({ value: opt.value, label: opt.label }))}
                        required={policy.is_required}
                    />
                );
            }

            case 'checkbox': {
                const options = normalizeOptions(policyOptions);
                return (
                    <div>
                        <div className="space-y-2">
                            {options.map(opt => (
                                <label key={opt.key} className="flex items-center">
                                    <Field
                                        type="checkbox"
                                        name={fieldName}
                                        value={opt.value}
                                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                    />
                                    <span className="ml-2 text-sm text-gray-700">{opt.label}</span>
                                </label>
                            ))}
                        </div>
                    </div>
                );
            }

            case 'custom_rule':
                return (
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            {policy.name} {policy.is_required && <span className="text-red-500">*</span>}
                        </label>
                        <div className="space-y-2">
                            {policy.config?.rules?.map((rule, index) => (
                                <div key={index} className="p-3 border rounded-md bg-gray-50">
                                    <div className="text-sm text-gray-600">
                                        {rule.before_hours} hours before: {rule.refund_percent}% refund
                                        {rule.after_hours &&
                                            `, ${rule.after_hours} hours after: ${rule.refund_percent}% refund`}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                );

            default:
                return null;
        }
    };

    if (loading || !property) {
        return (
            <div className="mt-2 p-6 bg-white rounded-lg">
                <UiLoader label="Loading policies..." />
            </div>
        );
    }

    const groupedPolicies = policies.reduce((acc: Record<string, { name: string; policies: Policy[] }>, policy) => {
        const categoryId = policy.category._id || 'uncategorized';
        const categoryName = policy.category?.name || 'Uncategorized';

        if (!acc[categoryId]) {
            acc[categoryId] = {
                name: categoryName,
                policies: [],
            };
        }

        acc[categoryId].policies.push(policy);
        return acc;
    }, {});

    return (
        <div className="mt-2 p-6 bg-white rounded-lg">
            <div className="mb-8">
                <SectionTitle title="Property Policies" subtitle="Configure your property policies and rules" />
            </div>

            <Formik
                key={JSON.stringify(initialValues)} // Force re-render when initial values change
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
                enableReinitialize
                validateOnChange={true}
                validateOnBlur={true}
                validateOnMount={false}
            >
                {({ errors }) => {
                    return (
                        <Form className="space-y-6">
                            {Object.entries(groupedPolicies).map(([categoryId, group]) => (
                                <div key={categoryId} className="mb-8 border-b pb-6">
                                    <h2 className="text-xl font-bold mb-4">{group.name}</h2>

                                    {group.policies.map(policy => (
                                        <div key={policy._id} className="mb-6 pl-4">
                                            <h3 className="text-lg font-semibold text-gray-900">
                                                {policy.name}{' '}
                                                {policy.is_required && <span className="text-red-500">*</span>}
                                            </h3>

                                            {policy.description && (
                                                <p className="text-sm text-gray-600 mt-1">{policy.description}</p>
                                            )}

                                            <div className="mt-2">{renderPolicyField(policy, policy._id)}</div>
                                        </div>
                                    ))}
                                </div>
                            ))}

                            <CancellationRules />

                            {/*{Object.keys(errors).length > 0 &&*/}
                            {/*    Object.keys(touched).length !== Object.keys(initialValues).length && (*/}
                            {/*        <div className="text-red-600 bg-red-50 border border-red-200 rounded p-3 text-sm">*/}
                            {/*            Please fill all required fields marked with * before proceeding.*/}
                            {/*        </div>*/}
                            {/*    )}*/}
                            {Object.keys(errors).length > 0 && (
                                <div className="text-red-600 bg-red-50 border border-red-200 rounded p-3 text-sm">
                                    Please fill all required fields marked with * before proceeding.
                                </div>
                            )}

                            {/* Error and Loading Feedback */}
                            {formError && (
                                <div className="mb-4 text-red-600 bg-red-50 border border-red-200 rounded p-3 text-sm">
                                    {formError}
                                </div>
                            )}

                            {error && (
                                <div className="mb-4 text-red-600 bg-red-50 border border-red-200 rounded p-3 text-sm">
                                    {error}
                                </div>
                            )}

                            <CustomRules />

                            <div className="flex justify-between mt-6 gap-4">
                                {isOnboarding && <UiBackButton />}
                                <UiNextButton label={isOnboarding ? 'Next' : 'Submit'} />
                            </div>
                        </Form>
                    );
                }}
            </Formik>
        </div>
    );
};

export default Policies;
