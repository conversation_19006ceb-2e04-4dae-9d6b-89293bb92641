import { useEffect, useState } from 'react';
import DatePicker from 'react-datepicker';
import useBoundStore from '../../../../store/useBoundStore.ts';
import { useApiHook } from '../../../../hooks/useApi.ts';
import { useToastHook } from '../../../../hooks/useToaster.ts';
import type { IRoomType } from '../../../../interfaces/IRoomType.ts';
import type { IRoomAvailability, RoomAvailabilityPayload } from '../../../../interfaces/IRoomAvailability.ts';
import UiLoader from '../../../../lib/UiLoader.tsx';
import UiButton from '../../../../lib/UiButton.tsx';

const getDateArray = (start: Date, end: Date) => {
    const arr = [];
    const dt = new Date(start);
    while (dt <= end) {
        arr.push(new Date(dt));
        dt.setDate(dt.getDate() + 1);
    }
    return arr;
};

function RoomAvailability() {
    const { selectedProperty } = useBoundStore();
    const { get, post } = useApiHook();
    const { showSuccess, showError } = useToastHook();
    const [roomTypes, setRoomTypes] = useState<IRoomType[]>([]);
    const [loading, setLoading] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
        new Date(),
        new Date(Date.now() + 6 * 24 * 60 * 60 * 1000),
    ]); // default 7 days
    const [dates, setDates] = useState<Date[]>(
        getDateArray(new Date(), new Date(Date.now() + 6 * 24 * 60 * 60 * 1000))
    );
    const [availabilities, setAvailabilities] = useState<{ [roomType: string]: { [date: string]: string } }>({});
    const [existingRoomAvailability, setExistingRoomAvailability] = useState<IRoomAvailability[]>([]);

    // Fetch room type and availability
    useEffect(() => {
        if (!selectedProperty?._id) return;
        setLoading(true);
        get<IRoomType[]>(`properties/${selectedProperty._id}/room-types`).then(roomType => {
            setRoomTypes(roomType || []);
            setLoading(false);
        });
    }, [selectedProperty]);

    // Fetch availability when date range or room type change
    useEffect(() => {
        if (!selectedProperty?._id || roomTypes.length === 0) return;

        const [start, end] = dateRange;
        if (start && end) {
            setLoading(true);
            setDates(getDateArray(start!, end!));
            get<IRoomAvailability[]>(
                `properties/${selectedProperty._id}/room-type-availability?start=${start.toISOString().slice(0, 10)}&end=${end.toISOString().slice(0, 10)}`
            ).then(roomAvailability => {
                setExistingRoomAvailability(roomAvailability || []);
                setLoading(false);
            });
        }
    }, [dateRange, roomTypes, selectedProperty]);

    useEffect(() => {
        if (roomTypes.length === 0 || dates.length === 0 || loading) return;

        const matrix: { [roomType: string]: { [date: string]: string } } = {};

        roomTypes.forEach(roomType => {
            matrix[roomType._id] = {};
            dates.forEach(date => {
                const dateStr = date.toISOString().slice(0, 10);
                const existingRoomType = existingRoomAvailability?.find(rc => {
                    return rc.roomType._id === roomType._id && rc.date?.slice(0, 10) === dateStr;
                });

                matrix[roomType._id][dateStr] = existingRoomType
                    ? String(existingRoomType.availability)
                    : String(roomType.noOfRooms || 0);
            });
        });

        setAvailabilities(matrix);
    }, [existingRoomAvailability, roomTypes, dates, loading]);

    // Handle price change
    const handlePriceChange = (pkgId: string, dateStr: string, value: string) => {
        setAvailabilities(prev => ({
            ...prev,
            [pkgId]: {
                ...prev[pkgId],
                [dateStr]: value.replace(/[^0-9.]/g, ''),
            },
        }));
    };

    // Handle submit - bulk upsert all room availability
    const handleSubmit = async () => {
        setSubmitting(true);
        try {
            const roomAvailabilities: RoomAvailabilityPayload[] = [];
            Object.entries(availabilities).forEach(([propertyId, dateMap]) => {
                Object.entries(dateMap).forEach(([date, availability]) => {
                    if (availability && availability !== '0') {
                        roomAvailabilities.push({
                            code: `${propertyId}-${date}`,
                            propertyId: selectedProperty._id,
                            date,
                            roomType: propertyId,
                            availability: Number(availability),
                        } as RoomAvailabilityPayload);
                    }
                });
            });
            // Use the new API: POST /properties/${selectedProperty._id}/room-type-availability { rateCards: [...] }
            await post(`properties/${selectedProperty._id}/room-type-availability`, { roomAvailabilities });
            showSuccess('Room availability updated successfully!');
            // Refresh the data
            const [start, end] = dateRange;
            const updatedRoomAvailability = await get<IRoomAvailability[]>(
                `properties/${selectedProperty._id}/room-type-availability?start=${start?.toISOString().slice(0, 10)}&end=${end?.toISOString().slice(0, 10)}`
            );
            setExistingRoomAvailability(updatedRoomAvailability || []);
        } catch (_e) {
            showError('Failed to update room availability');
        } finally {
            setSubmitting(false);
        }
    };

    if (loading) {
        return <UiLoader label="Loading Room Availability..." />;
    }

    return (
        <div className="p-6 bg-white rounded-lg">
            <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold">Room Availability</h2>
                <div>
                    <DatePicker
                        selectsRange
                        startDate={dateRange[0]}
                        endDate={dateRange[1]}
                        onChange={(update: [Date | null, Date | null] | null) => {
                            if (update) {
                                setDateRange(update);
                            }
                        }}
                        dateFormat="dd/MM/yyyy"
                        className="border px-2 py-1 rounded"
                    />
                </div>
            </div>
            {loading ? (
                <UiLoader label="Loading..." />
            ) : (
                <form
                    onSubmit={e => {
                        e.preventDefault();
                        handleSubmit();
                    }}
                >
                    <div className="overflow-x-auto">
                        <table className="min-w-full border border-gray-200">
                            <thead>
                                <tr>
                                    <th className="bg-gray-50 px-4 py-2 text-left">Packages</th>
                                    {dates.map(date => (
                                        <th key={date.toISOString()} className="bg-gray-50 px-4 py-2 text-center">
                                            {date.toLocaleDateString()}
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody>
                                {roomTypes.map(roomType => (
                                    <tr key={roomType._id}>
                                        <td className="font-medium px-4 py-2 whitespace-nowrap">{roomType.name}</td>
                                        {dates.map(date => {
                                            const dateStr = date.toISOString().slice(0, 10);
                                            // const existingRateCard = existingRoomAvailability.find(rc =>
                                            //     rc.roomType._id === roomType._id && rc.date?.slice(0, 10) === dateStr
                                            // );
                                            // const isCustomRate = existingRateCard !== undefined;

                                            return (
                                                <td key={dateStr} className="px-2 py-1">
                                                    <input
                                                        type="number"
                                                        className={`w-full border rounded px-2 py-1 text-sm `}
                                                        value={availabilities[roomType._id]?.[dateStr] || ''}
                                                        onChange={e =>
                                                            handlePriceChange(roomType._id, dateStr, e.target.value)
                                                        }
                                                        min={0}
                                                        placeholder={String(roomType.noOfRooms || 0)}
                                                    />
                                                    {/*{isCustomRate && (*/}
                                                    {/*    <div className="text-xs text-blue-600 mt-1">Custom Availability</div>*/}
                                                    {/*)}*/}
                                                </td>
                                            );
                                        })}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                    <div className="flex justify-end mt-4">
                        <UiButton type="submit" disabled={submitting} className="bg-purple-500 text-white px-6">
                            {submitting ? 'Saving...' : 'Submit'}
                        </UiButton>
                    </div>
                </form>
            )}
        </div>
    );
}

export default RoomAvailability;
