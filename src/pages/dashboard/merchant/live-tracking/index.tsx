import { useEffect, useState, useMemo } from 'react';
import type { ColumnDef } from '@tanstack/react-table';
import useBoundStore from '../../../../store/useBoundStore.ts';
import type { GroupReservation } from '../../../../interfaces/IBooking.ts';
import { useApiHook } from '../../../../hooks/useApi.ts';
import { formatDate, formatDateTime } from '../../../../utils/helpers/dateHelpers.ts';
import CurrencyFormat from '../../../../components/common/CurrencyFormat.tsx';
import UiTable from '../../../../lib/UiTable.tsx';
import UiPopup from '../../../../lib/UiPopup.tsx';
import UiLoader from '../../../../lib/UiLoader.tsx';

function LiveTracking() {
    const { selectedProperty } = useBoundStore();
    const [popupOpen, setPopupOpen] = useState(false);
    const [selectedReservation, setSelectedReservation] = useState<GroupReservation | null>(null);
    const [reservations, setReservations] = useState<GroupReservation[]>([]);
    const { get, loading } = useApiHook();

    useEffect(() => {
        const fetchReservations = async () => {
            const response = await get<GroupReservation[]>(`reservations/property/${selectedProperty._id}`);

            if (response) {
                setReservations(response.filter(r => r.status == 'confirmed'));
            }
        };
        fetchReservations();
    }, [selectedProperty]);

    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case 'confirmed':
                return 'bg-green-100 text-green-800';
            case 'blocked':
                return 'bg-yellow-100 text-yellow-800';
            case 'pending':
                return 'bg-blue-100 text-blue-800';
            case 'cancelled':
                return 'bg-red-100 text-red-800';
            case 'completed':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    // const getPaymentStatusColor = (status: string) => {
    //     switch (status.toLowerCase()) {
    //         case "paid":
    //             return "bg-green-100 text-green-800";
    //         case "pending":
    //             return "bg-yellow-100 text-yellow-800";
    //         case "unpaid":
    //             return "bg-red-100 text-red-800";
    //         case "cancelled":
    //             return "bg-gray-100 text-gray-800";
    //         case "refunded":
    //             return "bg-blue-100 text-blue-800";
    //         default:
    //             return "bg-gray-100 text-gray-800";
    //     }
    // };

    const columns = useMemo<ColumnDef<GroupReservation>[]>(
        () => [
            {
                accessorKey: 'reservationCode',
                header: 'Booking ID',
                cell: ({ row }) => <div className="font-medium text-gray-900">{row.original.reservationCode}</div>,
            },
            {
                accessorKey: 'createdAt',
                header: 'Booked On',
                cell: ({ row }) => (
                    <div className="text-sm text-gray-500">{formatDate(new Date(row.original.createdAt))}</div>
                ),
            },
            {
                accessorKey: 'reservations',
                header: 'Rooms & Guests',
                cell: ({ row }) => {
                    const totalAdults = row.original.reservations.reduce((sum, res) => sum + res.noOfAdults, 0);
                    const totalChildren = row.original.reservations.reduce((sum, res) => sum + res.noOfChildren, 0);
                    return (
                        <div className="space-y-1">
                            <div className="text-sm font-medium text-gray-900">
                                {row.original.reservations.length} Room
                                {row.original.reservations.length > 1 ? 's' : ''}
                            </div>
                            <div className="text-sm text-gray-500">
                                {totalAdults} Adult{totalAdults > 1 ? 's' : ''}, {totalChildren} Child
                                {totalChildren > 1 ? 'ren' : ''}
                            </div>
                        </div>
                    );
                },
            },
            {
                accessorKey: 'reservations',
                header: 'Check-in/Check-out',
                cell: ({ row }) => {
                    const firstReservation = row.original.reservations[0];
                    const lastReservation = row.original.reservations[row.original.reservations.length - 1];
                    return (
                        <div className="space-y-1">
                            <div className="text-sm">
                                <span className="font-medium text-gray-900">In:</span>{' '}
                                {formatDateTime(new Date(firstReservation.startDateTime))}
                            </div>
                            <div className="text-sm">
                                <span className="font-medium text-gray-900">Out:</span>{' '}
                                {formatDateTime(new Date(lastReservation.endDateTime))}
                            </div>
                        </div>
                    );
                },
            },
            {
                accessorKey: 'reservations',
                header: 'Total Amount',
                cell: ({ row }) => {
                    const totalAmount = row.original.reservations.reduce((sum, res) => sum + res.totalAmount, 0);
                    return (
                        <div className="font-medium text-gray-900">
                            <CurrencyFormat amount={totalAmount} />
                        </div>
                    );
                },
            },
            {
                accessorKey: 'status',
                header: 'Guest Status',
                cell: ({ row }) => (
                    <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                            row.original.status
                        )}`}
                    >
                        {row.original.status.charAt(0).toUpperCase() + row.original.status.slice(1)}
                    </span>
                ),
            },

            {
                id: 'actions',
                header: 'Actions',
                cell: ({ row }) => (
                    <div className="flex items-center space-x-2">
                        <button
                            onClick={() => handleViewDetails(row.original)}
                            className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                        >
                            View
                        </button>
                    </div>
                ),
            },
        ],
        []
    );

    const handleViewDetails = (reservation: GroupReservation) => {
        // TODO: Implement view details modal/page
        setSelectedReservation(reservation);
        setPopupOpen(true);
    };

    // const handleConfirm = (reservation: GroupReservation) => {
    //     // TODO: Implement confirm reservation
    // };

    // const handleCancel = (reservation: GroupReservation) => {
    //     // TODO: Implement cancel reservation
    // };

    if (loading) {
        return <UiLoader label="Loading reservations..." />;
    }

    return (
        <div className="p-4">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">Reservations</h1>
                    <p className="text-gray-600">{selectedProperty.name}</p>
                </div>
                <div className="text-sm text-gray-500">
                    {reservations.length} reservation
                    {reservations.length !== 1 ? 's' : ''}
                </div>
            </div>

            <UiTable
                columns={columns}
                data={reservations}
                enableSorting={true}
                enableFiltering={false}
                enablePagination={true}
                enableGlobalFilter={true}
                defaultPageSize={10}
                className="bg-white rounded-lg shadow"
            />
            <UiPopup
                isOpen={popupOpen}
                onClose={() => setPopupOpen(false)}
                title="Reservation Details"
                width="max-w-4xl"
                height="max-h-[90vh]"
            >
                {selectedReservation ? (
                    <div
                        className="overflow-y-auto px-6 py-6 text-sm text-gray-700"
                        style={{ maxHeight: 'calc(90vh - 64px)', minHeight: '200px' }}
                    >
                        {/* Booking Info */}
                        <div className="mb-6">
                            <h3 className="text-base font-semibold text-gray-900 mb-2">Booking Information</h3>
                            <ul className="space-y-1">
                                <li>
                                    <span className="font-medium">Booking ID:</span>{' '}
                                    {selectedReservation.groupReservationId}
                                </li>
                                <li>
                                    <span className="font-medium">Reservation ID:</span>{' '}
                                    {selectedReservation.reservationCode}
                                </li>
                                <li>
                                    <span className="font-medium">Status:</span> {selectedReservation.status}
                                </li>
                                <li>
                                    <span className="font-medium">Payment Status:</span>{' '}
                                    {selectedReservation.paymentStatus}
                                </li>
                                <li>
                                    <span className="font-medium">Booked On:</span>{' '}
                                    {formatDateTime(new Date(selectedReservation.createdAt))}
                                </li>
                            </ul>
                        </div>

                        {/* Booker Info */}
                        <div className="mb-6">
                            <h3 className="text-base font-semibold text-gray-900 mb-2">Booker Details</h3>
                            <ul className="space-y-1">
                                <li>
                                    <span className="font-medium">Name:</span>{' '}
                                    {selectedReservation.bookerDetails.firstName}{' '}
                                    {selectedReservation.bookerDetails.lastName}
                                </li>
                                <li>
                                    <span className="font-medium">Email:</span>{' '}
                                    {selectedReservation.bookerDetails.email}
                                </li>
                            </ul>
                        </div>

                        {/* Reservation Details */}
                        <div>
                            <h3 className="text-base font-semibold text-gray-900 mb-4">Reservation Details</h3>
                            <div className="space-y-5">
                                {selectedReservation.reservations.map((res, index) => (
                                    <div key={res._id} className="rounded-xl border bg-gray-50 p-4 shadow-sm">
                                        <h4 className="text-sm font-semibold text-indigo-600 mb-2">Room {index + 1}</h4>
                                        <ul className="space-y-1">
                                            <li>
                                                <span className="font-medium">Room Type ID:</span> {res.roomTypeId.name}
                                            </li>
                                            <li>
                                                <span className="font-medium">Package ID:</span> {res.packageId.name}
                                            </li>
                                            <li>
                                                <span className="font-medium">Start:</span>{' '}
                                                {formatDateTime(new Date(res.startDateTime))}
                                            </li>
                                            <li>
                                                <span className="font-medium">End:</span>{' '}
                                                {formatDateTime(new Date(res.endDateTime))}
                                            </li>
                                            <li>
                                                <span className="font-medium">Guests:</span> {res.noOfAdults} Adult
                                                {res.noOfAdults > 1 ? 's' : ''}, {res.noOfChildren} Child
                                                {res.noOfChildren > 1 ? 'ren' : ''}
                                            </li>
                                            <li>
                                                <span className="font-medium">Price:</span>{' '}
                                                <CurrencyFormat amount={res.price} />
                                            </li>
                                            <li>
                                                <span className="font-medium">Total Tax:</span>{' '}
                                                <CurrencyFormat amount={res.tax || 0} />
                                            </li>
                                            <li>
                                                <span className="font-medium">Total Amount:</span>{' '}
                                                <CurrencyFormat amount={res.totalAmount} />
                                            </li>
                                        </ul>

                                        {/* Guest Details */}
                                        {res.guestDetails?.length > 0 && (
                                            <div className="mt-4">
                                                <p className="font-medium text-sm text-gray-800 mb-1">Guest(s):</p>
                                                <ul className="list-disc ml-5 text-sm text-gray-600">
                                                    {res.guestDetails.map(guest => (
                                                        <li key={guest._id}>
                                                            {guest.firstName} {guest.lastName} ({guest.gender}) –{' '}
                                                            {guest.email}
                                                        </li>
                                                    ))}
                                                </ul>
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                ) : (
                    <div className="p-4 text-sm text-gray-500">No reservation selected.</div>
                )}
            </UiPopup>
        </div>
    );
}

export default LiveTracking;
