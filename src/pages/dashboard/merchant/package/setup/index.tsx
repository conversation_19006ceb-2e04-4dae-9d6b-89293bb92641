import { useEffect, useState } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useNavigate, useParams, useSearchParams } from 'react-router';
import { useApiHook } from '../../../../../hooks/useApi.ts';
import { useToastHook } from '../../../../../hooks/useToaster.ts';
import type { IPackage } from '../../../../../interfaces/IPackage.ts';
import type { IDomainValue } from '../../../../../interfaces/IDomainValue.ts';
import type { IRoomType } from '../../../../../interfaces/IRoomType.ts';
import type { ICompany } from '../../../../../interfaces/ICompany.ts';
import { domainValue } from '../../../../../utils/constants/domainValues.ts';
import SectionTitle from '../../../../../components/common/SectionTitle.tsx';
import UiInput from '../../../../../lib/UiInput.tsx';
import UiSelect from '../../../../../lib/UiSelect.tsx';
import { allowOnlyNumbers } from '../../../../../utils/helpers/inputValidation.ts';
import axios from 'axios';

interface RoomType {
    _id: string;
    name: string;
    maxOccupancy: number;
}

const PackagesSetup = () => {
    const [searchParams] = useSearchParams();
    const { propertyId } = useParams();
    const packageId = searchParams.get('packageId');
    const [selectedRoomType, setSelectedRoomType] = useState<RoomType | null>(null);
    const { get, post, put } = useApiHook();
    const { showSuccess, showError } = useToastHook();
    const [packages, setPackages] = useState<IPackage | null>(null);
    const navigate = useNavigate();
    const [allAmenities, setAllAmenities] = useState<IDomainValue[]>([]);
    const [allTaxes, setAllTaxes] = useState<IDomainValue[]>([]);
    const [allMeals, setAllMeals] = useState<IDomainValue[]>([]);
    const [roomTypes, setRoomTypes] = useState<IRoomType[]>([]);
    const [propertyAmenities, setPropertyAmenities] = useState<IDomainValue[]>([]);
    const [stayLimits, setStayLimits] = useState<{ minLimit: string; maxLimit: string } | null>(null);

    const validationSchema = Yup.object({
        name: Yup.string().required('Package name is required'),
        description: Yup.string(),
        roomTypeId: Yup.string().required('Room type is required'),
        duration: Yup.number()
            .required('Duration is required')
            .positive('Must be positive')
            .integer('Must be a whole number')
            .test('min-stay-limit', function (value) {
                const minLimit = stayLimits?.minLimit ? Number(stayLimits.minLimit) : 0;
                if (value < minLimit) {
                    return this.createError({
                        message: `Duration must be at least ${minLimit} hour${minLimit !== 1 ? 's' : ''}`,
                    });
                }
                return true;
            })
            .test('max-stay-limit', function (value) {
                const maxLimit = stayLimits?.maxLimit ? Number(stayLimits.maxLimit) : Infinity;
                if (value > maxLimit) {
                    return this.createError({
                        message: `Duration must not exceed ${maxLimit} hour${maxLimit !== 1 ? 's' : ''}`,
                    });
                }
                return true;
            }),
        price: Yup.number().required('Price is required').positive('Price must be positive'),
        noOfAdults: Yup.number()
            .required('No Of Adult is Required')
            .min(0, 'Must be >= 0')
            .test('max-occupancy-check', function (value) {
                const { noOfChildren } = this.parent;
                const totalGuests = (value || 0) + (noOfChildren || 0);
                const maxOccupancy = selectedRoomType?.maxOccupancy || Infinity;

                if (totalGuests > maxOccupancy) {
                    return this.createError({
                        message: `Total guests must not exceed ${maxOccupancy}`,
                    });
                }

                return true;
            }),

        noOfChildren: Yup.number()
            // .required("No Of Child is Required")
            .min(0, 'Must be >= 0')
            .test('max-occupancy-check', function (value) {
                const { noOfAdults } = this.parent;
                const totalGuests = (value || 0) + (noOfAdults || 0);
                const maxOccupancy = selectedRoomType?.maxOccupancy || Infinity;

                if (totalGuests > maxOccupancy) {
                    return this.createError({
                        message: `Total guests must not exceed ${maxOccupancy}`,
                    });
                }

                return true;
            }),

        taxes: Yup.array().min(1, 'Tax is required'),
        amenities: Yup.array().min(1, 'Amenities are required'),
        customFields: Yup.object({
            extraBed: Yup.object({
                available: Yup.boolean(),
                price: Yup.number().when('customFields.extraBed.available', {
                    is: true,
                    then: schema =>
                        schema
                            .required('Extra bed price is required when available')
                            .positive('Price must be positive'),
                }),
            }),
            checkInInstructions: Yup.object({
                note: Yup.string(),
            }),
        }),
    });

    const fetchPackage = async () => {
        if (packageId) {
            const response = await get<IPackage>(`/properties/${propertyId}/packages/${packageId}`);
            setPackages(response);
        }
    };
    const fetchProperty = async () => {
        const response = await get<ICompany>(`/properties/${propertyId}`);
        setStayLimits({
            minLimit: response.customFields.propertyDetails.staylimit as string,
            maxLimit: response.customFields.propertyDetails.maxstaylimit as string,
        });
        const amenitiesMap = new Map<string, IDomainValue>();
        allAmenities.forEach(amenity => {
            amenitiesMap.set(amenity._id, amenity);
        });
        const customFields = response.customFields as unknown as { amenities?: string[] };
        if (customFields?.amenities) {
            setPropertyAmenities(
                customFields.amenities
                    .map(amenityId => amenitiesMap.get(amenityId))
                    .filter((amenity): amenity is IDomainValue => amenity !== undefined)
            );
        }
    };

    const fetchAmenities = async () => {
        const response = await get<IDomainValue[]>(`/domain-values/categoryId/${domainValue.AmenitiesSubCategory}`);
        setAllAmenities(response);
    };

    const fetchTaxes = async () => {
        const response = await get<IDomainValue[]>(
            `/domain-values/categoryId/${domainValue.PropertyTaxes}/propertyId/${propertyId}`
        );
        setAllTaxes(response);
    };

    const fetchMeals = async () => {
        const response = await get<IDomainValue[]>(
            `/domain-values/categoryId/${domainValue.Meals}/propertyId/${propertyId}`
        );
        setAllMeals(response);
    };

    const fetchRoomTypes = async () => {
        const response = await get<IRoomType[]>(`/properties/${propertyId}/room-types`);
        setRoomTypes(response);
    };

    useEffect(() => {
        fetchProperty();
    }, [allAmenities]);

    useEffect(() => {
        fetchAmenities();
        fetchTaxes();
        fetchMeals();
        fetchRoomTypes();
    }, [propertyId]);

    useEffect(() => {
        if (packageId) {
            fetchPackage();
        }
    }, [packageId]);

    const extraBed = packages?.customFields?.extraBed as { available: boolean; price?: number };
    const checkInInstructions = packages?.customFields?.checkInInstructions as { note?: string };

    const initialValues = {
        name: packages?.name || '',
        description: packages?.description || '',
        roomTypeId: packages?.roomTypeId || '',
        duration: packages?.duration || 1,
        price: packages?.price || '',
        noOfAdults: packages?.noOfAdults || '',
        noOfChildren: packages?.noOfChildren || '',
        taxes: packages?.taxes ? packages.taxes.map((tax: IDomainValue) => tax._id || tax) : [],
        amenities: packages?.amenities ? packages.amenities.map(amenity => amenity._id || amenity) : [],
        customFields: {
            extraBed: {
                available: extraBed?.available || false,
                price: extraBed?.price || '',
            },
            checkInInstructions: {
                note: checkInInstructions?.note || '',
            },
            // @ts-expect-error needs to handle meals properly
            meals: packages?.customFields?.meals ? packages.customFields.meals.map(meal => meal._id || meal) : [],
        },
    };

    const handleSubmit = async (values: typeof initialValues) => {
        const submitData = {
            name: values.name,
            description: values.description,
            roomTypeId: values.roomTypeId,
            duration: values.duration,
            price: values.price,
            noOfAdults: values.noOfAdults,
            noOfChildren: values.noOfChildren === '' ? 0 : values.noOfChildren,
            taxes: values.taxes,
            amenities: values.amenities,
            customFields: {
                extraBed: {
                    available: values.customFields.extraBed.available,
                    price: values.customFields.extraBed.available ? values.customFields.extraBed.price : undefined,
                },
                checkInInstructions: {
                    note: values.customFields.checkInInstructions.note,
                },
                meals: values.customFields.meals,
            },
        };

        if (packageId) {
            try {
                showSuccess('Package updated successfully!');
                await put(`/properties/${propertyId}/packages/${packageId}`, submitData);
                navigate(-1);
            } catch (error: unknown) {
                if (axios.isAxiosError(error)) {
                    const errorMessage = error.response?.data as { error: string };
                    showError(errorMessage?.error || 'An unexpected error occurred');
                } else {
                    showError('An unexpected error occurred');
                }
            }
        } else {
            try {
                await post(`/properties/${propertyId}/packages`, submitData);
                showSuccess('Package created successfully!');
                navigate(-1);
            } catch (error: unknown) {
                if (axios.isAxiosError(error)) {
                    const errorMessage = error.response?.data as { error: string };
                    showError(errorMessage?.error || 'An unexpected error occurred');
                } else {
                    showError('An unexpected error occurred');
                }
            }
        }
    };
    return (
        <div className="mt-2 p-6 bg-white rounded-lg">
            <div className="mb-8">
                <SectionTitle
                    title={packageId ? 'Edit Package' : 'Add New Package'}
                    subtitle="Configure package details, pricing, and amenities"
                />
            </div>

            <Formik
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
                enableReinitialize
            >
                {({ setFieldValue, isSubmitting, values }) => {
                    // eslint-disable-next-line react-hooks/rules-of-hooks
                    useEffect(() => {
                        const selected = roomTypes.find(rt => rt._id === values.roomTypeId);
                        setSelectedRoomType(selected || null);
                    }, [values.roomTypeId]);
                    return (
                        <Form className="space-y-8">
                            {/* Basic Information */}
                            <div className="bg-gray-50 p-6 rounded-lg">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <UiInput
                                        name="name"
                                        label="Package Name"
                                        required
                                        type="text"
                                        placeholder="e.g., Deluxe Weekend Package"
                                    />

                                    <UiSelect
                                        name="roomTypeId"
                                        label="Room Type"
                                        required
                                        options={roomTypes.map(roomType => ({
                                            value: roomType._id,
                                            label: roomType.name,
                                        }))}
                                    />

                                    <UiInput
                                        name="duration"
                                        label="Duration (Hrs)"
                                        required
                                        type="number"
                                        placeholder="Number of days"
                                        onKeyDown={allowOnlyNumbers}
                                    />

                                    <UiInput
                                        name="noOfAdults"
                                        label="No of Adults"
                                        required
                                        type="number"
                                        placeholder="Number of adults"
                                        onKeyDown={allowOnlyNumbers}
                                    />
                                    <UiInput
                                        name="noOfChildren"
                                        label="No of Children"
                                        // required
                                        type="number"
                                        placeholder="Number of children"
                                        onKeyDown={allowOnlyNumbers}
                                    />

                                    <UiInput
                                        name="price"
                                        label="Package Price"
                                        required
                                        type="number"
                                        placeholder="Enter package price"
                                        onKeyDown={allowOnlyNumbers}
                                    />

                                    <UiSelect
                                        name="taxes"
                                        label="Taxes"
                                        required
                                        multiple
                                        options={allTaxes.map(tax => ({
                                            value: tax._id,
                                            label: tax.name,
                                        }))}
                                    />

                                    <UiSelect
                                        name="amenities"
                                        label="Amenities"
                                        required
                                        multiple
                                        options={propertyAmenities.map(amenity => ({
                                            value: amenity._id,
                                            label: amenity.name,
                                        }))}
                                    />

                                    <UiSelect
                                        name="customFields.meals"
                                        label="Meals"
                                        multiple
                                        options={allMeals.map(meal => ({
                                            value: meal._id,
                                            label: meal.name,
                                        }))}
                                    />

                                    <UiInput
                                        name="description"
                                        label="Description"
                                        type="text"
                                        placeholder="Brief description of the package"
                                    />
                                </div>
                            </div>

                            {/* Taxes */}
                            {/* <div className="bg-gray-50 p-6 rounded-lg">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Taxes</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                {allTaxes.map((tax) => (
                                    <div key={tax._id} className="flex items-center">
                                        <input
                                            type="checkbox"
                                            id={tax._id}
                                            name="taxes"
                                            value={tax._id}
                                            checked={values.taxes.includes(tax._id)}
                                            onChange={(e) => {
                                                const taxId = tax._id;
                                                const newValues = e.target.checked
                                                    ? [...values.taxes, taxId]
                                                    : values.taxes.filter((id: string) => id !== taxId);
                                                setFieldValue("taxes", newValues);
                                            }}
                                            className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                        />
                                        <label htmlFor={tax._id} className="ml-2 text-sm text-gray-700">
                                            {tax.name}
                                        </label>
                                    </div>
                                ))}
                            </div>
                            {allTaxes.length === 0 && (
                                <p className="text-sm text-gray-500 italic">No taxes available</p>
                            )}
                        </div> */}

                            {/* Amenities */}
                            {/* <div className="bg-gray-50 p-6 rounded-lg">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Amenities</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                {propertyAmenities.map((amenity) => (
                                    <div key={amenity._id} className="flex items-center">
                                        <input
                                            type="checkbox"
                                            id={amenity._id}
                                            name="amenities"
                                            value={amenity._id}
                                            checked={values.amenities.includes(amenity._id)}
                                            onChange={(e) => {
                                                const amenityId = amenity._id;
                                                const newValues = e.target.checked
                                                    ? [...values.amenities, amenityId]
                                                    : values.amenities.filter((id: string) => id !== amenityId);
                                                setFieldValue("amenities", newValues);
                                            }}
                                            className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                        />
                                        <label htmlFor={amenity._id} className="ml-2 text-sm text-gray-700">
                                            {amenity.name}
                                        </label>
                                    </div>
                                ))}
                            </div>
                            {propertyAmenities.length === 0 && (
                                <p className="text-sm text-gray-500 italic">No amenities available for this property</p>
                            )}
                        </div> */}

                            {/* Custom Fields */}
                            <div className="bg-gray-50 p-6 rounded-lg">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Additional Options</h3>

                                {/* Extra Bed */}
                                <div className="mb-6">
                                    <div className="flex items-center space-x-2 mb-3">
                                        <input
                                            type="checkbox"
                                            id="extraBedAvailable"
                                            name="customFields.extraBed.available"
                                            checked={values.customFields.extraBed.available}
                                            onChange={e =>
                                                setFieldValue('customFields.extraBed.available', e.target.checked)
                                            }
                                            className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                        />
                                        <label
                                            htmlFor="extraBedAvailable"
                                            className="text-sm font-medium text-gray-700"
                                        >
                                            Extra Bed Available
                                        </label>
                                    </div>

                                    {values.customFields.extraBed.available && (
                                        <div className="ml-6">
                                            <UiInput
                                                name="customFields.extraBed.price"
                                                label="Extra Bed Price *"
                                                type="number"
                                                onKeyDown={allowOnlyNumbers}
                                                placeholder="Enter extra bed price"
                                            />
                                        </div>
                                    )}
                                </div>

                                {/* Check-in Instructions */}
                                {/* <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Check-in Instructions
                                    </label>
                                    <textarea
                                        name="customFields.checkInInstructions.note"
                                        value={values.customFields.checkInInstructions.note}
                                        onChange={(e) => setFieldValue("customFields.checkInInstructions.note", e.target.value)}
                                        rows={3}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                        placeholder="Enter check-in instructions or notes"
                                    />
                                </div> */}
                            </div>

                            {/* Submit Button */}
                            <div className="pt-6 border-t border-gray-200 flex flex-col md:flex-row gap-4 justify-between">
                                <button
                                    type="button"
                                    className="w-full md:w-auto px-8 py-3 bg-red-500 text-white font-medium rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                    onClick={() => navigate(-1)}
                                >
                                    Cancel
                                </button>
                                <button
                                    type="submit"
                                    disabled={isSubmitting}
                                    className="w-full md:w-auto px-8 py-3 bg-blue-600 text-white font-medium rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    {isSubmitting ? (
                                        <span className="flex items-center">
                                            <svg
                                                className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                            >
                                                <circle
                                                    className="opacity-25"
                                                    cx="12"
                                                    cy="12"
                                                    r="10"
                                                    stroke="currentColor"
                                                    strokeWidth="4"
                                                ></circle>
                                                <path
                                                    className="opacity-75"
                                                    fill="currentColor"
                                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                                ></path>
                                            </svg>
                                            {packageId ? 'Updating...' : 'Creating...'}
                                        </span>
                                    ) : packageId ? (
                                        'Update Package'
                                    ) : (
                                        'Create Package'
                                    )}
                                </button>
                            </div>
                        </Form>
                    );
                }}
            </Formik>
        </div>
    );
};

export default PackagesSetup;
