import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router';
import { useApiHook } from '../../../../hooks/useApi.ts';
import type { IPackage } from '../../../../interfaces/IPackage.ts';
import type { ColumnDef } from '@tanstack/react-table';
import UiTable from '../../../../lib/UiTable.tsx';
import UiLoader from '../../../../lib/UiLoader.tsx';

function Packages() {
    const navigate = useNavigate();
    const { propertyId } = useParams();
    const { get, delete: deleteApi, loading } = useApiHook();
    const [packages, setPackages] = useState<IPackage[]>([]);

    useEffect(() => {
        const fetchPackages = async () => {
            const response = await get<IPackage[]>(`/properties/${propertyId}/packages`);
            setPackages(response);
        };
        fetchPackages();
    }, [propertyId, get]);

    const handleEdit = (packageId: string) => {
        navigate(`setup?packageId=${packageId}`);
    };

    const handleDelete = async (packageId: string) => {
        if (window.confirm('Are you sure you want to delete this package?')) {
            try {
                await deleteApi(`/packages/${packageId}`);
                const response = await get<IPackage[]>(`/properties/${propertyId}/packages`);
                setPackages(response);
            } catch (error) {
                console.error('Error deleting package:', error);
                alert('Failed to delete package. Please try again.');
            }
        }
    };

    const columns: ColumnDef<IPackage>[] = [
        {
            accessorKey: 'code',
            header: 'Code',
            enableSorting: true,
        },
        {
            accessorKey: 'name',
            header: 'Package Name',
            enableSorting: true,
        },
        // {
        //     accessorKey: 'description',
        //     header: 'Description',
        //     enableSorting: true,
        //     cell: ({ row }: any) => row.original.description || '-',
        // },
        {
            accessorKey: 'roomTypeId',
            header: 'Room Type',
            enableSorting: true,
            cell: ({ row }) => {
                const roomType = row.original.roomTypeId;
                return roomType?.name || roomType || '-';
            },
        },
        {
            accessorKey: 'duration',
            header: 'Duration (Hrs)',
            enableSorting: true,
        },
        {
            accessorKey: 'noOfAdults',
            header: 'Adults',
            enableSorting: true,
        },
        {
            accessorKey: 'noOfChildren',
            header: 'Children',
            enableSorting: true,
        },
        {
            accessorKey: 'price',
            header: 'Price',
            enableSorting: true,
            cell: ({ row }) => `${row.original.price?.toFixed(2) || '0.00'}`,
        },
        {
            accessorKey: 'customFields',
            header: 'Extra Bed',
            enableSorting: false,
            cell: ({ row }) => {
                const customFields = row.original.customFields;
                const available = customFields?.extraBed as { available: boolean; price: number };
                if (available.available as boolean) {
                    return `${available.price}`;
                }
                return '-';
            },
        },
        {
            accessorKey: 'actions',
            header: 'Actions',
            enableSorting: false,
            cell: ({ row }) => {
                const packageId = row.original._id;
                return (
                    <div className="flex gap-2">
                        <button
                            onClick={() => handleEdit(packageId)}
                            className="p-2 text-blue-600 hover:text-blue-800 transition-colors"
                            title="Edit Package"
                        >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                />
                            </svg>
                        </button>
                        <button
                            onClick={() => handleDelete(packageId)}
                            className="p-2 text-red-600 hover:text-red-800 transition-colors"
                            title="Delete Package"
                        >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                />
                            </svg>
                        </button>
                    </div>
                );
            },
        },
    ];

    if (loading) {
        return <UiLoader label={'Loading packages...'} />;
    }

    return (
        <div className="p-6">
            <UiTable
                columns={columns}
                data={packages}
                enableSorting={true}
                enableFiltering={false}
                enablePagination={true}
                defaultPageSize={3}
                pageSizeOptions={[3, 5, 10]}
                className=""
            >
                <div className="flex justify-between items-center w-full">
                    <h2 className="text-2xl font-bold">Packages</h2>
                    <button
                        className="bg-indigo-500 text-white py-2 px-4 rounded cursor-pointer"
                        onClick={() => navigate(`setup`)}
                    >
                        +
                    </button>
                </div>
            </UiTable>
        </div>
    );
}

export default Packages;
