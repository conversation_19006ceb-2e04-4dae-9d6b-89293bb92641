import { useEffect, useState } from 'react';
import { PencilIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';
import { useNavigate, useParams } from 'react-router';
import { useToastHook } from '../../../../hooks/useToaster.ts';
import { useApiHook } from '../../../../hooks/useApi.ts';
import type { IRoomType } from '../../../../interfaces/IRoomType.ts';
import type { ColumnDef } from '@tanstack/react-table';
import ConfirmationDialog from '../../../../components/common/ConfirmationDialog.tsx';
import UiTable from '../../../../lib/UiTable.tsx';
import UiBackButton from '../../../../lib/UiBackButton.tsx';
import UiNextButton from '../../../../lib/UiNextButton.tsx';
import UiLoader from '../../../../lib/UiLoader.tsx';

function Rooms() {
    const { propertyId } = useParams();
    const navigate = useNavigate();
    const { showSuccess } = useToastHook();

    const { loading, get, patch } = useApiHook();
    const [roomTypes, setRoomTypes] = useState<IRoomType[]>([]);
    const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState(false);
    const [selectedRoomType, setSelectedRoomType] = useState<IRoomType | null>(null);
    const isOnboarding = location.pathname.includes('onboarding');

    const fetchRoomTypes = async () => {
        const response = await get<IRoomType[]>(`/properties/${propertyId}/room-types`);
        setRoomTypes(response);
        return response;
    };

    const toggleRoomTypeStatus = async (roomType: IRoomType) => {
        const newStatus = !roomType.active;

        const response = await patch(`/properties/${propertyId}/room-types/${roomType._id}`, { active: newStatus });

        if (response) {
            fetchRoomTypes();
            showSuccess('Room status changed sucessfully!');
        }
    };

    const openConfirmationDialog = (roomType: IRoomType) => {
        setSelectedRoomType(roomType);
        setIsConfirmationDialogOpen(true);
    };

    const handleConfirmAction = async () => {
        if (selectedRoomType) {
            try {
                await toggleRoomTypeStatus(selectedRoomType);
                setIsConfirmationDialogOpen(false);
                setSelectedRoomType(null);
            } catch (error) {
                console.error('Error toggling room type status:', error);
            }
        }
    };

    const handleAddRoomType = () => {
        const totalPrivateRoomTypes = roomTypes.reduce((acc, roomType) => {
            return (roomType.customFields?.bathroomType as string).toLowerCase() === 'private'
                ? acc + roomType.noOfRooms
                : acc;
        }, 0);
        const totalPublicRoomTypes = roomTypes.reduce((acc, roomType) => {
            return (roomType.customFields?.bathroomType as string).toLowerCase() === 'public'
                ? acc + roomType.noOfRooms
                : acc;
        }, 0);
        if (isOnboarding) {
            navigate(
                `/onboarding/${propertyId}/room-types/setup?private=${totalPrivateRoomTypes}&public=${totalPublicRoomTypes}`
            );
        } else {
            navigate(`setup?private=${totalPrivateRoomTypes}&public=${totalPublicRoomTypes}`);
        }
    };
    // Define columns for the table
    const columns: ColumnDef<IRoomType>[] = [
        {
            accessorKey: 'name',
            header: 'Name',
            enableSorting: true,
        },
        {
            accessorKey: 'area',
            header: 'Area of Room',
            enableSorting: true,
        },
        {
            accessorKey: 'maxOccupancy',
            header: 'Max Occupancy',
            enableSorting: true,
        },
        {
            accessorKey: 'bedType',
            header: 'Bed Type',
            enableSorting: true,
        },
        {
            accessorKey: 'customFields.bathroomType',
            header: 'Bathroom Type',
            enableSorting: true,
        },
        {
            accessorKey: 'customFields.isSmokingAllowed',
            header: 'Smoking Allowed',
            enableSorting: true,
            cell: ({ row }) => (row.original.customFields.isSmokingAllowed ? 'Yes' : 'No'),
        },
        {
            accessorKey: 'status',
            header: 'Status',
            enableSorting: false,
            cell: ({ row }) => {
                const roomType: IRoomType = row.original;

                return (
                    <div className="flex space-x-2">
                        <button
                            onClick={() => handleEdit(roomType)}
                            className="text-indigo-600 hover:text-indigo-900"
                            title="Edit"
                        >
                            <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                            onClick={() => openConfirmationDialog(roomType)}
                            className={
                                roomType.active
                                    ? 'text-red-600 hover:text-red-900'
                                    : 'text-green-600 hover:text-green-900'
                            }
                            title={roomType.active ? 'Deactivate' : 'Activate'}
                        >
                            {roomType.active ? (
                                <XCircleIcon className="h-4 w-4" />
                            ) : (
                                <CheckCircleIcon className="h-4 w-4" />
                            )}
                        </button>
                    </div>
                );
            },
        },
    ];

    function handleEdit(roomType: IRoomType) {
        const totalPrivateRoomTypes = roomTypes.reduce((acc, rt) => {
            return (rt.customFields?.bathroomType as string).toLowerCase() === 'private' && rt._id !== roomType._id
                ? acc + rt.noOfRooms
                : acc;
        }, 0);
        const totalPublicRoomTypes = roomTypes.reduce((acc, rt) => {
            return (rt.customFields?.bathroomType as string).toLowerCase() === 'public' && rt._id !== roomType._id
                ? acc + rt.noOfRooms
                : acc;
        }, 0);
        if (isOnboarding) {
            navigate(
                `/onboarding/${propertyId}/room-types/setup?roomTypeId=${roomType._id}&private=${totalPrivateRoomTypes}&public=${totalPublicRoomTypes}`
            );
        } else {
            navigate(
                `setup?roomTypeId=${roomType._id}&private=${totalPrivateRoomTypes}&public=${totalPublicRoomTypes}`
            );
        }
    }

    useEffect(() => {
        fetchRoomTypes();
    }, []);

    const getConfirmationDialogProps = () => {
        if (selectedRoomType) {
            return {
                title: selectedRoomType.active ? 'Deactivate Room Type' : 'Activate Room Type',
                description: `Are you sure you want to ${selectedRoomType.active ? 'deactivate' : 'activate'} "${selectedRoomType.name}"? ${selectedRoomType.active ? 'This will hide the room type from users.' : 'This will make the room type visible to users.'}`,
                confirmText: selectedRoomType.active ? 'Deactivate' : 'Activate',
                cancelText: 'Cancel',
                confirmButtonClassName: selectedRoomType.active
                    ? 'bg-orange-600 hover:bg-orange-700 focus:ring-orange-500'
                    : 'bg-green-600 hover:bg-green-700 focus:ring-green-500',
            };
        }
        return {};
    };

    if (loading) {
        return <UiLoader label="Loading rooms..." />;
    }

    return (
        <>
            <ConfirmationDialog
                isOpen={isConfirmationDialogOpen}
                onClose={() => {
                    setIsConfirmationDialogOpen(false);
                    setSelectedRoomType(null);
                }}
                onConfirm={handleConfirmAction}
                isLoading={loading}
                {...getConfirmationDialogProps()}
            />
            <div className="p-6">
                <div className="flex justify-between items-center w-full mb-4">
                    <h2 className="text-2xl font-bold">Room Types</h2>
                    <button
                        className="bg-indigo-500 text-white py-2 px-4 rounded cursor-pointer"
                        onClick={handleAddRoomType}
                    >
                        +
                    </button>
                </div>
                <UiTable
                    columns={columns}
                    data={roomTypes}
                    enableSorting={true}
                    enableFiltering={false}
                    enablePagination={true}
                    defaultPageSize={3}
                    pageSizeOptions={[3, 5, 10]}
                    className=""
                />

                <div className="flex justify-between mt-6 gap-4">
                    {isOnboarding && (
                        <>
                            <UiBackButton />
                            <UiNextButton label="Next" isFormik={false} to={`/onboarding/${propertyId}/policies`} />
                        </>
                    )}
                </div>
            </div>
        </>
    );
}

export default Rooms;
