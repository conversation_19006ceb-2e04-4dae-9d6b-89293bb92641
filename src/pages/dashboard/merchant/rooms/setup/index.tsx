import { useEffect, useState } from 'react';
import { Formik, Form, Field, ErrorMessage, type FieldProps } from 'formik';
import * as Yup from 'yup';

import { ChevronUpIcon, ChevronDownIcon, InformationCircleIcon } from '@heroicons/react/24/solid';
import { useNavigate, useParams, useSearchParams } from 'react-router';
import { useToastHook } from '../../../../../hooks/useToaster.ts';
import { useApiHook } from '../../../../../hooks/useApi.ts';
import type { IRoomType } from '../../../../../interfaces/IRoomType.ts';
import type { IDomainValue } from '../../../../../interfaces/IDomainValue.ts';
import { domainValue } from '../../../../../utils/constants/domainValues.ts';
import SectionTitle from '../../../../../components/common/SectionTitle.tsx';
import UiInput from '../../../../../lib/UiInput.tsx';
import { allowOnlyAlphabetsAndNumbers } from '../../../../../utils/helpers/inputValidation.ts';
import UiSelect from '../../../../../lib/UiSelect.tsx';
import UiFileUpload from '../../../../../lib/UiFileUpload.tsx';
import UiBackButton from '../../../../../lib/UiBackButton.tsx';
import UiNextButton from '../../../../../lib/UiNextButton.tsx';
import type { ICompany } from '../../../../../interfaces/ICompany.ts';

const RoomSetup = () => {
    const [searchParams] = useSearchParams();
    const { propertyId } = useParams();
    const roomTypeId = searchParams.get('roomTypeId');
    const { error, get, post, put } = useApiHook();
    const { showSuccess, showError } = useToastHook();
    const [roomType, setRoomType] = useState<IRoomType | null>(null);
    const [property, setProperty] = useState<ICompany | null>(null);
    const [isOpen, setIsOpen] = useState<boolean>(true);
    const navigate = useNavigate();
    const isOnboarding = location.pathname.includes('onboarding');
    const totalPrivateRoomTypes = Number(searchParams.get('private')) || 0;
    const totalPublicRoomTypes = Number(searchParams.get('public')) || 0;

    // Memoize the validation schema to update when dependencies change
    const validationSchema = Yup.object({
        code: Yup.string().required('Code is required'),
        name: Yup.string().required('Room type name is required'),
        description: Yup.string().optional(),
        bathroomType: Yup.string().required('Bathroom Type is required'),
        noOfRooms: Yup.number()
            .required('No of Rooms Required')
            .positive('Must be positive')
            .integer('Must be a whole number')
            .test('max-rooms', 'Exceeds available room units', function (value) {
                const bedType = this.parent.bathroomType;
                const isPrivate = bedType?.toLowerCase().includes('private');
                const totalPrivateUnits = Number(property?.customFields.propertyDetails.privateUnits) || 0;
                const totalPublicUnits = Number(property?.customFields.propertyDetails.sharedUnits) || 0;

                if (!value) return true;
                if (isPrivate) {
                    const available = totalPrivateUnits - totalPrivateRoomTypes;
                    return (
                        value <= available ||
                        this.createError({
                            message:
                                available > 0
                                    ? `Only ${available} private rooms available`
                                    : 'No private rooms available',
                        })
                    );
                } else {
                    const available = totalPublicUnits - totalPublicRoomTypes;
                    return (
                        value <= available ||
                        this.createError({
                            message:
                                available > 0
                                    ? `Only ${available} public rooms available`
                                    : 'No public rooms available',
                        })
                    );
                }
            }),
        maxOccupancy: Yup.number()
            .required('Maximum occupancy is required')
            .positive('Must be positive')
            .integer('Must be a whole number'),
        area: Yup.number().required('Area is required').positive('Area must be positive'),
        bedType: Yup.string().required('Bed type is required'),
        allowSmoking: Yup.boolean().required('Smoking preference is required'),
        imageUrls: Yup.array().min(2, 'At least two images are required'),
    });

    const [allBedTypes, setAllBedTypes] = useState<IDomainValue[]>([]);

    const fetchRoomType = async () => {
        if (roomTypeId) {
            const response = await get<IRoomType>(`/properties/${propertyId}/room-types/${roomTypeId}`);
            setRoomType(response);
        }
    };

    const fetchProperty = async () => {
        const response = await get<ICompany>(`/properties/${propertyId}`);
        setProperty(response);
    };

    const fetchBedTypes = async () => {
        const response = await get<IDomainValue[]>(`/domain-values/categoryId/${domainValue.BedTypes}`);
        setAllBedTypes(response);
    };

    useEffect(() => {
        fetchBedTypes();
        fetchProperty();
    }, []);

    useEffect(() => {
        if (roomTypeId) {
            fetchRoomType();
        }
    }, [roomTypeId]);

    const initialValues = {
        code: roomType?.code || '',
        name: roomType?.name || '',
        description: roomType?.description || '',
        noOfRooms: roomType?.noOfRooms || '',
        maxOccupancy: roomType?.maxOccupancy ?? 0,
        area: roomType?.area || '',
        bedType: roomType?.bedType || '',
        allowSmoking: roomType?.customFields?.allowSmoking || false,
        imageUrls: roomType?.imageUrls && roomType.imageUrls.length > 0 ? roomType.imageUrls : [],
        bathroomType: roomType?.customFields?.bathroomType || '',
    };

    const selectBathRoomOptions = [
        { value: 'private', label: 'Private', name: 'Private' },
        { value: 'shared', label: 'Shared', name: 'Shared' },
    ];

    const handleSubmit = async (values: typeof initialValues) => {
        try {
            const submitData = {
                name: values.name,
                description: values.description,
                noOfRooms: values.noOfRooms,
                maxOccupancy: values.maxOccupancy,
                area: values.area,
                bedType: values.bedType,
                imageUrls: values.imageUrls.filter((file: File | string) => !!file),
                customFields: {
                    isSmokingAllowed: values.allowSmoking,
                    code: values.code || '',
                    bathroomType: values.bathroomType,
                },
            };
            if (roomTypeId) {
                await put(`/properties/${propertyId}/room-types/${roomTypeId}`, submitData);
                showSuccess('Room type updated successfully!');
            } else {
                await post(`/properties/${propertyId}/room-types`, submitData);
                showSuccess('Room type created successfully!');
            }
            if (isOnboarding) {
                navigate(`/onboarding/${propertyId}/room-types`);
            } else {
                navigate(-1);
            }
        } catch (err) {
            const e = err as { response?: { data?: { error: string } } };
            showError(error || e.response?.data?.error || 'An error occurred during room type submission');
        }
    };

    return (
        <div className="mt-2 p-6 bg-white rounded-lg">
            <div className="mb-8">
                <SectionTitle
                    title={roomTypeId ? 'Edit Room Type' : 'Add New Room Type'}
                    subtitle="Configure room details, amenities, and specifications"
                />
            </div>

            <Formik
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
                enableReinitialize
            >
                {({ setFieldValue, values }) => (
                    <Form className="space-y-8">
                        <div className="bg-gray-50 p-6 rounded-lg">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Room Specifications</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <UiInput
                                        name="area"
                                        label="Area (sq ft) *"
                                        type="number"
                                        placeholder="Enter room area"
                                    />
                                </div>

                                <div>
                                    <UiSelect
                                        name="bedType"
                                        label="Bed Type *"
                                        options={allBedTypes.map(bedType => ({
                                            value: bedType.name,
                                            label: bedType.name,
                                        }))}
                                    />
                                </div>

                                <div>
                                    <UiSelect
                                        name="bathroomType"
                                        label="Bathroom Type *"
                                        options={selectBathRoomOptions}
                                    />
                                </div>

                                <div>
                                    <UiInput
                                        name="noOfRooms"
                                        label="Number of Rooms *"
                                        type="number"
                                        placeholder="Enter number of rooms"
                                    />
                                </div>
                            </div>

                            <div className="mt-4">
                                <div className="flex items-center space-x-2">
                                    <input
                                        type="checkbox"
                                        id="allowSmoking"
                                        name="allowSmoking"
                                        checked={!!values.allowSmoking}
                                        onChange={e => setFieldValue('allowSmoking', e.target.checked)}
                                        className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                    />
                                    <label htmlFor="allowSmoking" className="text-sm font-medium text-gray-700">
                                        Smoking Allowed in Room
                                    </label>
                                </div>
                                <ErrorMessage name="allowSmoking" component="div" className="text-sm text-red-500" />
                            </div>
                        </div>

                        <div className="bg-gray-50 p-6 rounded-lg">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <UiInput
                                        name="code"
                                        label="Code"
                                        required={true}
                                        type="text"
                                        placeholder="Room Code"
                                        onKeyDown={allowOnlyAlphabetsAndNumbers}
                                    />
                                </div>

                                <div>
                                    <UiInput
                                        name="name"
                                        label="Name *"
                                        type="text"
                                        placeholder="e.g., Deluxe Double, Executive Suite"
                                    />
                                </div>

                                <div>
                                    <UiInput
                                        name="maxOccupancy"
                                        label="Maximum Occupancy *"
                                        type="number"
                                        placeholder="Maximum guests per room"
                                    />
                                </div>

                                <div>
                                    <UiInput
                                        name="description"
                                        label="Description"
                                        type="text"
                                        placeholder="Brief description of the room type"
                                    />
                                </div>
                            </div>
                        </div>

                        <div className="mb-6 border border-gray-300 rounded-sm overflow-hidden">
                            <button
                                onClick={() => setIsOpen(!isOpen)}
                                className="w-full flex justify-between items-center bg-gray-100 px-4 py-3 hover:bg-gray-200 transition"
                            >
                                <div className="flex items-center gap-2 text-left text-gray-800 font-semibold">
                                    <InformationCircleIcon className="h-5 w-5 text-blue-500" />
                                    <span>Photo Upload Guidelines</span>
                                </div>
                                {isOpen ? (
                                    <ChevronUpIcon className="h-5 w-5 text-gray-600" />
                                ) : (
                                    <ChevronDownIcon className="h-5 w-5 text-gray-600" />
                                )}
                            </button>

                            {isOpen && (
                                <div className="bg-gray-50 px-4 py-4 text-sm text-gray-700 space-y-2">
                                    <p>
                                        📸 <strong>Add high quality photos</strong> to increase user interest.
                                    </p>
                                    <p>
                                        💡 The photo size should not exceed <strong>2 MB</strong>.
                                    </p>
                                    <p>
                                        🌞 Always take the picture in <strong>bright natural light</strong>.
                                    </p>
                                    <p>
                                        🏨 Capture well-organized rooms from <strong>various angles</strong>.
                                    </p>
                                    <p>
                                        📷 Upload at least <strong>2 photos per section</strong>.
                                    </p>
                                    <p>
                                        🖼️ Shoot in <strong>landscape mode</strong> to look professional.
                                    </p>
                                    <p>
                                        🚫 <strong>No watermarks</strong> on photos.
                                    </p>
                                </div>
                            )}
                        </div>

                        <div className="bg-gray-50 p-6 rounded-lg">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Room Images</h3>
                            <Field name="imageUrls">
                                {({ field, form }: FieldProps) => (
                                    <>
                                        <UiFileUpload
                                            name={field.name}
                                            value={field.value}
                                            onFileUpload={urls => {
                                                form.setFieldValue(field.name, urls);
                                                form.setFieldTouched(field.name, true);
                                                setTimeout(() => {
                                                    form.validateForm();
                                                }, 0);
                                            }}
                                            allowedTypes="images"
                                            label="Room Images"
                                            multiple
                                        />
                                        <ErrorMessage
                                            name="imageUrls"
                                            component="div"
                                            className="text-sm text-red-500"
                                        />
                                    </>
                                )}
                            </Field>
                        </div>

                        <div className="flex justify-between mt-6 gap-4">
                            <UiBackButton />
                            <UiNextButton label="Submit" />
                        </div>
                    </Form>
                )}
            </Formik>
        </div>
    );
};

export default RoomSetup;
