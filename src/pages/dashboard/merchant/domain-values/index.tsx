'use client';
import { useState } from 'react';
import type { IDomainValue } from '../../../../interfaces/IDomainValue';
import DomainValues from '../../common/domain-values';
import { useParams } from 'react-router';

function MerchantDomainValues() {
    const [selectedDomainValue, setSelectedDomainValue] = useState<IDomainValue | null>(null);

    const { propertyId } = useParams();
    const fetchMetaUrl = '/domain-values?level=PROPERTY_META';
    const fetchDataUrl =
        selectedDomainValue && propertyId
            ? `/domain-values?level=PROPERTY_DATA&propertyId=${propertyId}&categoryId=${selectedDomainValue._id}`
            : '/domain-values?level=PROPERTY_DATA';
    const deleteUrlPrefix = '/domain-values';

    if (!propertyId) {
        return (
            <div className="w-full p-6 bg-white rounded-lg shadow-sm text-center text-gray-500">
                No property selected. Please select a property to manage domain values.
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-100">
            <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <DomainValues
                    propertyId={propertyId}
                    fetchMetaUrl={fetchMetaUrl}
                    fetchDataUrl={fetchDataUrl}
                    deleteUrlPrefix={deleteUrlPrefix}
                    selectedDomainValue={selectedDomainValue}
                    setSelectedDomainValue={setSelectedDomainValue}
                />
            </div>
        </div>
    );
}

export default MerchantDomainValues;
