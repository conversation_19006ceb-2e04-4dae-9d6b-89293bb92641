import { Toaster } from 'react-hot-toast';
import { RouterProvider } from 'react-router';
import { router } from './routes';

function App() {
    return (
        <>
            <Toaster
                position="top-right"
                toastOptions={{
                    duration: 3000,
                    className: 'bg-[var(--white-color)] text-[var(--black-color)]',
                }}
            />
            <RouterProvider router={router} />
        </>
    );
}

export default App;
