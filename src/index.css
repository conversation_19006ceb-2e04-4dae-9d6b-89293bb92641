@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import 'react-datepicker/dist/react-datepicker.css';
@import "tailwindcss";
@import 'swiper/css';
@import 'swiper/css/navigation';
@import 'swiper/css/pagination';


html {
    overflow-x: hidden;
    scroll-behavior: smooth;
}

:root {
    --topbar-height: 64px;
    --sidebar-width: 300px;
    --primary-color: #FC4F4B;
    --primary-color-light: #fee2e2;
    --secondary-color: #C61316;
    --secondary-color-light: #FF715D90;
}

body {
    font-family: 'Poppins', sans-serif;
    margin: 0;
    padding: 0;
}

.sidebar-sm {
    --sidebar-width: 85px;
}

.max-page-height {
    height: calc(100vh - var(--topbar-height));
}

.main-content {
    max-width: 1920px;
    margin: 0 auto;
}

.container-width {
    max-width: 1500px;
    padding: 0 1rem;
}

@media (max-width: 1320px) {
    .container-width {
        max-width: 1200px;
    }
}

.theme-text {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    background-clip: text;
    color: transparent;
}

.theme-background {
    background: linear-gradient(90deg, var(--primary-color));
}

.theme-background-light {
    background: linear-gradient(90deg, var(--primary-color-light), var(--secondary-color-light));
}

.theme-border {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box, linear-gradient(90deg, var(--primary-color), var(--secondary-color)) border-box;
    background-origin: padding-box, border-box;
    background-clip: padding-box, border-box;
}

.theme-border-bottom::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.react-datepicker-wrapper,
.react-tel-input .form-control,
.react-dropdown-select-dropdown {
    width: 100%;
}

.react-dropdown-select-dropdown {
    min-width: 250px;
    border: none !important;
    border-radius: 0;
}

.react-dropdown-select-content {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.react-dropdown-select-item {
    color: black;
    border-bottom: 1px solid var(--primary-color);
}

.select-placeholder-hide .react-dropdown-select-input {
    display: none;
}

.swiper-pagination-bullet-active {
    background: var(--primary-color);
    opacity: 1;
}

.swiper-button-next::after,
.swiper-button-prev::after {
    color: var(--secondary-color);
}

.react-datepicker-popper {
    z-index: 50;
}

.hover\:theme-text:hover {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.hover\:theme-background:hover {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.hover\:theme-background-light:hover {
    background: 90deg, var(--primary-color);
}