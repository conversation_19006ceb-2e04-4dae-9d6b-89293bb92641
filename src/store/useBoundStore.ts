import { create } from 'zustand';
import { createJSONStorage, devtools, persist } from 'zustand/middleware';
import { createCartSlice, type CartSlice } from './slices/cartSlice';
import { createLocationSlice, type LocationSlice } from './slices/locationSlice';
import { createSearchSlice, type SearchSlice } from './slices/searchSlice';
import { createLoggedUserSlice, type LoggedUserSlice } from './slices/loggedUserSlice';
import { createGuestPropertySlice, type GuestPropertySlice } from './slices/guestPropertySlice';
import { createPropertySlice, type PropertySlice } from './slices/propertySlice';
import CryptoJS from 'crypto-js';
import { type AuthPopupSlice, createAuthPopupSlice } from './slices/authPopupSlice';
import { SECRETE_KEY } from '../constants/env.ts';

type BoundState = CartSlice &
    LocationSlice &
    SearchSlice &
    GuestPropertySlice &
    PropertySlice &
    LoggedUserSlice &
    AuthPopupSlice;

// Encryption functions using AES
const encrypt = (text: string): string => CryptoJS.AES.encrypt(text, SECRETE_KEY).toString();
const decrypt = (cipher: string): string => {
    try {
        const bytes = CryptoJS.AES.decrypt(cipher, SECRETE_KEY);
        return bytes.toString(CryptoJS.enc.Utf8);
    } catch (e) {
        console.error('Decryption failed:', e);
        return '';
    }
};

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

// Encrypted storage implementation
const encryptedStorage = {
    getItem: (name: string) => {
        try {
            if (!isBrowser) return null;
            const value = sessionStorage.getItem(name);
            return value ? JSON.parse(decrypt(value)) : null;
        } catch (error) {
            console.error('Error decrypting storage item:', error);
            return null;
        }
    },
    setItem: (name: string, value: unknown) => {
        try {
            if (!isBrowser) return;
            sessionStorage.setItem(name, encrypt(JSON.stringify(value)));
        } catch (error) {
            console.error('Error encrypting storage item:', error);
        }
    },
    removeItem: (name: string) => {
        if (!isBrowser) return;
        sessionStorage.removeItem(name);
    },
};

const useBoundStore = create<BoundState>()(
    devtools(
        persist(
            (...a) => ({
                ...createCartSlice(...a),
                ...createLocationSlice(...a),
                ...createSearchSlice(...a),
                ...createGuestPropertySlice(...a),
                ...createPropertySlice(...a),
                ...createLoggedUserSlice(...a),
                ...createAuthPopupSlice(...a),
            }),
            {
                name: 'zustand-store',
                storage: createJSONStorage(() => encryptedStorage),
                partialize: state => ({
                    // location
                    locations: state.locations,
                    setLocations: state.setLocations,

                    // Cart
                    cart: state.cart,
                    addToCart: state.addToCart,

                    // Search
                    search: state.search,
                    updateSearch: state.updateSearch,

                    // GuestProperty
                    guestSelectedProperty: state.guestSelectedProperty,
                    updateGuestProperty: state.updateGuestProperty,

                    // GuestLocation
                    guestLocation: state.guestLocationDetails,
                    updateGuestLocationDetails: state.updateGuestLocationDetails,

                    // Dashboard Property
                    updateProperty: state.updateProperty,
                    selectedProperty: state.selectedProperty,

                    // Logged User
                    loggedUser: state.loggedUser,
                    updateLoggedUser: state.updateLoggedUser,
                    clearLoggedUser: state.clearLoggedUser,

                    // Auth Popup
                    openLoginPopup: state.openLoginPopup,
                    openRegisterPopup: state.openRegisterPopup,
                    openForgotPasswordPOpup: state.openForgotPasswordPopup,
                    openJoinUsPopup: state.openJoinUsPopup,

                    updateLoginPopup: state.updateLoginPopup,
                    updateRegisterPopup: state.updateRegisterPopup,
                    updateForgotPasswordPOpup: state.updateForgotPasswordPOpup,
                    updateJoinUsPopup: state.updateJoinUsPopup,

                    clearLoginPopup: state.clearLoginPopup,
                    clearRegisterPopup: state.clearRegisterPopup,
                    clearForgotPasswordPOpup: state.clearForgotPasswordPopup,
                    clearJoinUsPopup: state.clearJoinUsPopup,
                }),
            }
        )
    )
);

export default useBoundStore;
