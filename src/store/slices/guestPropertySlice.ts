import type { StateCreator } from 'zustand';
import type { ICompany } from '../../interfaces/ICompany.ts';

export interface GuestPropertySlice {
    guestSelectedProperty: ICompany;
    updateGuestProperty: (property: ICompany | undefined) => void;
    clearProperty: () => void;
}

export const createGuestPropertySlice: StateCreator<GuestPropertySlice, [], [], GuestPropertySlice> = set => ({
    guestSelectedProperty: {} as ICompany,
    updateGuestProperty: property =>
        set(() => ({
            guestSelectedProperty: property,
        })),
    clearProperty: () => set({ guestSelectedProperty: {} as ICompany }),
});
