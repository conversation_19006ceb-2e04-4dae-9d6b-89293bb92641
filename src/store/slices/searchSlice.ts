import type { StateCreator } from 'zustand';
import { SearchFormInitialValues } from '../../utils/constants/SearchFormInitialValues.ts';
import type { ISearchOptions } from '../../components/guest/search-filters/SearchForm.tsx';

export interface SearchSlice {
    search: ISearchOptions;
    updateSearch: (searchOptions: ISearchOptions) => void;
    clearSearch: () => void;
}

export const createSearchSlice: StateCreator<SearchSlice, [], [], SearchSlice> = set => ({
    search: SearchFormInitialValues as ISearchOptions,
    updateSearch: searchOptions =>
        set(() => ({
            search: searchOptions,
        })),
    clearSearch: () => set({ search: SearchFormInitialValues as ISearchOptions }),
});
