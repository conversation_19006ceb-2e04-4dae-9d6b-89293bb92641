import type { StateCreator } from 'zustand';
import type { IGuestLocation, ILocation } from '../../interfaces/ILocation.ts';

export interface LocationSlice {
    locations: ILocation[];
    setLocations: (location: ILocation[]) => void;
    clearLocations: () => void;

    guestLocationDetails: IGuestLocation;
    updateGuestLocationDetails: (location: IGuestLocation | undefined) => void;
}

export const createLocationSlice: StateCreator<LocationSlice, [], [], LocationSlice> = set => ({
    locations: [],
    setLocations: (locations: ILocation[]) =>
        set(() => ({
            locations: locations,
        })),
    clearLocations: () => set({ locations: [] }),

    guestLocationDetails: {} as IGuestLocation,
    updateGuestLocationDetails: location =>
        set(() => ({
            guestLocationDetails: location,
        })),
});
