import type { StateCreator } from 'zustand';

export interface AuthPopupSlice {
    openLoginPopup: boolean;
    openRegisterPopup: boolean;
    openForgotPasswordPopup: boolean;
    openJoinUsPopup: boolean;

    updateLoginPopup: (open: boolean) => void;
    updateRegisterPopup: (open: boolean) => void;
    updateForgotPasswordPOpup: (open: boolean) => void;
    updateJoinUsPopup: (open: boolean) => void;

    clearLoginPopup: () => void;
    clearRegisterPopup: () => void;
    clearForgotPasswordPopup: () => void;
    clearJoinUsPopup: () => void;
}

export const createAuthPopupSlice: StateCreator<AuthPopupSlice, [], [], AuthPopupSlice> = set => ({
    openLoginPopup: false,
    openRegisterPopup: false,
    openForgotPasswordPopup: false,
    openJoinUsPopup: false,

    updateLoginPopup: open => set(() => ({ openLoginPopup: open })),
    updateRegisterPopup: open => set(() => ({ openRegisterPopup: open })),
    updateForgotPasswordPOpup: open => set(() => ({ openForgotPasswordPopup: open })),
    updateJoinUsPopup: open => set(() => ({ openJoinUsPopup: open })),

    clearLoginPopup: () => set(() => ({ openLoginPopup: false })),
    clearRegisterPopup: () => set(() => ({ openRegisterPopup: false })),
    clearForgotPasswordPopup: () => set(() => ({ openForgotPasswordPopup: false })),
    clearJoinUsPopup: () => set(() => ({ openJoinUsPopup: false })),
});
