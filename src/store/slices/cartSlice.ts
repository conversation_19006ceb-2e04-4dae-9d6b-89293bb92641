import type { StateCreator } from 'zustand';
import type { IPackage } from '../../interfaces/IPackage.ts';

export interface CartSlice {
    cart: IPackage[];
    addToCart: (pkg: IPackage) => void;
    removeItem: (id: string) => void;
    clearCart: () => void;
}

export const createCartSlice: StateCreator<CartSlice, [], [], CartSlice> = set => ({
    cart: [],
    addToCart: id =>
        set(state => ({
            cart: [...state.cart, id],
        })),
    removeItem: id =>
        set(state => {
            const index = state.cart.findIndex(item => item._id === id);
            if (index === -1) return state;
            const newCart = [...state.cart];
            newCart.splice(index, 1);
            return { cart: newCart };
        }),
    clearCart: () => set({ cart: [] }),
});
