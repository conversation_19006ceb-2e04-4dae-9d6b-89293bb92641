import type { StateCreator } from 'zustand';
import type { ICompany } from '../../interfaces/ICompany.ts';

export interface PropertySlice {
    selectedProperty: ICompany;
    updateProperty: (property: ICompany | undefined) => void;
    clearProperty: () => void;
}

export const createPropertySlice: StateCreator<PropertySlice, [], [], PropertySlice> = set => ({
    selectedProperty: {} as ICompany,
    updateProperty: property =>
        set(() => ({
            selectedProperty: property,
        })),
    clearProperty: () => set({ selectedProperty: {} as ICompany }),
});
