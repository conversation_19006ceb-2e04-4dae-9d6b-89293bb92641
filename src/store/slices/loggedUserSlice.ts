import type { StateCreator } from 'zustand';
import type { IUser } from '../../interfaces/IUser.ts';

export interface ILoggedUser {
    token: string;
    user: IUser;
}

export interface LoggedUserSlice {
    loggedUser: ILoggedUser;
    updateLoggedUser: (loggedUserOptions: ILoggedUser) => void;
    clearLoggedUser: () => void;
}

export const createLoggedUserSlice: StateCreator<LoggedUserSlice, [], [], LoggedUserSlice> = set => ({
    loggedUser: { token: '', user: {} as IUser },
    updateLoggedUser: loggedUserOptions =>
        set(() => ({
            loggedUser: loggedUserOptions,
        })),
    clearLoggedUser: () => set({ loggedUser: { token: '', user: {} as IUser } }),
});
