import type { IRoomType } from './IRoomType.ts';
import type { IDomainValue } from './IDomainValue.ts';

export interface IAmenity {
    _id: string;
    code: string;
    name: string;
    description: string;
    category: string;
    icon: string;
}

export interface IPackage {
    _id: string;
    name: string;
    description: string;
    duration: number;
    propertyId: string;
    roomTypeId: IRoomType;
    roomType: string;
    price: number;
    rateCardPrice: number;
    taxes: IDomainValue[];
    amenities: IAmenity[];
    customFields: Record<string, unknown>;
    noOfAdults: number;
    noOfChildren: number;
}
