import type { IPhone, IUser } from './IUser.ts';
import type { IAddress } from './IAddress.ts';
import type { IPackage } from './IPackage.ts';

export interface ICompany {
    _id: string;
    active: boolean;
    deleted: boolean;
    name: string;
    premisesType: 'Airside' | 'Landside';
    description: string;
    poc?: IUser;
    phone: IPhone;
    address: IAddress;
    customFields: {
        propertyDetails: Record<string, unknown>;
        [key: string]: unknown;
    };
    packages: IPackage[];
    status: 'Pending' | 'Approved' | 'Rejected';
    logoUrl: string;
    createdAt: Date;
}
