import type { IDomainValue } from './IDomainValue.ts';

export interface IPolicy {
    _id: string;
    name: string;
    order: number;
    title: string;
    description?: string;
    category: IDomainValue;
    input_type: 'text' | 'radio' | 'checkbox' | 'select' | 'custom_rule';
    options?: { value: string; description: string }[];
    is_required: boolean;
    validation_rules?: {
        min?: number;
        max?: number;
        pattern?: string;
        required?: boolean;
    };
    conditional_logic?: {
        field: string;
        value: unknown;
        operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than';
    };
    config?: {
        rules?: Array<{
            refund_percent?: number;
            before_hours?: number;
            after_hours?: number;
            [key: string]: unknown;
        }>;
        [key: string]: unknown;
    };
    propertyId?: string;
    deleted: boolean;
}
