import type { IDomainValue } from './IDomainValue.ts';

export type BusinessDetails = {
    registrationNumber?: string;
    businessTaxId?: string;
    salesTaxId?: string;
    financialYearStart?: string;
    financialYearEnd?: string;
    registrationdoc?: string;
    registrationDocument?: IDomainValue[];
    idType?: string;
    otherIdType?: string;
    idCardNumber?: string;
    idProof?: IDomainValue[];
    bankAccountNumber?: string;
    reEnterBankAccountNumber?: string;
    receivingBankCodeCode?: string;
    bankName?: string;
    commission?: {
        percentage: string;
        frequency: string;
    };
    chargesTypes: IDomainValue[];
    salesTax: IDomainValue[];
};
