import type { ICompany } from './ICompany.ts';
import type { IPhone } from './IUser.ts';

interface TaxDetail {
    name: string;
    percentage: number;
}

interface GuestDetail {
    phone: IPhone;
    firstName: string;
    lastName: string;
    email: string;
    gender: string;
    _id: string;
}
interface RoomTypeId {
    name: string;
    description: string;
    area: string;
    bedType: string;
    bathroomType: string;
    imageUrls: string[];
    noOfRooms: number;
    maxOccupancy: number;
    customFields: { [key: string]: unknown };
}
interface PackageId {
    _id: string;
    name: string;
    customFields: { [key: string]: unknown };
}

interface FlightDetails {
    number: string;
    from: string;
    to: string;
    arrivalDateTime: string;
    departureDateTime: string;
}

export interface Reservation {
    roomTypeId: RoomTypeId;
    packageId: PackageId;
    startDateTime: string; // ISO string
    endDateTime: string; // ISO string
    noOfAdults: number;
    noOfChildren: number;
    flightDetails: FlightDetails;
    specialRequest: string;
    status: string;
    price: number;
    taxes: TaxDetail[];
    tax: number;
    totalAmount: number;
    guestDetails: GuestDetail[];
    _id: string;
}

interface BookerDetails {
    firstName: string;
    lastName: string;
    email: string;
    gender: string;
    phone: IPhone;
}

export interface GroupReservation {
    groupReservationId: string;
    reservationCode: string;
    propertyId: ICompany;
    reservations: Reservation[];
    bookerDetails: BookerDetails;
    status: string;
    paymentStatus: string;
    _id: string;
    createdAt: string; // ISO string
    updatedAt: string; // ISO string
    __v: number;
}

export interface GroupReservationResponse {
    bookingDetails: GroupReservation;
    message: string;
}
