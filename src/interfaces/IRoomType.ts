import type { IAmenity, IPackage } from './IPackage.ts';

export interface IRoomType {
    _id: string;
    name: string;
    description: string;
    noOfRooms: number;
    maxOccupancy: number;
    area: number;
    bedType: string;
    imageUrls: string[];
    bathroomType: string;
    propertyId?: string;
    amenities: IAmenity[];
    packages?: IPackage[];
    customFields: {
        hasBalcony: boolean;
        [key: string]: unknown;
    };
    createdAt?: string;
    updatedAt?: string;
    code?: string;
    __v?: number;
    active: boolean;
}
