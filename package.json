{"name": "staytansit-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --fix", "preview": "vite preview", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "fix": "npm run lint  && npm run format", "prepare": "husky"}, "dependencies": {"@headlessui/react": "^2.2.6", "@heroicons/react": "^2.2.0", "@react-google-maps/api": "^2.20.7", "@stripe/stripe-js": "^7.6.1", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-table": "^8.21.3", "axios": "^1.11.0", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "firebase": "^12.0.0", "formik": "^2.4.6", "react": "^19.1.0", "react-datepicker": "^8.4.0", "react-dom": "^19.1.0", "react-dropdown-select": "^4.12.2", "react-hot-toast": "^2.5.2", "react-router": "^7.7.1", "react-tooltip": "^5.29.1", "swiper": "^11.2.10", "tailwindcss": "^4.1.11", "yup": "^1.6.1", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/crypto-js": "^4.2.2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "husky": "^9.1.7", "prettier": "^3.6.2", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}