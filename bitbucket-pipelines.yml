image: node:22.16.0

pipelines:
  branches:
    # Auto deploy to prod environment
    main:
      - step:
          caches:
            - node
          size: 2x
          script:
            - npm ci
            - export NODE_OPTIONS=--max_old_space_size=7128
            - npm run build
            - cd dist/
            - pipe: atlassian/aws-s3-deploy:2.0.1
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: 'ap-south-1'
                S3_BUCKET: 'staytransitui'
                LOCAL_PATH: $(pwd)
                DELETE_FLAG: 'true'
            - pipe: atlassian/aws-cloudfront-invalidate:0.11.0
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: 'us-east-1'
                DISTRIBUTION_ID: 'E8K1NEOUCDVMA'